import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../core/config/app_config.dart';
import '../core/router/app_router.dart';
import '../core/theme/app_theme.dart';
import '../core/di/dependency_injection.dart';
import '../presentation/layout/app_layout.dart';
import '../data/datasources/websocket_datasource.dart';

/// Main Quester application with clean architecture
class QuesterApp extends StatelessWidget {
  const QuesterApp({super.key});

  @override
  Widget build(BuildContext context) {
    debugPrint('🏗️ Building QuesterApp...');
    return MultiBlocProvider(
      providers: DependencyInjection.getBlocs(),
      child: Builder(
        builder: (context) {
          return MaterialApp.router(
            title: AppConfig.appName,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
            builder: (context, child) {
              return Builder(
                builder: (context) {
                  // Safely initialize WebSocket connection
                  try {
                    debugPrint('🔌 Attempting WebSocket connection...');
                    DependencyInjection.instance.get<WebSocketDataSource>().connect();
                    debugPrint('✅ WebSocket connection initiated');
                  } catch (e) {
                    debugPrint('❌ WebSocket connection failed: $e');
                  }
                  
                  debugPrint('🎯 Building AppLayout...');
                  return _AppErrorBoundary(
                    child: AppLayout(child: child ?? const SizedBox.shrink()),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Error boundary widget to catch and display errors gracefully
class _AppErrorBoundary extends StatefulWidget {
  final Widget child;
  
  const _AppErrorBoundary({required this.child});

  @override
  State<_AppErrorBoundary> createState() => _AppErrorBoundaryState();
}

class _AppErrorBoundaryState extends State<_AppErrorBoundary> {
  bool _hasError = false;
  Object? _error;

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text(
                'Something went wrong',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('Error: ${_error.toString()}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                    _error = null;
                  });
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }

  @override
  void initState() {
    super.initState();
    
    // Wrap the child widget with error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _error = details.exception;
        });
      }
    };
  }
}

