import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../services/enterprise_service.dart';
import '../../models/analytics_data.dart';

// Events
abstract class EnterpriseEvent extends Equatable {
  const EnterpriseEvent();

  @override
  List<Object?> get props => [];
}

class LoadOrganization extends EnterpriseEvent {
  final String organizationId;

  const LoadOrganization({required this.organizationId});

  @override
  List<Object?> get props => [organizationId];
}

class LoadAnalyticsData extends EnterpriseEvent {
  final String organizationId;
  final String timeRange;
  final bool realTime;

  const LoadAnalyticsData({
    required this.organizationId,
    required this.timeRange,
    required this.realTime,
  });

  @override
  List<Object?> get props => [organizationId, timeRange, realTime];
}

class LoadAnalyticsDataEvent extends EnterpriseEvent {
  final String timeRange;

  const LoadAnalyticsDataEvent({required this.timeRange});

  @override
  List<Object?> get props => [timeRange];
}

class LoadMembers extends EnterpriseEvent {
  final String organizationId;

  const LoadMembers({required this.organizationId});

  @override
  List<Object?> get props => [organizationId];
}

class AddMember extends EnterpriseEvent {
  final String organizationId;
  final String email;
  final String roleId;

  const AddMember({
    required this.organizationId,
    required this.email,
    required this.roleId,
  });

  @override
  List<Object?> get props => [organizationId, email, roleId];
}

class UpdateMember extends EnterpriseEvent {
  final String organizationId;
  final String memberId;
  final Map<String, dynamic> updates;

  const UpdateMember({
    required this.organizationId,
    required this.memberId,
    required this.updates,
  });

  @override
  List<Object?> get props => [organizationId, memberId, updates];
}

class LoadRoles extends EnterpriseEvent {
  final String organizationId;

  const LoadRoles({required this.organizationId});

  @override
  List<Object?> get props => [organizationId];
}

class CreateRole extends EnterpriseEvent {
  final String organizationId;
  final String name;
  final List<String> permissions;
  final String? description;

  const CreateRole({
    required this.organizationId,
    required this.name,
    required this.permissions,
    this.description,
  });

  @override
  List<Object?> get props => [organizationId, name, permissions, description];
}

// States
abstract class EnterpriseState extends Equatable {
  const EnterpriseState();

  @override
  List<Object?> get props => [];
}

class EnterpriseInitial extends EnterpriseState {}

class EnterpriseLoading extends EnterpriseState {}

class AnalyticsDataLoading extends EnterpriseState {}

class AnalyticsDataError extends EnterpriseState {
  final String message;

  const AnalyticsDataError({required this.message});

  @override
  List<Object?> get props => [message];
}

class EnterpriseError extends EnterpriseState {
  final String message;

  const EnterpriseError({required this.message});

  @override
  List<Object?> get props => [message];
}

class OrganizationLoaded extends EnterpriseState {
  final Map<String, dynamic> organization;

  const OrganizationLoaded({required this.organization});

  @override
  List<Object?> get props => [organization];
}

class AnalyticsDataLoaded extends EnterpriseState {
  final AnalyticsData data;

  const AnalyticsDataLoaded({required this.data});

  @override
  List<Object?> get props => [data];
}

class MembersLoaded extends EnterpriseState {
  final List<Map<String, dynamic>> members;

  const MembersLoaded({required this.members});

  @override
  List<Object?> get props => [members];
}

class MemberAdded extends EnterpriseState {
  final Map<String, dynamic> member;

  const MemberAdded({required this.member});

  @override
  List<Object?> get props => [member];
}

class MemberUpdated extends EnterpriseState {
  final Map<String, dynamic> member;

  const MemberUpdated({required this.member});

  @override
  List<Object?> get props => [member];
}

class RolesLoaded extends EnterpriseState {
  final List<Map<String, dynamic>> roles;

  const RolesLoaded({required this.roles});

  @override
  List<Object?> get props => [roles];
}

class RoleCreated extends EnterpriseState {
  final Map<String, dynamic> role;

  const RoleCreated({required this.role});

  @override
  List<Object?> get props => [role];
}

// BLoC
class EnterpriseBloc extends Bloc<EnterpriseEvent, EnterpriseState> {
  final EnterpriseService _enterpriseService;

  EnterpriseBloc({required EnterpriseService enterpriseService})
      : _enterpriseService = enterpriseService,
        super(EnterpriseInitial()) {
    on<LoadOrganization>(_onLoadOrganization);
    on<LoadAnalyticsData>(_onLoadAnalyticsData);
    on<LoadAnalyticsDataEvent>(_onLoadAnalyticsDataEvent);
    on<LoadMembers>(_onLoadMembers);
    on<AddMember>(_onAddMember);
    on<UpdateMember>(_onUpdateMember);
    on<LoadRoles>(_onLoadRoles);
    on<CreateRole>(_onCreateRole);
  }

  Future<void> _onLoadOrganization(
    LoadOrganization event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final organization = await _enterpriseService.getOrganization(event.organizationId);
      emit(OrganizationLoaded(organization: organization));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onLoadAnalyticsData(
    LoadAnalyticsData event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final analyticsData = await _enterpriseService.getAnalytics(
        event.organizationId,
        timeRange: event.timeRange,
        metrics: [
          'userEngagement',
          'questCompletion',
          'achievementUnlocks',
          'teamPerformance',
          'activityTrends',
          'performanceMetrics',
          'topPerformers',
          'engagementHeatmap',
          'dailyActiveUsers',
          'avgSessionDuration',
          'retentionRate',
          'churnRate',
          'recentActivities',
        ],
      );
      final data = AnalyticsData.fromJson(analyticsData);
      emit(AnalyticsDataLoaded(data: data));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onLoadAnalyticsDataEvent(
    LoadAnalyticsDataEvent event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(AnalyticsDataLoading());
    try {
      // For demo purposes, create mock analytics data
      final analyticsData = {
        'activeUsers': 245,
        'tasksCompleted': 1834,
        'totalPoints': 48750,
        'totalTeams': 12,
        'engagementRate': 0.78,
        'completionRate': 0.82,
        'averagePointsPerUser': 198.5,
        'engagementData': [
          {'label': 'Mon', 'value': 150.0, 'timestamp': DateTime.now().subtract(Duration(days: 6)).toIso8601String()},
          {'label': 'Tue', 'value': 180.0, 'timestamp': DateTime.now().subtract(Duration(days: 5)).toIso8601String()},
          {'label': 'Wed', 'value': 165.0, 'timestamp': DateTime.now().subtract(Duration(days: 4)).toIso8601String()},
          {'label': 'Thu', 'value': 220.0, 'timestamp': DateTime.now().subtract(Duration(days: 3)).toIso8601String()},
          {'label': 'Fri', 'value': 195.0, 'timestamp': DateTime.now().subtract(Duration(days: 2)).toIso8601String()},
          {'label': 'Sat', 'value': 140.0, 'timestamp': DateTime.now().subtract(Duration(days: 1)).toIso8601String()},
          {'label': 'Sun', 'value': 120.0, 'timestamp': DateTime.now().toIso8601String()},
        ],
        'pointsDistribution': [
          {'label': 'Tasks', 'value': 35.0},
          {'label': 'Achievements', 'value': 25.0},
          {'label': 'Collaboration', 'value': 20.0},
          {'label': 'Learning', 'value': 15.0},
          {'label': 'Other', 'value': 5.0},
        ],
        'taskCompletionData': [
          {'label': 'Week 1', 'value': 180.0},
          {'label': 'Week 2', 'value': 220.0},
          {'label': 'Week 3', 'value': 195.0},
          {'label': 'Week 4', 'value': 240.0},
        ],
        'activityHeatmap': [
          {'label': 'Mon-9AM', 'value': 15.0},
          {'label': 'Mon-2PM', 'value': 25.0},
          {'label': 'Tue-9AM', 'value': 20.0},
          {'label': 'Tue-2PM', 'value': 30.0},
        ],
        'teamPerformanceData': [
          {'label': 'Productivity', 'value': 85.0},
          {'label': 'Quality', 'value': 78.0},
          {'label': 'Collaboration', 'value': 92.0},
          {'label': 'Innovation', 'value': 71.0},
        ],
        'teamComparisonData': [
          {'label': 'Alpha Team', 'value': 850.0},
          {'label': 'Beta Team', 'value': 720.0},
          {'label': 'Gamma Team', 'value': 680.0},
          {'label': 'Delta Team', 'value': 590.0},
        ],
        'teamMetrics': [
          {
            'id': 'team-1',
            'name': 'Alpha Team',
            'memberCount': 8,
            'totalPoints': 8500,
            'completionRate': 85.5,
            'avgPointsPerTask': 45.2,
            'activeStreak': 12,
            'color': 0xFF2196F3,
          },
          {
            'id': 'team-2',
            'name': 'Beta Team',
            'memberCount': 6,
            'totalPoints': 7200,
            'completionRate': 78.3,
            'avgPointsPerTask': 42.1,
            'activeStreak': 8,
            'color': 0xFF4CAF50,
          },
          {
            'id': 'team-3',
            'name': 'Gamma Team',
            'memberCount': 7,
            'totalPoints': 6800,
            'completionRate': 72.1,
            'avgPointsPerTask': 38.9,
            'activeStreak': 5,
            'color': 0xFFFF9800,
          },
        ],
        'recentActivities': [
          {
            'id': 'activity-1',
            'type': 'task_completed',
            'title': 'Task Completed',
            'description': 'John completed "Implement user authentication"',
            'userId': 'user-1',
            'userName': 'John Doe',
            'timestamp': DateTime.now().subtract(Duration(minutes: 15)).toIso8601String(),
          },
          {
            'id': 'activity-2',
            'type': 'achievement_earned',
            'title': 'Achievement Earned',
            'description': 'Sarah earned "Team Player" achievement',
            'userId': 'user-2',
            'userName': 'Sarah Smith',
            'timestamp': DateTime.now().subtract(Duration(hours: 2)).toIso8601String(),
          },
          {
            'id': 'activity-3',
            'type': 'milestone_reached',
            'title': 'Milestone Reached',
            'description': 'Alpha Team reached 1000 points milestone',
            'userId': 'team-1',
            'userName': 'Alpha Team',
            'timestamp': DateTime.now().subtract(Duration(hours: 4)).toIso8601String(),
          },
        ],
      };
      
      final data = AnalyticsData.fromJson(analyticsData);
      emit(AnalyticsDataLoaded(data: data));
    } catch (e) {
      emit(AnalyticsDataError(message: e.toString()));
    }
  }

  Future<void> _onLoadMembers(
    LoadMembers event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final members = await _enterpriseService.getMembers(event.organizationId);
      emit(MembersLoaded(members: members));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onAddMember(
    AddMember event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final member = await _enterpriseService.addMember(
        event.organizationId,
        email: event.email,
        roleId: event.roleId,
      );
      emit(MemberAdded(member: member));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onUpdateMember(
    UpdateMember event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final member = await _enterpriseService.updateMember(
        event.organizationId,
        event.memberId,
        event.updates,
      );
      emit(MemberUpdated(member: member));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onLoadRoles(
    LoadRoles event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final roles = await _enterpriseService.getRoles(event.organizationId);
      emit(RolesLoaded(roles: roles));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  Future<void> _onCreateRole(
    CreateRole event,
    Emitter<EnterpriseState> emit,
  ) async {
    emit(EnterpriseLoading());
    try {
      final role = await _enterpriseService.createRole(
        event.organizationId,
        name: event.name,
        permissions: event.permissions,
        description: event.description,
      );
      emit(RoleCreated(role: role));
    } catch (e) {
      emit(EnterpriseError(message: e.toString()));
    }
  }

  @override
  Future<void> close() {
    _enterpriseService.dispose();
    return super.close();
  }
}
