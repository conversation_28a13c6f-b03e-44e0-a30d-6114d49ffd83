import 'dart:async';
import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Events for Real-time BLoC
abstract class RealtimeEvent extends Equatable {
  const RealtimeEvent();

  @override
  List<Object?> get props => [];
}

class ConnectWebSocket extends RealtimeEvent {
  final String serverUrl;
  final String? userId;

  const ConnectWebSocket(this.serverUrl, {this.userId});

  @override
  List<Object?> get props => [serverUrl, userId];
}

class DisconnectWebSocket extends RealtimeEvent {}

class SubscribeToEvents extends RealtimeEvent {
  final List<String> subscriptionTypes;

  const SubscribeToEvents(this.subscriptionTypes);

  @override
  List<Object?> get props => [subscriptionTypes];
}

class UnsubscribeFromEvents extends RealtimeEvent {
  final List<String> subscriptionTypes;

  const UnsubscribeFromEvents(this.subscriptionTypes);

  @override
  List<Object?> get props => [subscriptionTypes];
}

class SendWebSocketMessage extends RealtimeEvent {
  final Map<String, dynamic> message;

  const SendWebSocketMessage(this.message);

  @override
  List<Object?> get props => [message];
}

class WebSocketMessageReceived extends RealtimeEvent {
  final Map<String, dynamic> message;

  const WebSocketMessageReceived(this.message);

  @override
  List<Object?> get props => [message];
}

class ConnectionLost extends RealtimeEvent {}

class ReconnectAttempt extends RealtimeEvent {}

class RealtimeErrorEvent extends RealtimeEvent {
  final String message;

  const RealtimeErrorEvent(this.message);

  @override
  List<Object?> get props => [message];
}

/// States for Real-time BLoC
abstract class RealtimeState extends Equatable {
  const RealtimeState();

  @override
  List<Object?> get props => [];
}

class RealtimeInitial extends RealtimeState {}

class RealtimeConnecting extends RealtimeState {}

class RealtimeConnected extends RealtimeState {
  final String connectionId;
  final List<String> subscriptions;
  final DateTime connectedAt;

  const RealtimeConnected({
    required this.connectionId,
    required this.subscriptions,
    required this.connectedAt,
  });

  @override
  List<Object?> get props => [connectionId, subscriptions, connectedAt];

  RealtimeConnected copyWith({
    String? connectionId,
    List<String>? subscriptions,
    DateTime? connectedAt,
  }) {
    return RealtimeConnected(
      connectionId: connectionId ?? this.connectionId,
      subscriptions: subscriptions ?? this.subscriptions,
      connectedAt: connectedAt ?? this.connectedAt,
    );
  }
}

class RealtimeDisconnected extends RealtimeState {
  final String? reason;

  const RealtimeDisconnected({this.reason});

  @override
  List<Object?> get props => [reason];
}

class RealtimeError extends RealtimeState {
  final String message;

  const RealtimeError(this.message);

  @override
  List<Object?> get props => [message];
}

class RealtimeReconnecting extends RealtimeState {
  final int attemptNumber;

  const RealtimeReconnecting(this.attemptNumber);

  @override
  List<Object?> get props => [attemptNumber];
}

/// Real-time BLoC for WebSocket communication
class RealtimeBloc extends Bloc<RealtimeEvent, RealtimeState> {
  WebSocketChannel? _channel;
  StreamSubscription? _messageSubscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  String? _serverUrl;
  String? _userId;
  final List<String> _activeSubscriptions = [];
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectInterval = Duration(seconds: 5);
  static const Duration heartbeatInterval = Duration(seconds: 30);

  // Real-time events stream for other BLoCs to listen to
  final StreamController<WebSocketEvent> _eventsController = StreamController<WebSocketEvent>.broadcast();
  Stream<WebSocketEvent> get eventsStream => _eventsController.stream;

  // User presence management
  final StreamController<UserPresence> _presenceController = StreamController<UserPresence>.broadcast();
  Stream<UserPresence> get presenceStream => _presenceController.stream;

  // Collaboration sessions stream
  final StreamController<CollaborationSession> _collaborationController = StreamController<CollaborationSession>.broadcast();
  Stream<CollaborationSession> get collaborationStream => _collaborationController.stream;

  RealtimeBloc() : super(RealtimeInitial()) {
    on<ConnectWebSocket>(_onConnectWebSocket);
    on<DisconnectWebSocket>(_onDisconnectWebSocket);
    on<SubscribeToEvents>(_onSubscribeToEvents);
    on<UnsubscribeFromEvents>(_onUnsubscribeFromEvents);
    on<SendWebSocketMessage>(_onSendWebSocketMessage);
    on<WebSocketMessageReceived>(_onWebSocketMessageReceived);
    on<ConnectionLost>(_onConnectionLost);
    on<ReconnectAttempt>(_onReconnectAttempt);
    on<RealtimeErrorEvent>(_onRealtimeError);
  }

  Future<void> _onConnectWebSocket(
    ConnectWebSocket event,
    Emitter<RealtimeState> emit,
  ) async {
    emit(RealtimeConnecting());

    try {
      _serverUrl = event.serverUrl;
      _userId = event.userId;
      
      final uri = Uri.parse(event.serverUrl);
      _channel = WebSocketChannel.connect(uri);
      
      // Listen for messages
      _messageSubscription = _channel!.stream.listen(
        (message) {
          try {
            final data = jsonDecode(message) as Map<String, dynamic>;
            add(WebSocketMessageReceived(data));
          } catch (e) {
            add(const RealtimeErrorEvent('Failed to parse WebSocket message'));
          }
        },
        onError: (error) {
          add(ConnectionLost());
        },
        onDone: () {
          add(ConnectionLost());
        },
      );

      // Start heartbeat timer
      _startHeartbeat();

      // Reset reconnection attempts on successful connection
      _reconnectAttempts = 0;

    } catch (e) {
      emit(RealtimeError('Failed to connect: $e'));
    }
  }

  Future<void> _onDisconnectWebSocket(
    DisconnectWebSocket event,
    Emitter<RealtimeState> emit,
  ) async {
    await _cleanup();
    emit(const RealtimeDisconnected(reason: 'User requested disconnect'));
  }

  Future<void> _onSubscribeToEvents(
    SubscribeToEvents event,
    Emitter<RealtimeState> emit,
  ) async {
    if (_channel == null) return;

    for (final subscription in event.subscriptionTypes) {
      _sendMessage({
        'type': 'subscribe',
        'subscription': subscription,
        'user_id': _userId,
      });
      
      if (!_activeSubscriptions.contains(subscription)) {
        _activeSubscriptions.add(subscription);
      }
    }

    if (state is RealtimeConnected) {
      final currentState = state as RealtimeConnected;
      emit(currentState.copyWith(subscriptions: List.from(_activeSubscriptions)));
    }
  }

  Future<void> _onUnsubscribeFromEvents(
    UnsubscribeFromEvents event,
    Emitter<RealtimeState> emit,
  ) async {
    if (_channel == null) return;

    for (final subscription in event.subscriptionTypes) {
      _sendMessage({
        'type': 'unsubscribe',
        'subscription': subscription,
      });
      
      _activeSubscriptions.remove(subscription);
    }

    if (state is RealtimeConnected) {
      final currentState = state as RealtimeConnected;
      emit(currentState.copyWith(subscriptions: List.from(_activeSubscriptions)));
    }
  }

  Future<void> _onSendWebSocketMessage(
    SendWebSocketMessage event,
    Emitter<RealtimeState> emit,
  ) async {
    _sendMessage(event.message);
  }

  Future<void> _onWebSocketMessageReceived(
    WebSocketMessageReceived event,
    Emitter<RealtimeState> emit,
  ) async {
    final message = event.message;
    final messageType = message['type'] as String?;

    switch (messageType) {
      case 'welcome':
        final connectionId = message['connection_id'] as String;
        emit(RealtimeConnected(
          connectionId: connectionId,
          subscriptions: List.from(_activeSubscriptions),
          connectedAt: DateTime.now(),
        ));
        
        // Send user authentication if userId is available
        if (_userId != null) {
          _sendMessage({
            'type': 'user_auth',
            'user_id': _userId,
          });
        }
        break;

      case 'subscription_confirmed':
        // Handle subscription confirmation
        break;

      case 'error':
        emit(RealtimeError(message['message'] as String? ?? 'WebSocket error'));
        break;

      case 'websocket_event':
        _handleWebSocketEvent(message);
        break;

      case 'user_presence':
        _handleUserPresence(message);
        break;

      case 'collaboration_update':
        _handleCollaborationUpdate(message);
        break;

      case 'pong':
        // Heartbeat response - connection is alive
        break;
    }
  }

  Future<void> _onConnectionLost(
    ConnectionLost event,
    Emitter<RealtimeState> emit,
  ) async {
    emit(const RealtimeDisconnected(reason: 'Connection lost'));
    
    // Attempt reconnection if not at max attempts
    if (_reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  Future<void> _onRealtimeError(
    RealtimeErrorEvent event,
    Emitter<RealtimeState> emit,
  ) async {
    emit(RealtimeError(event.message));
  }

  Future<void> _onReconnectAttempt(
    ReconnectAttempt event,
    Emitter<RealtimeState> emit,
  ) async {
    _reconnectAttempts++;
    emit(RealtimeReconnecting(_reconnectAttempts));

    if (_serverUrl != null) {
      add(ConnectWebSocket(_serverUrl!, userId: _userId));
    }
  }

  void _sendMessage(Map<String, dynamic> message) {
    if (_channel != null) {
      try {
        _channel!.sink.add(jsonEncode(message));
      } catch (e) {
        add(const RealtimeErrorEvent('Failed to send message'));
      }
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      _sendMessage({
        'type': 'ping',
        'timestamp': DateTime.now().toIso8601String(),
      });
    });
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectInterval, () {
      add(ReconnectAttempt());
    });
  }

  void _handleWebSocketEvent(Map<String, dynamic> data) {
    try {
      if (data['event'] != null) {
        final event = WebSocketEvent.fromJson(data['event'] as Map<String, dynamic>);
        _eventsController.add(event);
      }
    } catch (e) {
      // Handle parsing error
    }
  }

  void _handleUserPresence(Map<String, dynamic> data) {
    try {
      if (data['presence'] != null) {
        final presence = UserPresence.fromJson(data['presence'] as Map<String, dynamic>);
        _presenceController.add(presence);
      }
    } catch (e) {
      // Handle parsing error
    }
  }

  void _handleCollaborationUpdate(Map<String, dynamic> data) {
    try {
      if (data['session'] != null) {
        final session = CollaborationSession.fromJson(data['session'] as Map<String, dynamic>);
        _collaborationController.add(session);
      }
    } catch (e) {
      // Handle parsing error
    }
  }

  Future<void> _cleanup() async {
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    _messageSubscription?.cancel();
    
    try {
      await _channel?.sink.close();
    } catch (e) {
      // Ignore close errors
    }
    
    _channel = null;
    _activeSubscriptions.clear();
  }

  // Public methods for external use

  /// Send a user presence update
  void updateUserPresence(UserPresence presence) {
    final event = WebSocketEvent.userPresence(
      userId: presence.userId,
      status: presence.status.toString().split('.').last,
      additionalData: {
        'activity': presence.activity?.toString().split('.').last,
        'activity_context': presence.activityContext,
        'status_message': presence.statusMessage,
      },
    );

    _sendMessage({
      'type': 'websocket_event',
      'event': event.toJson(),
    });
  }

  /// Send a collaboration update
  void sendCollaborationUpdate(String sessionId, Map<String, dynamic> changes) {
    final event = WebSocketEvent.liveEditing(
      documentId: sessionId,
      userId: _userId ?? 'anonymous',
      operation: 'update',
      changes: changes,
    );

    _sendMessage({
      'type': 'websocket_event',
      'event': event.toJson(),
    });
  }

  /// Send a quest update notification
  void sendQuestUpdate(String questId, String action, Map<String, dynamic> questData) {
    final event = WebSocketEvent.questUpdate(
      questId: questId,
      userId: _userId ?? 'anonymous',
      action: action,
      questData: questData,
    );

    _sendMessage({
      'type': 'websocket_event',
      'event': event.toJson(),
    });
  }

  @override
  Future<void> close() async {
    await _cleanup();
    await _eventsController.close();
    await _presenceController.close();
    await _collaborationController.close();
    return super.close();
  }
}
