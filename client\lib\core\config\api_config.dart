/// API Configuration for the Quester client application
class ApiConfig {
  // Server configuration
  static const String baseUrl = 'http://localhost:8080/api';
  static const String wsUrl = 'ws://localhost:8080/ws';
  
  // Authentication endpoints
  static const String authBaseUrl = 'http://localhost:8080/auth';
  
  // Gamification endpoints
  static const String gamificationBaseUrl = 'http://localhost:8080/gamification';
  
  // Enterprise endpoints
  static const String enterpriseBaseUrl = 'http://localhost:8080/enterprise';
  
  // Request timeouts
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 10);
  
  // API versioning
  static const String apiVersion = 'v1';
  
  // Environment-specific configuration
  static const bool isProduction = false;
  static const bool enableLogging = true;
  
  // Rate limiting
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  
  /// Get the full API URL for a given endpoint
  static String getApiUrl(String endpoint) {
    return '$baseUrl/$endpoint';
  }
  
  /// Get the authentication URL for a given endpoint
  static String getAuthUrl(String endpoint) {
    return '$authBaseUrl/$endpoint';
  }
  
  /// Get the gamification URL for a given endpoint
  static String getGamificationUrl(String endpoint) {
    return '$gamificationBaseUrl/$endpoint';
  }
  
  /// Get the enterprise URL for a given endpoint
  static String getEnterpriseUrl(String endpoint) {
    return '$enterpriseBaseUrl/$endpoint';
  }
  
  /// Get WebSocket URL for real-time features
  static String getWebSocketUrl([String? path]) {
    return path != null ? '$wsUrl/$path' : wsUrl;
  }
}
