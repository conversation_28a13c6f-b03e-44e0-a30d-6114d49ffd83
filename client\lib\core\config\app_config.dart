/// Application configuration constants
class AppConfig {
  static const String appName = 'Quester';
  static const String version = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:8080';
  static const String websocketUrl = 'ws://localhost:8080/ws';
  static const String apiVersion = 'v1';
  
  // Breakpoints for responsive design
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 1024.0;
  static const double desktopBreakpoint = 1440.0;
  static const double largeDesktopBreakpoint = 1920.0;
  
  // Navigation Configuration
  static const double bottomNavHeight = 80.0;
  static const double sideNavWidth = 280.0;
  static const double sideNavWidthCollapsed = 72.0;
  static const double appBarHeight = 64.0;
  
  // Animation Durations
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
  
  // Assets
  static const String logoPath = 'assets/images/logo.png';
  static const String logoIconPath = 'assets/images/logo_icon.png';
}