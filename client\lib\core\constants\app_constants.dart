/// Application-wide constants
class AppConstants {
  // Navigation Routes
  static const String dashboardRoute = '/';
  static const String questsRoute = '/quests';
  static const String gamificationRoute = '/gamification';
  static const String analyticsRoute = '/analytics';
  static const String enterpriseRoute = '/enterprise';
  static const String freelancingRoute = '/freelancing';
  static const String learningRoute = '/learning';
  static const String notificationsRoute = '/notifications';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
  
  // Authentication Routes
  static const String loginRoute = '/auth/login';
  static const String registerRoute = '/auth/register';
  static const String forgotPasswordRoute = '/auth/forgot-password';
  static const String twoFactorRoute = '/auth/two-factor';
  
  // API Endpoints
  static const String gamificationApi = '/api/v1/gamification';
  static const String analyticsApi = '/api/v1/analytics';
  static const String enterpriseApi = '/api/v1/enterprise';
  static const String authApi = '/api/v1/auth';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  // WebSocket Events
  static const String userActivityEvent = 'user_activity';
  static const String notificationEvent = 'notification';
  static const String pointsUpdatedEvent = 'points_updated';
  static const String achievementUnlockedEvent = 'achievement_unlocked';
  static const String leaderboardUpdatedEvent = 'leaderboard_updated';
  
  // Error Messages
  static const String networkErrorMessage = 'Network connection failed';
  static const String generalErrorMessage = 'Something went wrong';
  static const String authErrorMessage = 'Authentication failed';
  
  // Success Messages
  static const String pointsAwardedMessage = 'Points awarded successfully!';
  static const String achievementUnlockedMessage = 'New achievement unlocked!';
  static const String questCompletedMessage = 'Quest completed!';
}