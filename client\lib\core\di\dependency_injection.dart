import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;

import '../utils/logger.dart';
import '../../data/datasources/websocket_datasource.dart';
import '../../data/repositories/notification_repository.dart';
import '../../data/repositories/user_repository.dart';
import '../../domain/bloc/app_bloc.dart';
import '../../domain/bloc/navigation_bloc.dart';
import '../../domain/bloc/notification_bloc.dart';
import '../../domain/bloc/user_bloc.dart';

/// Dependency injection container
/// Manages singleton instances and provides clean dependency resolution
class DependencyInjection {
  static final DependencyInjection _instance = DependencyInjection._internal();
  static DependencyInjection get instance => _instance;
  
  DependencyInjection._internal();

  final Map<Type, dynamic> _services = {};

  /// Initialize all dependencies
  Future<void> initialize() async {
    // Core services
    _registerSingleton<http.Client>(http.Client());
    _registerSingleton<WebSocketDataSource>(WebSocketDataSource());

    // Repositories
    _registerSingleton<NotificationRepository>(
      NotificationRepository(
        httpClient: get<http.Client>(),
        webSocketDataSource: get<WebSocketDataSource>(),
      ),
    );

    _registerSingleton<UserRepository>(
      UserRepository(
        httpClient: get<http.Client>(),
        webSocketDataSource: get<WebSocketDataSource>(),
      ),
    );

    Logger.info('Dependencies initialized successfully', tag: 'DependencyInjection');
  }

  /// Register a singleton service
  void _registerSingleton<T>(T instance) {
    _services[T] = instance;
  }

  /// Get a registered service
  T get<T>() {
    final service = _services[T];
    if (service == null) {
      throw Exception('Service of type $T is not registered');
    }
    return service as T;
  }

  /// Check if a service is registered
  bool isRegistered<T>() {
    return _services.containsKey(T);
  }

  /// Get BLoC providers for the app
  static List<BlocProvider> getBlocs() {
    return [
      // App-wide state management
      BlocProvider<AppBloc>(
        create: (context) => AppBloc(),
      ),

      // Navigation state management
      BlocProvider<NavigationBloc>(
        create: (context) => NavigationBloc(),
      ),

      // Notification management with real-time updates
      BlocProvider<NotificationBloc>(
        create: (context) => NotificationBloc(
          notificationRepository: instance.get<NotificationRepository>(),
        ),
      ),

      // User management with real-time updates
      BlocProvider<UserBloc>(
        create: (context) => UserBloc(
          userRepository: instance.get<UserRepository>(),
        ),
      ),
    ];
  }

  /// Dispose of resources
  Future<void> dispose() async {
    // Dispose WebSocket connection
    final websocket = _services[WebSocketDataSource] as WebSocketDataSource?;
    websocket?.dispose();

    // Dispose HTTP client
    final httpClient = _services[http.Client] as http.Client?;
    httpClient?.close();

    _services.clear();
    Logger.info('Dependencies disposed successfully', tag: 'DependencyInjection');
  }
}