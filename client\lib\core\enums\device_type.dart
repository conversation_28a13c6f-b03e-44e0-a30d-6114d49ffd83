/// Device type enumeration for responsive design
enum DeviceType {
  mobile,
  tablet, 
  desktop,
  largeDesktop,
}

extension DeviceTypeExtension on DeviceType {
  bool get isMobile => this == DeviceType.mobile;
  bool get isTablet => this == DeviceType.tablet;
  bool get isDesktop => this == DeviceType.desktop || this == DeviceType.largeDesktop;
  bool get isLargeDesktop => this == DeviceType.largeDesktop;
  
  String get displayName {
    switch (this) {
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablet';
      case DeviceType.desktop:
        return 'Desktop';
      case DeviceType.largeDesktop:
        return 'Large Desktop';
    }
  }
}