import 'package:flutter/material.dart';

/// Analytics data models for enterprise dashboard
/// Provides comprehensive data structures for business intelligence

class AnalyticsData {
  final int activeUsers;
  final int tasksCompleted;
  final int totalPoints;
  final int totalTeams;
  final List<ChartDataPoint> engagementData;
  final List<ChartDataPoint> pointsDistribution;
  final List<ChartDataPoint> taskCompletionData;
  final List<ChartDataPoint> activityHeatmap;
  final List<ChartDataPoint> teamPerformanceData;
  final List<ChartDataPoint> teamComparisonData;
  final List<TeamMetrics> teamMetrics;
  final List<ActivityEntry> recentActivities;
  final double engagementRate;
  final double completionRate;
  final double averagePointsPerUser;

  AnalyticsData({
    required this.activeUsers,
    required this.tasksCompleted,
    required this.totalPoints,
    required this.totalTeams,
    required this.engagementData,
    required this.pointsDistribution,
    required this.taskCompletionData,
    required this.activityHeatmap,
    required this.teamPerformanceData,
    required this.teamComparisonData,
    required this.teamMetrics,
    required this.recentActivities,
    required this.engagementRate,
    required this.completionRate,
    required this.averagePointsPerUser,
  });

  factory AnalyticsData.fromJson(Map<String, dynamic> json) {
    return AnalyticsData(
      activeUsers: json['activeUsers'] ?? 0,
      tasksCompleted: json['tasksCompleted'] ?? 0,
      totalPoints: json['totalPoints'] ?? 0,
      totalTeams: json['totalTeams'] ?? 0,
      engagementData: (json['engagementData'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      pointsDistribution: (json['pointsDistribution'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      taskCompletionData: (json['taskCompletionData'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      activityHeatmap: (json['activityHeatmap'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      teamPerformanceData: (json['teamPerformanceData'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      teamComparisonData: (json['teamComparisonData'] as List<dynamic>?)
          ?.map((e) => ChartDataPoint.fromJson(e))
          .toList() ?? [],
      teamMetrics: (json['teamMetrics'] as List<dynamic>?)
          ?.map((e) => TeamMetrics.fromJson(e))
          .toList() ?? [],
      recentActivities: (json['recentActivities'] as List<dynamic>?)
          ?.map((e) => ActivityEntry.fromJson(e))
          .toList() ?? [],
      engagementRate: (json['engagementRate'] ?? 0.0).toDouble(),
      completionRate: (json['completionRate'] ?? 0.0).toDouble(),
      averagePointsPerUser: (json['averagePointsPerUser'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activeUsers': activeUsers,
      'tasksCompleted': tasksCompleted,
      'totalPoints': totalPoints,
      'totalTeams': totalTeams,
      'engagementData': engagementData.map((e) => e.toJson()).toList(),
      'pointsDistribution': pointsDistribution.map((e) => e.toJson()).toList(),
      'taskCompletionData': taskCompletionData.map((e) => e.toJson()).toList(),
      'activityHeatmap': activityHeatmap.map((e) => e.toJson()).toList(),
      'teamPerformanceData': teamPerformanceData.map((e) => e.toJson()).toList(),
      'teamComparisonData': teamComparisonData.map((e) => e.toJson()).toList(),
      'teamMetrics': teamMetrics.map((e) => e.toJson()).toList(),
      'recentActivities': recentActivities.map((e) => e.toJson()).toList(),
      'engagementRate': engagementRate,
      'completionRate': completionRate,
      'averagePointsPerUser': averagePointsPerUser,
    };
  }
}

/// Chart data point for analytics visualizations
class ChartDataPoint {
  final String label;
  final double value;
  final DateTime? timestamp;
  final String? category;
  final Map<String, dynamic>? metadata;

  ChartDataPoint({
    required this.label,
    required this.value,
    this.timestamp,
    this.category,
    this.metadata,
  });

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) {
    return ChartDataPoint(
      label: json['label'] ?? '',
      value: (json['value'] ?? 0.0).toDouble(),
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'])
          : null,
      category: json['category'],
      metadata: json['metadata']?.cast<String, dynamic>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      'timestamp': timestamp?.toIso8601String(),
      'category': category,
      'metadata': metadata,
    };
  }
}

/// Team metrics for analytics
class TeamMetrics {
  final String id;
  final String name;
  final int memberCount;
  final int totalPoints;
  final double completionRate;
  final double avgPointsPerTask;
  final int activeStreak;
  final Color color;

  TeamMetrics({
    required this.id,
    required this.name,
    required this.memberCount,
    required this.totalPoints,
    required this.completionRate,
    required this.avgPointsPerTask,
    required this.activeStreak,
    required this.color,
  });

  factory TeamMetrics.fromJson(Map<String, dynamic> json) {
    return TeamMetrics(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      memberCount: json['memberCount'] ?? 0,
      totalPoints: json['totalPoints'] ?? 0,
      completionRate: (json['completionRate'] ?? 0.0).toDouble(),
      avgPointsPerTask: (json['avgPointsPerTask'] ?? 0.0).toDouble(),
      activeStreak: json['activeStreak'] ?? 0,
      color: Color(json['color'] ?? 0xFF2196F3),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'memberCount': memberCount,
      'totalPoints': totalPoints,
      'completionRate': completionRate,
      'avgPointsPerTask': avgPointsPerTask,
      'activeStreak': activeStreak,
      'color': color.toARGB32,
    };
  }
}

/// Activity entry for timeline
class ActivityEntry {
  final String id;
  final String type;
  final String title;
  final String description;
  final String userId;
  final String userName;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  ActivityEntry({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.userId,
    required this.userName,
    required this.timestamp,
    this.metadata,
  });

  factory ActivityEntry.fromJson(Map<String, dynamic> json) {
    return ActivityEntry(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata']?.cast<String, dynamic>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'description': description,
      'userId': userId,
      'userName': userName,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Chart types for analytics widgets
enum ChartType {
  line,
  bar,
  pie,
  radar,
  heatmap,
  combo,
}
