import 'dart:async';
import 'dart:developer' as developer;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../services/cache_service.dart';
import '../services/sync_service.dart';

/// Offline-first repository pattern for data management
/// Provides seamless online/offline data access with automatic synchronization
abstract class OfflineRepository<T> {
  final ApiService _apiService;
  final CacheService _cacheService;
  final SyncService _syncService;
  final Connectivity _connectivity;

  /// Protected getter for API service access in subclasses
  @protected
  ApiService get apiService => _apiService;
  
  // Repository configuration
  final String repositoryName;
  final Duration defaultCacheTTL;
  final bool enableOfflineMode;
  
  OfflineRepository({
    required ApiService apiService,
    required CacheService cacheService,
    required SyncService syncService,
    required this.repositoryName,
    this.defaultCacheTTL = const Duration(hours: 1),
    this.enableOfflineMode = true,
    Connectivity? connectivity,
  }) : _apiService = apiService,
       _cacheService = cacheService,
       _syncService = syncService,
       _connectivity = connectivity ?? Connectivity();

  /// Get data with offline-first strategy
  Future<T?> get(String id, {bool forceRefresh = false}) async {
    final cacheKey = _buildCacheKey('item', id);
    
    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        final cached = await _cacheService.get<T>(cacheKey);
        if (cached != null) {
          developer.log('Cache hit for $repositoryName:$id', name: 'OfflineRepository');
          return cached;
        }
      }
      
      // Try to fetch from API if online
      if (await _isOnline()) {
        final data = await fetchFromApi(id);
        if (data != null) {
          await _cacheService.set(cacheKey, data, ttl: defaultCacheTTL);
          developer.log('Fetched and cached $repositoryName:$id', name: 'OfflineRepository');
          return data;
        }
      }
      
      // Fallback to cache if offline or API failed
      if (enableOfflineMode) {
        final cached = await _cacheService.get<T>(cacheKey);
        if (cached != null) {
          developer.log('Offline fallback for $repositoryName:$id', name: 'OfflineRepository');
          return cached;
        }
      }
      
      return null;
    } catch (e) {
      developer.log('Error in get($id): $e', name: 'OfflineRepository');
      
      // Try cache as fallback
      if (enableOfflineMode) {
        final cached = await _cacheService.get<T>(cacheKey);
        if (cached != null) {
          developer.log('Error fallback to cache for $repositoryName:$id', name: 'OfflineRepository');
          return cached;
        }
      }
      
      rethrow;
    }
  }

  /// Get list of data with offline-first strategy
  Future<List<T>> getList({
    Map<String, dynamic>? filters,
    int? page,
    int? limit,
    bool forceRefresh = false,
  }) async {
    final cacheKey = _buildCacheKey('list', _serializeFilters(filters, page, limit));
    
    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        final cached = await _cacheService.get<List<T>>(cacheKey);
        if (cached != null) {
          developer.log('Cache hit for $repositoryName list', name: 'OfflineRepository');
          return cached;
        }
      }
      
      // Try to fetch from API if online
      if (await _isOnline()) {
        final data = await fetchListFromApi(filters: filters, page: page, limit: limit);
        await _cacheService.set(cacheKey, data, ttl: defaultCacheTTL);
        developer.log('Fetched and cached $repositoryName list', name: 'OfflineRepository');
        return data;
      }
      
      // Fallback to cache if offline or API failed
      if (enableOfflineMode) {
        final cached = await _cacheService.get<List<T>>(cacheKey);
        if (cached != null) {
          developer.log('Offline fallback for $repositoryName list', name: 'OfflineRepository');
          return cached;
        }
      }
      
      return [];
    } catch (e) {
      developer.log('Error in getList(): $e', name: 'OfflineRepository');
      
      // Try cache as fallback
      if (enableOfflineMode) {
        final cached = await _cacheService.get<List<T>>(cacheKey);
        if (cached != null) {
          developer.log('Error fallback to cache for $repositoryName list', name: 'OfflineRepository');
          return cached;
        }
      }
      
      return [];
    }
  }

  /// Create new item with offline support
  Future<T?> create(Map<String, dynamic> data) async {
    try {
      if (await _isOnline()) {
        // Create via API
        final result = await createViaApi(data);
        if (result != null) {
          // Cache the new item
          final cacheKey = _buildCacheKey('item', extractId(result));
          await _cacheService.set(cacheKey, result, ttl: defaultCacheTTL);
          
          // Invalidate list caches
          await _invalidateListCaches();
          
          developer.log('Created $repositoryName item online', name: 'OfflineRepository');
          return result;
        }
      }
      
      // Queue for offline sync
      if (enableOfflineMode) {
        await _syncService.addPendingOperation(PendingOperation(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: 'create_$repositoryName',
          method: 'POST',
          endpoint: getCreateEndpoint(),
          data: data,
          createdAt: DateTime.now(),
        ));
        
        developer.log('Queued $repositoryName creation for sync', name: 'OfflineRepository');
        
        // Return optimistic result if possible
        return createOptimisticResult(data);
      }
      
      return null;
    } catch (e) {
      developer.log('Error in create(): $e', name: 'OfflineRepository');
      rethrow;
    }
  }

  /// Update item with offline support
  Future<T?> update(String id, Map<String, dynamic> data) async {
    try {
      if (await _isOnline()) {
        // Update via API
        final result = await updateViaApi(id, data);
        if (result != null) {
          // Update cache
          final cacheKey = _buildCacheKey('item', id);
          await _cacheService.set(cacheKey, result, ttl: defaultCacheTTL);
          
          // Invalidate list caches
          await _invalidateListCaches();
          
          developer.log('Updated $repositoryName item online', name: 'OfflineRepository');
          return result;
        }
      }
      
      // Queue for offline sync
      if (enableOfflineMode) {
        await _syncService.addPendingOperation(PendingOperation(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: 'update_$repositoryName',
          method: 'PUT',
          endpoint: getUpdateEndpoint(id),
          data: data,
          createdAt: DateTime.now(),
        ));
        
        // Update cache optimistically
        final cacheKey = _buildCacheKey('item', id);
        final cached = await _cacheService.get<T>(cacheKey);
        if (cached != null) {
          final updated = updateOptimistically(cached, data);
          await _cacheService.set(cacheKey, updated, ttl: defaultCacheTTL);
          developer.log('Updated $repositoryName item optimistically', name: 'OfflineRepository');
          return updated;
        }
      }
      
      return null;
    } catch (e) {
      developer.log('Error in update($id): $e', name: 'OfflineRepository');
      rethrow;
    }
  }

  /// Delete item with offline support
  Future<bool> delete(String id) async {
    try {
      if (await _isOnline()) {
        // Delete via API
        final success = await deleteViaApi(id);
        if (success) {
          // Remove from cache
          final cacheKey = _buildCacheKey('item', id);
          await _cacheService.remove(cacheKey);
          
          // Invalidate list caches
          await _invalidateListCaches();
          
          developer.log('Deleted $repositoryName item online', name: 'OfflineRepository');
          return true;
        }
      }
      
      // Queue for offline sync
      if (enableOfflineMode) {
        await _syncService.addPendingOperation(PendingOperation(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: 'delete_$repositoryName',
          method: 'DELETE',
          endpoint: getDeleteEndpoint(id),
          createdAt: DateTime.now(),
        ));
        
        // Remove from cache optimistically
        final cacheKey = _buildCacheKey('item', id);
        await _cacheService.remove(cacheKey);
        
        developer.log('Queued $repositoryName deletion for sync', name: 'OfflineRepository');
        return true;
      }
      
      return false;
    } catch (e) {
      developer.log('Error in delete($id): $e', name: 'OfflineRepository');
      rethrow;
    }
  }

  /// Check if device is online
  Future<bool> _isOnline() async {
    final result = await _connectivity.checkConnectivity();
    return !result.contains(ConnectivityResult.none);
  }

  /// Build cache key
  String _buildCacheKey(String type, String identifier) {
    return '${repositoryName}_${type}_$identifier';
  }

  /// Serialize filters for cache key
  String _serializeFilters(Map<String, dynamic>? filters, int? page, int? limit) {
    final parts = <String>[];
    
    if (filters != null && filters.isNotEmpty) {
      final sortedKeys = filters.keys.toList()..sort();
      for (final key in sortedKeys) {
        parts.add('$key:${filters[key]}');
      }
    }
    
    if (page != null) parts.add('page:$page');
    if (limit != null) parts.add('limit:$limit');
    
    return parts.join('|');
  }

  /// Invalidate list caches
  Future<void> _invalidateListCaches() async {
    // This would remove all list-related cache entries
    // Implementation depends on cache service capabilities
  }

  // Abstract methods to be implemented by concrete repositories
  Future<T?> fetchFromApi(String id);
  Future<List<T>> fetchListFromApi({Map<String, dynamic>? filters, int? page, int? limit});
  Future<T?> createViaApi(Map<String, dynamic> data);
  Future<T?> updateViaApi(String id, Map<String, dynamic> data);
  Future<bool> deleteViaApi(String id);
  
  String getCreateEndpoint();
  String getUpdateEndpoint(String id);
  String getDeleteEndpoint(String id);
  
  String extractId(T item);
  T? createOptimisticResult(Map<String, dynamic> data);
  T updateOptimistically(T item, Map<String, dynamic> data);
}
