import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../constants/app_constants.dart';
import '../../features/analytics/analytics_screen.dart';
import '../../features/gamification/gamification_screen.dart';
import '../../features/quests/quest_screen.dart';
import '../../screens/enterprise/enterprise_screen.dart';
import '../../screens/auth/login_screen.dart';
import '../../screens/auth/register_screen.dart';
import '../../screens/auth/forgot_password_screen.dart';
import '../../screens/auth/two_factor_verification_screen.dart';
import '../../features/home/<USER>';
import '../../features/freelancing/freelancing_screen.dart';
import '../../features/learning/learning_screen.dart';

/// Application router configuration using GoRouter
/// Features:
/// - Declarative routing
/// - Deep linking support
/// - Navigation state management
/// - Route guards and redirects
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppConstants.dashboardRoute,
    debugLogDiagnostics: true,
    routes: [
      // Authentication Routes (outside shell for full-screen experience)
      GoRoute(
        path: '/auth/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/auth/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/auth/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/auth/two-factor',
        name: 'two-factor',
        builder: (context, state) {
          final sessionToken = state.uri.queryParameters['sessionToken'] ?? '';
          final methods = state.uri.queryParameters['methods']?.split(',') ?? ['totp'];
          return TwoFactorVerificationScreen(
            sessionToken: sessionToken,
            availableMethods: methods,
          );
        },
      ),
      
      // Main Shell Route - Contains the app layout
      ShellRoute(
        builder: (context, state, child) {
          // The child will be wrapped with AppLayout automatically
          return child;
        },
        routes: [
          // Home (Simple Dashboard)
          GoRoute(
            path: AppConstants.dashboardRoute,
            name: 'home',
            builder: (context, state) => const SimpleHomeScreen(),
          ),

          // Quests
          GoRoute(
            path: AppConstants.questsRoute,
            name: 'quests',
            builder: (context, state) => const QuestScreen(),
          ),

          // Gamification
          GoRoute(
            path: AppConstants.gamificationRoute,
            name: 'gamification',
            builder: (context, state) => const GamificationScreen(),
          ),

          // Analytics
          GoRoute(
            path: AppConstants.analyticsRoute,
            name: 'analytics',
            builder: (context, state) => const AnalyticsScreen(organizationId: 'default'),
          ),

          // Enterprise
          GoRoute(
            path: AppConstants.enterpriseRoute,
            name: 'enterprise',
            builder: (context, state) => const EnterpriseScreen(),
          ),

          // Freelancing Marketplace
          GoRoute(
            path: AppConstants.freelancingRoute,
            name: 'freelancing',
            builder: (context, state) => const FreelancingScreen(),
          ),

          // Learning Management
          GoRoute(
            path: AppConstants.learningRoute,
            name: 'learning',
            builder: (context, state) => const LearningScreen(),
          ),

          // Notifications (Placeholder - will be implemented)
          GoRoute(
            path: AppConstants.notificationsRoute,
            name: 'notifications',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Notifications - Coming Soon')),
            ),
          ),

          // Profile (Placeholder - will be implemented)
          GoRoute(
            path: AppConstants.profileRoute,
            name: 'profile',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Profile - Coming Soon')),
            ),
          ),

          // Settings (Placeholder - will be implemented)
          GoRoute(
            path: AppConstants.settingsRoute,
            name: 'settings',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Settings - Coming Soon')),
            ),
          ),
        ],
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: ${state.error?.toString() ?? "Page not found"}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
    
    // Route redirects and guards can be added here
    redirect: (context, state) {
      // Example: Authentication checks
      // final isAuthenticated = context.read<AuthBloc>().state.isAuthenticated;
      // if (!isAuthenticated && !state.matchedLocation.startsWith('/auth')) {
      //   return '/auth/login';
      // }
      return null;
    },
  );
}

// Placeholder screens for routes not yet fully implemented


class QuestDetailsScreen extends StatelessWidget {
  final String questId;

  const QuestDetailsScreen({
    super.key,
    required this.questId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Quest Details: $questId')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.flag_outlined, size: 64),
            const SizedBox(height: 16),
            Text(
              'Quest ID: $questId',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const Text('Quest details would be displayed here'),
          ],
        ),
      ),
    );
  }
}

class CreateQuestScreen extends StatelessWidget {
  const CreateQuestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Quest')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_task_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Create Quest',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Quest creation form would be here'),
          ],
        ),
      ),
    );
  }
}


class AchievementsScreen extends StatelessWidget {
  const AchievementsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Achievements')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.emoji_events_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Achievements',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Your earned achievements'),
          ],
        ),
      ),
    );
  }
}

class LeaderboardScreen extends StatelessWidget {
  const LeaderboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Leaderboard')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.leaderboard_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Leaderboard',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Top performers and rankings'),
          ],
        ),
      ),
    );
  }
}



class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Notifications',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('All your notifications'),
          ],
        ),
      ),
    );
  }
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Profile Screen',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Your profile information'),
          ],
        ),
      ),
    );
  }
}

class EditProfileScreen extends StatelessWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Profile')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.edit_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Edit Profile',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Edit your profile information'),
          ],
        ),
      ),
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings_rounded, size: 64),
            SizedBox(height: 16),
            Text(
              'Settings Screen',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text('Application settings and preferences'),
          ],
        ),
      ),
    );
  }
}

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account Settings')),
      body: const Center(
        child: Text('Account Settings'),
      ),
    );
  }
}

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notification Settings')),
      body: const Center(
        child: Text('Notification Settings'),
      ),
    );
  }
}

class PrivacySettingsScreen extends StatelessWidget {
  const PrivacySettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Privacy Settings')),
      body: const Center(
        child: Text('Privacy Settings'),
      ),
    );
  }
}

class NotFoundScreen extends StatelessWidget {
  final String? errorMessage;

  const NotFoundScreen({
    super.key,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline_rounded, size: 64),
            const SizedBox(height: 16),
            const Text(
              '404 - Page Not Found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            if (errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(errorMessage!),
            ],
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}