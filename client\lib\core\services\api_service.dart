import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:shared/shared.dart';

/// API service for communicating with the Quester gamification server
class ApiService {
  // Use empty base URL to make requests relative to current domain (through nginx proxy)
  static const String _baseUrl = '';
  static const String _gamificationPath = '/gamification';
  static const String _enterprisePath = '/enterprise';
  static const String _analyticsPath = '/analytics';
  
  final http.Client _client;
  
  ApiService({http.Client? client}) : _client = client ?? http.Client();
  
  /// Get user points and role information
  Future<UserPoints?> getUserPoints(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/points'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return UserPoints.fromJson(data);
      }
      return null;
    } catch (e) {
      developer.log('Error fetching user points: $e', name: 'ApiService');
      return null;
    }
  }
  
  /// Award points to a user
  Future<bool> awardPoints(String userId, int points, String activityType, String description) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/points'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'points': points,
          'activity_type': activityType,
          'description': description,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      developer.log('Error awarding points: $e', name: 'ApiService');
      return false;
    }
  }
  
  /// Get user statistics
  Future<Map<String, dynamic>?> getUserStats(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/stats'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      developer.log('Error fetching user stats: $e');
      return null;
    }
  }
  
  /// Get user achievements
  Future<List<Achievement>> getUserAchievements(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/achievements'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Achievement.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching user achievements: $e');
      return [];
    }
  }
  
  /// Get all available achievements
  Future<List<Achievement>> getAllAchievements() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/achievements'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Achievement.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching achievements: $e');
      return [];
    }
  }
  
  /// Get leaderboard data
  Future<List<LeaderboardEntry>?> getLeaderboard({String type = 'global_points', int limit = 50}) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/leaderboard?type=$type&limit=$limit'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> entries = data['entries'] ?? [];
        return entries.map((entry) => LeaderboardEntry.fromJson(entry)).toList();
      }
      return null;
    } catch (e) {
      developer.log('Error fetching leaderboard: $e');
      return null;
    }
  }
  
  /// Get all available rewards
  Future<List<Reward>> getAllRewards() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/rewards'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Reward.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching rewards: $e');
      return [];
    }
  }
  
  /// Purchase a reward
  Future<bool> purchaseReward(String rewardId, String userId, int userPoints) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_gamificationPath/rewards/$rewardId/purchase'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': userId,
          'user_points': userPoints,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      developer.log('Error purchasing reward: $e');
      return false;
    }
  }
  
  /// Check for new achievements
  Future<List<Achievement>> checkAchievements(String userId, Map<String, dynamic> progressData) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/achievement/check'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(progressData),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> achievements = data['achievements'] ?? [];
        return achievements.map((json) => Achievement.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      developer.log('Error checking achievements: $e');
      return [];
    }
  }
  
  /// Get global activity feed
  Future<List<Map<String, dynamic>>> getGlobalActivity({int limit = 50, int offset = 0}) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/activity/global?limit=$limit&offset=$offset'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching global activity: $e');
      return [];
    }
  }
  
  /// Get user-specific activity feed
  Future<List<Map<String, dynamic>>> getUserActivity(String userId, {int limit = 50, int offset = 0}) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/activity/user/$userId?limit=$limit&offset=$offset'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching user activity: $e');
      return [];
    }
  }

  // ==================== ENTERPRISE API METHODS ====================

  /// Create a new organization
  Future<Map<String, dynamic>?> createOrganization(Map<String, dynamic> organizationData) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_enterprisePath/organizations'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(organizationData),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      developer.log('Error creating organization: $e');
      return null;
    }
  }

  /// Get organization details
  Future<Map<String, dynamic>?> getOrganization(String organizationId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_enterprisePath/organizations/$organizationId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      developer.log('Error fetching organization: $e');
      return null;
    }
  }

  /// Get organizations for a user
  Future<List<Map<String, dynamic>>> getUserOrganizations(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_enterprisePath/user/$userId/organizations'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['data'] is List) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      return [];
    } catch (e) {
      developer.log('Error fetching user organizations: $e');
      return [];
    }
  }
  
  // ==================== QUEST API METHODS ====================

  /// Create a new quest
  Future<Map<String, dynamic>?> createQuest(Map<String, dynamic> questData) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_gamificationPath/quests'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(questData),
      );
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      developer.log('Error creating quest: $e');
      return null;
    }
  }

  /// Get all quests for a user
  Future<List<Map<String, dynamic>>> getUserQuests(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/user/$userId/quests'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching user quests: $e');
      return [];
    }
  }

  /// Get quest details by ID
  Future<Map<String, dynamic>?> getQuest(String questId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/quests/$questId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      developer.log('Error fetching quest: $e');
      return null;
    }
  }

  /// Update a quest
  Future<bool> updateQuest(String questId, Map<String, dynamic> questData) async {
    try {
      final response = await _client.put(
        Uri.parse('$_baseUrl$_gamificationPath/quests/$questId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(questData),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      developer.log('Error updating quest: $e');
      return false;
    }
  }

  /// Delete a quest
  Future<bool> deleteQuest(String questId) async {
    try {
      final response = await _client.delete(
        Uri.parse('$_baseUrl$_gamificationPath/quests/$questId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      return response.statusCode == 200;
    } catch (e) {
      developer.log('Error deleting quest: $e');
      return false;
    }
  }

  /// Complete a quest task
  Future<bool> completeTask(String questId, String taskId) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$_gamificationPath/quests/$questId/tasks/$taskId/complete'),
        headers: {'Content-Type': 'application/json'},
      );
      
      return response.statusCode == 200;
    } catch (e) {
      developer.log('Error completing task: $e');
      return false;
    }
  }

  /// Get quest templates
  Future<List<Map<String, dynamic>>> getQuestTemplates() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl$_gamificationPath/quest-templates'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      developer.log('Error fetching quest templates: $e');
      return [];
    }
  }

  // ==================== ANALYTICS API METHODS ====================

  /// Generic GET request with query parameters
  Future<ApiResponse> get(String endpoint, {Map<String, String>? queryParameters}) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint').replace(queryParameters: queryParameters);
      final response = await _client.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return ApiResponse(
          data: jsonDecode(response.body),
          statusCode: response.statusCode,
          success: true,
        );
      } else {
        return ApiResponse(
          data: null,
          statusCode: response.statusCode,
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      developer.log('Error in GET request to $endpoint: $e', name: 'ApiService');
      return ApiResponse(
        data: null,
        statusCode: 500,
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Generic POST request
  Future<ApiResponse> post(String endpoint, {Map<String, dynamic>? body}) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
        body: body != null ? jsonEncode(body) : null,
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse(
          data: jsonDecode(response.body),
          statusCode: response.statusCode,
          success: true,
        );
      } else {
        return ApiResponse(
          data: null,
          statusCode: response.statusCode,
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      developer.log('Error in POST request to $endpoint: $e', name: 'ApiService');
      return ApiResponse(
        data: null,
        statusCode: 500,
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Subscribe to real-time metrics updates (WebSocket simulation)
  Stream<Map<String, dynamic>> subscribeToMetricsUpdates(String organizationId) {
    // For now, simulate WebSocket with periodic HTTP polling
    // In a real implementation, this would be a WebSocket connection
    return Stream.periodic(const Duration(seconds: 30), (_) async {
      try {
        final response = await get('$_analyticsPath/dashboard/$organizationId');
        if (response.success && response.data != null) {
          return response.data as Map<String, dynamic>;
        }
        return <String, dynamic>{};
      } catch (e) {
        developer.log('Error in metrics subscription: $e', name: 'ApiService');
        return <String, dynamic>{};
      }
    }).asyncMap((future) => future);
  }

  // ==================== FREELANCER API METHODS ====================

  /// Get freelancer profile
  Future<ApiResponse> getFreelancerProfile(String userId) async {
    return await get('/api/v1/freelancer/profile/$userId');
  }

  /// Update freelancer profile
  Future<ApiResponse> updateFreelancerProfile(Map<String, dynamic> profileData) async {
    return await post('/api/v1/freelancer/profile', body: profileData);
  }

  /// Get freelancer skills
  Future<ApiResponse> getFreelancerSkills() async {
    return await get('/api/v1/freelancer/skills');
  }

  /// Update freelancer availability
  Future<ApiResponse> updateFreelancerAvailability(Map<String, dynamic> availabilityData) async {
    return await post('/api/v1/freelancer/availability', body: availabilityData);
  }

  /// Get freelancer portfolio
  Future<ApiResponse> getFreelancerPortfolio(String userId) async {
    return await get('/api/v1/freelancer/portfolio/$userId');
  }

  /// Add portfolio item
  Future<ApiResponse> addPortfolioItem(Map<String, dynamic> portfolioItem) async {
    return await post('/api/v1/freelancer/portfolio', body: portfolioItem);
  }

  /// Update portfolio item
  Future<ApiResponse> updatePortfolioItem(String itemId, Map<String, dynamic> portfolioItem) async {
    return await post('/api/v1/freelancer/portfolio/$itemId', body: portfolioItem);
  }

  /// Delete portfolio item
  Future<ApiResponse> deletePortfolioItem(String itemId) async {
    try {
      final response = await _client.delete(
        Uri.parse('$_baseUrl/api/v1/freelancer/portfolio/$itemId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return ApiResponse(
          data: null,
          statusCode: response.statusCode,
          success: true,
        );
      } else {
        return ApiResponse(
          data: null,
          statusCode: response.statusCode,
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      developer.log('Error deleting portfolio item: $e', name: 'ApiService');
      return ApiResponse(
        data: null,
        statusCode: 500,
        success: false,
        error: e.toString(),
      );
    }
  }

  // ==================== SSO CONFIGURATION API METHODS ====================

  /// Get SSO configuration for organization
  Future<ApiResponse> getSSOConfiguration(String organizationId) async {
    return await get('/api/v1/enterprise/sso/config/$organizationId');
  }

  /// Save SSO configuration
  Future<ApiResponse> saveSSOConfiguration(Map<String, dynamic> configData) async {
    return await post('/api/v1/enterprise/sso/config', body: configData);
  }

  /// Test SSO configuration
  Future<ApiResponse> testSSOConfiguration(Map<String, dynamic> configData) async {
    return await post('/api/v1/enterprise/sso/test', body: configData);
  }

  /// Get available SSO providers
  Future<ApiResponse> getSSOProviders() async {
    return await get('/api/v1/enterprise/sso/providers');
  }

  /// Validate SSO metadata
  Future<ApiResponse> validateSSOMetadata(String metadata) async {
    return await post('/api/v1/enterprise/sso/validate-metadata', body: {'metadata': metadata});
  }

  void dispose() {
    _client.close();
  }
}

/// API Response wrapper class
class ApiResponse {
  final dynamic data;
  final int statusCode;
  final bool success;
  final String? error;

  const ApiResponse({
    required this.data,
    required this.statusCode,
    required this.success,
    this.error,
  });
}
