import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared/shared.dart';
import '../utils/logger.dart';
import 'secure_storage.dart';

/// Authentication service for handling user authentication
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  static String get _baseUrl {
    // In development, use nginx proxy URL for proper Docker networking
    const serverHost = String.fromEnvironment('SERVER_HOST', defaultValue: 'localhost');
    const serverPort = String.fromEnvironment('SERVER_PORT', defaultValue: '80');
    const useHttps = String.fromEnvironment('USE_HTTPS', defaultValue: 'false');
    
    final protocol = useHttps == 'true' ? 'https' : 'http';
    return '$protocol://$serverHost:$serverPort';
  }

  AuthSession? _currentSession;

  /// Initialize the authentication service
  Future<void> initialize() async {
    try {
      // Initialize shared preferences for future use if needed
      await _loadStoredSession();
      Logger.info('AuthService initialized successfully', tag: 'AuthService');
    } catch (e) {
      Logger.error('AuthService initialization failed', tag: 'AuthService', error: e);
      rethrow;
    }
  }

  /// Get current authentication session
  AuthSession? get currentSession => _currentSession;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentSession != null && !_currentSession!.isExpired;

  /// Check if user has specific permission
  bool hasPermission(Permission permission) {
    return _currentSession?.hasPermission(permission) ?? false;
  }

  /// Check if user has permission in specific organization
  bool hasPermissionInOrganization(String organizationId, Permission permission) {
    return _currentSession?.hasPermissionInOrganization(organizationId, permission) ?? false;
  }

  /// Check if user is admin
  bool get isAdmin => _currentSession?.isAdmin ?? false;

  /// Check if user is system admin
  bool get isSystemAdmin => _currentSession?.isSystemAdmin ?? false;

  /// Login with email and password
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
    String? twoFactorCode,
    String? organizationId,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/auth/login'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({
              'email': email.trim().toLowerCase(),
              'password': password,
              'rememberMe': rememberMe,
              'twoFactorCode': twoFactorCode?.trim(),
              'organizationId': organizationId?.trim(),
            }),
          )
          .timeout(Duration(seconds: 30));

      if (response.body.isEmpty) {
        return AuthResult.error(
          message: 'Empty response from server',
          errorCode: 'SERVER_ERROR',
        );
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        if (data['requiresTwoFactor'] == true) {
          return AuthResult.requiresTwoFactor(
            sessionToken: data['sessionToken'],
            availableMethods: (data['availableMethods'] as List<dynamic>)
                .map((m) => TwoFactorMethod.values.firstWhere((method) => method.name == m))
                .toList(),
          );
        }

        if (data['session'] == null) {
          return AuthResult.error(
            message: 'Invalid session data from server',
            errorCode: 'SERVER_ERROR',
          );
        }
        
        final session = AuthSession.fromJson(data['session'] as Map<String, dynamic>);
        await _storeSession(session);
        _currentSession = session;

        return AuthResult.success(
          session: session,
          requiresEmailVerification: data['requiresEmailVerification'] ?? false,
        );
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Login failed',
          errorCode: data['errorCode'] ?? 'AUTH_ERROR',
        );
      }
    } catch (e) {
      Logger.error('Login failed', tag: 'AuthService', error: e);
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String displayName,
    String? firstName,
    String? lastName,
    String? organizationName,
    String? invitationToken,
    Map<String, dynamic>? userPreferences,
    bool acceptTerms = false,
    bool subscribeToNewsletter = false,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'displayName': displayName,
          'firstName': firstName,
          'lastName': lastName,
          'organizationName': organizationName,
          'invitationToken': invitationToken,
          'userPreferences': userPreferences,
          'acceptTerms': acceptTerms,
          'subscribeToNewsletter': subscribeToNewsletter,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.registrationSuccess(
          userId: data['userId'],
          requiresEmailVerification: data['requiresEmailVerification'] ?? true,
          organizationCreated: data['organizationCreated'] ?? false,
          organizationId: data['organizationId'],
        );
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Registration failed',
          errorCode: data['errorCode'] ?? 'REGISTRATION_ERROR',
          passwordPolicy: data['passwordPolicy'],
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Verify two-factor authentication
  Future<AuthResult> verifyTwoFactor({
    required String sessionToken,
    required TwoFactorMethod method,
    required String code,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/verify-2fa'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'sessionToken': sessionToken,
          'method': method.name,
          'code': code,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        final session = AuthSession.fromJson(data['session']);
        await _storeSession(session);
        _currentSession = session;

        return AuthResult.success(
          session: session,
          requiresEmailVerification: data['requiresEmailVerification'] ?? false,
        );
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Two-factor verification failed',
          errorCode: data['errorCode'] ?? '2FA_ERROR',
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Refresh authentication token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await _getStoredRefreshToken();
      if (refreshToken == null) return false;

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'refreshToken': refreshToken}),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        final session = AuthSession.fromJson(data['session']);
        await _storeSession(session);
        _currentSession = session;
        return true;
      } else {
        await _clearStoredSession();
        _currentSession = null;
        return false;
      }
    } catch (e) {
      Logger.error('Token refresh failed', tag: 'AuthService', error: e);
      await _clearStoredSession();
      _currentSession = null;
      return false;
    }
  }

  /// Verify email address
  Future<AuthResult> verifyEmail({
    required String verificationToken,
    required String email,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/verify-email'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'verificationToken': verificationToken,
          'email': email,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.emailVerified();
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Email verification failed',
          errorCode: data['errorCode'] ?? 'VERIFICATION_ERROR',
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Request password reset
  Future<AuthResult> requestPasswordReset(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/forgot-password'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email}),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.passwordResetSent();
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Password reset failed',
          errorCode: data['errorCode'] ?? 'RESET_ERROR',
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Reset password with token
  Future<AuthResult> resetPassword({
    required String resetToken,
    required String newPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/reset-password'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'resetToken': resetToken,
          'newPassword': newPassword,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.passwordResetComplete();
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Password reset failed',
          errorCode: data['errorCode'] ?? 'RESET_ERROR',
          passwordPolicy: data['passwordPolicy'],
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Change password (authenticated user)
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/change-password'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_currentSession?.token}',
        },
        body: jsonEncode({
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.passwordChanged();
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Password change failed',
          errorCode: data['errorCode'] ?? 'CHANGE_ERROR',
          passwordPolicy: data['passwordPolicy'],
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Setup two-factor authentication
  Future<TwoFactorSetupResult> setupTwoFactor({
    required TwoFactorMethod method,
    String? phoneNumber,
    String? email,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/setup-2fa'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_currentSession?.token}',
        },
        body: jsonEncode({
          'method': method.name,
          'phoneNumber': phoneNumber,
          'email': email,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return TwoFactorSetupResult.success(
          method: method,
          secret: data['secret'],
          qrCode: data['qrCode'],
          backupCodes: data['backupCodes']?.cast<String>(),
          phoneNumber: data['phoneNumber'],
          email: data['email'],
        );
      } else {
        return TwoFactorSetupResult.error(
          message: data['error'] ?? 'Two-factor setup failed',
          errorCode: data['errorCode'] ?? '2FA_SETUP_ERROR',
        );
      }
    } catch (e) {
      return TwoFactorSetupResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Update user profile
  Future<AuthResult> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/auth/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_currentSession?.token}',
        },
        body: jsonEncode({
          'displayName': displayName,
          'firstName': firstName,
          'lastName': lastName,
          'preferences': preferences,
        }),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        // Update local session with new user data
        if (_currentSession != null) {
          final updatedUser = _currentSession!.user.copyWith(
            displayName: displayName ?? _currentSession!.user.displayName,
            firstName: firstName ?? _currentSession!.user.firstName,
            lastName: lastName ?? _currentSession!.user.lastName,
            preferences: preferences ?? _currentSession!.user.preferences,
          );
          _currentSession = _currentSession!.copyWith(user: updatedUser);
          await _storeSession(_currentSession!);
        }

        return AuthResult.profileUpdated();
      } else {
        return AuthResult.error(
          message: data['error'] ?? 'Profile update failed',
          errorCode: data['errorCode'] ?? 'PROFILE_ERROR',
        );
      }
    } catch (e) {
      return AuthResult.error(
        message: 'Network error: $e',
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Get password policy
  Future<Map<String, dynamic>?> getPasswordPolicy() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/auth/password-policy'),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return data['policy'] as Map<String, dynamic>;
      }
    } catch (e) {
      Logger.error('Password policy error', tag: 'AuthService', error: e);
    }
    return null;
  }

  /// Check email availability
  Future<bool> isEmailAvailable(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/check-email'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email}),
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200 && data['success'] == true) {
        return data['available'] ?? false;
      }
    } catch (e) {
      Logger.error('Email availability check failed', tag: 'AuthService', error: e);
    }
    return false;
  }

  /// Logout user
  Future<void> logout() async {
    try {
      if (_currentSession != null) {
        await http.post(
          Uri.parse('$_baseUrl/auth/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${_currentSession?.token}',
          },
        );
      }
    } catch (e) {
      Logger.warning('Logout error', tag: 'AuthService', error: e);
    } finally {
      await _clearStoredSession();
      _currentSession = null;
    }
  }

  /// Check if session is valid and refresh if needed
  Future<bool> validateSession() async {
    if (_currentSession == null) {
      await _loadStoredSession();
      if (_currentSession == null) return false;
    }

    if (_currentSession!.isExpired) {
      // Session is already expired, try to refresh
      final refreshSuccess = await refreshToken();
      return refreshSuccess;
    } else if (_currentSession!.isExpiringSoon) {
      // Session expires soon, proactively refresh
      final refreshSuccess = await refreshToken();
      return refreshSuccess || !_currentSession!.isExpired; // Keep current if refresh fails but still valid
    }

    return true;
  }

  // Private helper methods

  Future<void> _loadStoredSession() async {
    try {
      final sessionData = await SecureAuthStorage.getSession();
      final tokenData = sessionData['token'];
      final refreshTokenData = sessionData['refreshToken'];
      final userData = sessionData['userData'];

      if (tokenData != null && refreshTokenData != null && userData != null) {
        final sessionJson = jsonDecode(userData) as Map<String, dynamic>;
        _currentSession = AuthSession.fromJson(sessionJson);

        // Validate session is not expired
        if (_currentSession!.isExpired) {
          await refreshToken();
        }
      }
    } catch (e) {
      Logger.error('Error loading stored session', tag: 'AuthService', error: e);
      await _clearStoredSession();
    }
  }

  Future<void> _storeSession(AuthSession session) async {
    try {
      await SecureAuthStorage.storeSession(
        session.token,
        session.refreshToken,
        jsonEncode(session.toJson()),
      );
    } catch (e) {
      Logger.error('Error storing session', tag: 'AuthService', error: e);
    }
  }

  Future<void> _clearStoredSession() async {
    try {
      await SecureAuthStorage.clearSession();
    } catch (e) {
      Logger.error('Error clearing stored session', tag: 'AuthService', error: e);
    }
  }

  Future<String?> _getStoredRefreshToken() async {
    final sessionData = await SecureAuthStorage.getSession();
    return sessionData['refreshToken'];
  }
}

/// Result of authentication operations
class AuthResult {
  final bool isSuccess;
  final String? message;
  final String? errorCode;
  final AuthSession? session;
  final bool requiresTwoFactor;
  final String? sessionToken;
  final List<TwoFactorMethod>? availableTwoFactorMethods;
  final bool requiresEmailVerification;
  final String? userId;
  final bool organizationCreated;
  final String? organizationId;
  final Map<String, dynamic>? passwordPolicy;

  const AuthResult._({
    required this.isSuccess,
    this.message,
    this.errorCode,
    this.session,
    this.requiresTwoFactor = false,
    this.sessionToken,
    this.availableTwoFactorMethods,
    this.requiresEmailVerification = false,
    this.userId,
    this.organizationCreated = false,
    this.organizationId,
    this.passwordPolicy,
  });

  factory AuthResult.success({
    required AuthSession session,
    bool requiresEmailVerification = false,
  }) {
    return AuthResult._(
      isSuccess: true,
      session: session,
      requiresEmailVerification: requiresEmailVerification,
      message: 'Authentication successful',
    );
  }

  factory AuthResult.requiresTwoFactor({
    required String sessionToken,
    required List<TwoFactorMethod> availableMethods,
  }) {
    return AuthResult._(
      isSuccess: false,
      requiresTwoFactor: true,
      sessionToken: sessionToken,
      availableTwoFactorMethods: availableMethods,
      message: 'Two-factor authentication required',
    );
  }

  factory AuthResult.registrationSuccess({
    required String userId,
    bool requiresEmailVerification = true,
    bool organizationCreated = false,
    String? organizationId,
  }) {
    return AuthResult._(
      isSuccess: true,
      userId: userId,
      requiresEmailVerification: requiresEmailVerification,
      organizationCreated: organizationCreated,
      organizationId: organizationId,
      message: 'Registration successful',
    );
  }

  factory AuthResult.emailVerified() {
    return const AuthResult._(
      isSuccess: true,
      message: 'Email verified successfully',
    );
  }

  factory AuthResult.passwordResetSent() {
    return const AuthResult._(
      isSuccess: true,
      message: 'Password reset email sent',
    );
  }

  factory AuthResult.passwordResetComplete() {
    return const AuthResult._(
      isSuccess: true,
      message: 'Password reset successfully',
    );
  }

  factory AuthResult.passwordChanged() {
    return const AuthResult._(
      isSuccess: true,
      message: 'Password changed successfully',
    );
  }

  factory AuthResult.profileUpdated() {
    return const AuthResult._(
      isSuccess: true,
      message: 'Profile updated successfully',
    );
  }

  factory AuthResult.error({
    required String message,
    required String errorCode,
    Map<String, dynamic>? passwordPolicy,
  }) {
    return AuthResult._(
      isSuccess: false,
      message: message,
      errorCode: errorCode,
      passwordPolicy: passwordPolicy,
    );
  }
}

/// Result of two-factor authentication setup
class TwoFactorSetupResult {
  final bool isSuccess;
  final String? message;
  final String? errorCode;
  final TwoFactorMethod? method;
  final String? secret;
  final String? qrCode;
  final List<String>? backupCodes;
  final String? phoneNumber;
  final String? email;

  const TwoFactorSetupResult._({
    required this.isSuccess,
    this.message,
    this.errorCode,
    this.method,
    this.secret,
    this.qrCode,
    this.backupCodes,
    this.phoneNumber,
    this.email,
  });

  factory TwoFactorSetupResult.success({
    required TwoFactorMethod method,
    String? secret,
    String? qrCode,
    List<String>? backupCodes,
    String? phoneNumber,
    String? email,
  }) {
    return TwoFactorSetupResult._(
      isSuccess: true,
      method: method,
      secret: secret,
      qrCode: qrCode,
      backupCodes: backupCodes,
      phoneNumber: phoneNumber,
      email: email,
      message: 'Two-factor authentication setup successful',
    );
  }

  factory TwoFactorSetupResult.error({
    required String message,
    required String errorCode,
  }) {
    return TwoFactorSetupResult._(
      isSuccess: false,
      message: message,
      errorCode: errorCode,
    );
  }
}