import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';

/// Comprehensive caching service for the Quester application
/// Provides multi-level caching with TTL, compression, and intelligent eviction
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  SharedPreferences? _prefs;
  final Map<String, CacheEntry> _memoryCache = {};
  final Map<String, Timer> _expirationTimers = {};
  
  // Configuration
  static const int _maxMemoryCacheSize = 100;
  static const Duration _defaultTTL = Duration(hours: 1);
  static const String _cachePrefix = 'cache_';

  /// Initialize the cache service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadCacheMetadata();
    await _cleanupExpiredEntries();
    developer.log('Cache service initialized', name: 'CacheService');
  }

  /// Store data in cache with optional TTL
  Future<void> set(
    String key,
    dynamic data, {
    Duration? ttl,
    CacheLevel level = CacheLevel.both,
    bool compress = false,
  }) async {
    final effectiveTTL = ttl ?? _defaultTTL;
    final expiresAt = DateTime.now().add(effectiveTTL);
    
    final entry = CacheEntry(
      key: key,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      accessCount: 0,
      lastAccessed: DateTime.now(),
      compressed: compress,
      size: _calculateSize(data),
    );

    // Store in memory cache
    if (level == CacheLevel.memory || level == CacheLevel.both) {
      await _setMemoryCache(key, entry);
    }

    // Store in disk cache
    if (level == CacheLevel.disk || level == CacheLevel.both) {
      await _setDiskCache(key, entry);
    }

    // Set expiration timer
    _setExpirationTimer(key, effectiveTTL);
    
    developer.log('Cached data for key: $key (TTL: ${effectiveTTL.inMinutes}m)', name: 'CacheService');
  }

  /// Retrieve data from cache
  Future<T?> get<T>(String key, {bool updateAccessTime = true}) async {
    CacheEntry? entry;

    // Try memory cache first
    entry = _memoryCache[key];
    
    // Try disk cache if not in memory
    if (entry == null) {
      entry = await _getDiskCache(key);
      if (entry != null) {
        // Promote to memory cache
        await _setMemoryCache(key, entry);
      }
    }

    if (entry == null || entry.isExpired) {
      if (entry?.isExpired == true) {
        await remove(key);
      }
      return null;
    }

    // Update access statistics
    if (updateAccessTime) {
      entry = entry.copyWithAccess();
      _memoryCache[key] = entry;
      await _updateDiskCacheMetadata(key, entry);
    }

    return entry.data as T?;
  }

  /// Check if key exists in cache
  Future<bool> has(String key) async {
    return await get(key, updateAccessTime: false) != null;
  }

  /// Remove data from cache
  Future<void> remove(String key) async {
    // Remove from memory
    _memoryCache.remove(key);
    
    // Remove from disk
    await _prefs?.remove('$_cachePrefix$key');
    
    // Cancel expiration timer
    _expirationTimers[key]?.cancel();
    _expirationTimers.remove(key);
    
    await _updateCacheMetadata();
    
    developer.log('Removed cache entry: $key', name: 'CacheService');
  }

  /// Clear all cache data
  Future<void> clear() async {
    // Clear memory cache
    _memoryCache.clear();
    
    // Clear expiration timers
    for (final timer in _expirationTimers.values) {
      timer.cancel();
    }
    _expirationTimers.clear();
    
    // Clear disk cache
    if (_prefs != null) {
      final keys = _prefs!.getKeys().where((key) => key.startsWith(_cachePrefix));
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    }
    
    await _updateCacheMetadata();
    
    developer.log('Cache cleared', name: 'CacheService');
  }

  /// Get cache statistics
  Future<CacheStats> getStats() async {
    final diskKeys = _prefs?.getKeys().where((key) => key.startsWith(_cachePrefix)) ?? <String>[];
    
    return CacheStats(
      memoryEntries: _memoryCache.length,
      diskEntries: diskKeys.length,
      memorySize: _memoryCache.values.fold(0, (sum, entry) => sum + entry.size),
      totalHits: _memoryCache.values.fold(0, (sum, entry) => sum + entry.accessCount),
    );
  }

  /// Cleanup expired entries
  Future<void> cleanup() async {
    await _cleanupExpiredEntries();
  }

  /// Store in memory cache with LRU eviction
  Future<void> _setMemoryCache(String key, CacheEntry entry) async {
    _memoryCache[key] = entry;
    
    // Evict if over limit
    if (_memoryCache.length > _maxMemoryCacheSize) {
      await _evictLRU();
    }
  }

  /// Store in disk cache
  Future<void> _setDiskCache(String key, CacheEntry entry) async {
    if (_prefs == null) return;
    
    try {
      String jsonData = jsonEncode(entry.toJson());
      
      // Compress if requested and beneficial
      if (entry.compressed && jsonData.length > 1000) {
        final bytes = utf8.encode(jsonData);
        final compressed = gzip.encode(bytes);
        if (compressed.length < bytes.length * 0.8) {
          jsonData = base64Encode(compressed);
        }
      }
      
      await _prefs!.setString('$_cachePrefix$key', jsonData);
      await _updateCacheMetadata();
    } catch (e) {
      developer.log('Error storing to disk cache: $e', name: 'CacheService');
    }
  }

  /// Retrieve from disk cache
  Future<CacheEntry?> _getDiskCache(String key) async {
    if (_prefs == null) return null;
    
    try {
      final jsonData = _prefs!.getString('$_cachePrefix$key');
      if (jsonData == null) return null;
      
      String decodedData = jsonData;
      
      // Try to decompress if it looks like compressed data
      try {
        final compressed = base64Decode(jsonData);
        final decompressed = gzip.decode(compressed);
        decodedData = utf8.decode(decompressed);
      } catch (_) {
        // Not compressed or decompression failed, use original
      }
      
      final json = jsonDecode(decodedData) as Map<String, dynamic>;
      return CacheEntry.fromJson(json);
    } catch (e) {
      developer.log('Error retrieving from disk cache: $e', name: 'CacheService');
      return null;
    }
  }

  /// Set expiration timer
  void _setExpirationTimer(String key, Duration ttl) {
    _expirationTimers[key]?.cancel();
    _expirationTimers[key] = Timer(ttl, () async {
      await remove(key);
    });
  }

  /// Evict least recently used entries
  Future<void> _evictLRU() async {
    final entries = _memoryCache.entries.toList();
    entries.sort((a, b) => a.value.lastAccessed.compareTo(b.value.lastAccessed));
    
    final toRemove = entries.take(_memoryCache.length - _maxMemoryCacheSize + 1);
    for (final entry in toRemove) {
      _memoryCache.remove(entry.key);
    }
  }

  /// Cleanup expired entries
  Future<void> _cleanupExpiredEntries() async {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    // Check memory cache
    for (final entry in _memoryCache.entries) {
      if (entry.value.expiresAt.isBefore(now)) {
        expiredKeys.add(entry.key);
      }
    }
    
    // Check disk cache
    if (_prefs != null) {
      final diskKeys = _prefs!.getKeys().where((key) => key.startsWith(_cachePrefix));
      for (final key in diskKeys) {
        final cacheKey = key.substring(_cachePrefix.length);
        final entry = await _getDiskCache(cacheKey);
        if (entry != null && entry.expiresAt.isBefore(now)) {
          expiredKeys.add(cacheKey);
        }
      }
    }
    
    // Remove expired entries
    for (final key in expiredKeys) {
      await remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      developer.log('Cleaned up ${expiredKeys.length} expired cache entries', name: 'CacheService');
    }
  }

  /// Load cache metadata
  Future<void> _loadCacheMetadata() async {
    // Implementation for loading cache metadata
    // This would track cache statistics and configuration
  }

  /// Update cache metadata
  Future<void> _updateCacheMetadata() async {
    // Implementation for updating cache metadata
    // This would update statistics and track cache health
  }

  /// Update disk cache metadata for specific entry
  Future<void> _updateDiskCacheMetadata(String key, CacheEntry entry) async {
    // Update the disk cache entry with new access information
    await _setDiskCache(key, entry);
  }

  /// Calculate approximate size of data
  int _calculateSize(dynamic data) {
    try {
      return utf8.encode(jsonEncode(data)).length;
    } catch (_) {
      return 0;
    }
  }


}

/// Cache levels
enum CacheLevel {
  memory,
  disk,
  both,
}

/// Cache entry model
class CacheEntry {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime expiresAt;
  final int accessCount;
  final DateTime lastAccessed;
  final bool compressed;
  final int size;

  const CacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.accessCount,
    required this.lastAccessed,
    required this.compressed,
    required this.size,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);

  CacheEntry copyWithAccess() {
    return CacheEntry(
      key: key,
      data: data,
      createdAt: createdAt,
      expiresAt: expiresAt,
      accessCount: accessCount + 1,
      lastAccessed: DateTime.now(),
      compressed: compressed,
      size: size,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'access_count': accessCount,
      'last_accessed': lastAccessed.toIso8601String(),
      'compressed': compressed,
      'size': size,
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      key: json['key'] as String,
      data: json['data'],
      createdAt: DateTime.parse(json['created_at'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      accessCount: json['access_count'] as int,
      lastAccessed: DateTime.parse(json['last_accessed'] as String),
      compressed: json['compressed'] as bool,
      size: json['size'] as int,
    );
  }
}

/// Cache statistics
class CacheStats {
  final int memoryEntries;
  final int diskEntries;
  final int memorySize;
  final int totalHits;

  const CacheStats({
    required this.memoryEntries,
    required this.diskEntries,
    required this.memorySize,
    required this.totalHits,
  });

  double get hitRate => totalHits > 0 ? totalHits / (memoryEntries + diskEntries) : 0.0;

  @override
  String toString() {
    return 'CacheStats(memory: $memoryEntries, disk: $diskEntries, size: ${memorySize}B, hits: $totalHits, hit rate: ${(hitRate * 100).toStringAsFixed(1)}%)';
  }
}
