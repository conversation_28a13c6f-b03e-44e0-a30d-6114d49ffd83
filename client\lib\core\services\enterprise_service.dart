import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import 'logging_service.dart';

/// Enterprise Service for managing organization features
/// 
/// Handles enterprise-level operations including:
/// - Organization management
/// - Member management and roles
/// - Analytics and reporting
/// - SSO configuration
/// - Compliance features
class EnterpriseService {
  static const String _baseUrl = ApiConfig.baseUrl;
  late final http.Client _client;

  EnterpriseService() {
    _client = http.Client();
  }

  // Organization Management

  /// Create a new organization
  Future<Map<String, dynamic>> createOrganization({
    required String name,
    required String subscriptionTier,
    Map<String, dynamic>? settings,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'name': name,
          'subscription_tier': subscriptionTier,
          'settings': settings ?? {},
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create organization: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating organization: $e');
    }
  }

  /// Get organization details
  Future<Map<String, dynamic>> getOrganization(String organizationId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId'),
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get organization: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting organization: $e');
    }
  }

  /// Update organization settings
  Future<Map<String, dynamic>> updateOrganization(
    String organizationId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _client.put(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode(updates),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update organization: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error updating organization: $e');
    }
  }

  // Member Management

  /// Get organization members
  Future<List<Map<String, dynamic>>> getMembers(String organizationId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/members'),
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['members'] ?? []);
      } else {
        throw Exception('Failed to get members: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting members: $e');
    }
  }

  /// Add a new member to the organization
  Future<Map<String, dynamic>> addMember(
    String organizationId, {
    required String email,
    required String roleId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/members'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'email': email,
          'role_id': roleId,
          'metadata': metadata ?? {},
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to add member: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error adding member: $e');
    }
  }

  /// Update member role or settings
  Future<Map<String, dynamic>> updateMember(
    String organizationId,
    String memberId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _client.put(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/members/$memberId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode(updates),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update member: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error updating member: $e');
    }
  }

  // Analytics and Reporting

  /// Get analytics summary for the organization
  Future<Map<String, dynamic>> getAnalyticsSummary(
    String organizationId, {
    String timeRange = '7d',
    bool realTime = false,
  }) async {
    try {
      final queryParams = {
        'time_range': timeRange,
        'real_time': realTime.toString(),
      };

      final uri = Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/analytics/summary')
          .replace(queryParameters: queryParams);

      final response = await _client.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get analytics summary: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting analytics summary: $e');
    }
  }

  /// Get detailed analytics data
  Future<Map<String, dynamic>> getAnalytics(
    String organizationId, {
    required String timeRange,
    List<String>? metrics,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final body = {
        'time_range': timeRange,
        'metrics': metrics ?? [],
        'filters': filters ?? {},
      };

      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/analytics'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get analytics: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting analytics: $e');
    }
  }

  /// Create a custom analytics report
  Future<Map<String, dynamic>> createReport(
    String organizationId, {
    required String name,
    required Map<String, dynamic> configuration,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/analytics/reports'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'name': name,
          'configuration': configuration,
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create report: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating report: $e');
    }
  }

  // Role Management

  /// Get organization roles
  Future<List<Map<String, dynamic>>> getRoles(String organizationId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/roles'),
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['roles'] ?? []);
      } else {
        throw Exception('Failed to get roles: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting roles: $e');
    }
  }

  /// Create a custom role
  Future<Map<String, dynamic>> createRole(
    String organizationId, {
    required String name,
    required List<String> permissions,
    String? description,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/roles'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'name': name,
          'permissions': permissions,
          'description': description,
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create role: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating role: $e');
    }
  }

  // SSO Configuration (Future Implementation)

  /// Configure SSO settings for the organization
  Future<Map<String, dynamic>> configureSSOSettings(
    String organizationId,
    Map<String, dynamic> ssoConfig,
  ) async {
    try {
      final response = await _client.put(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/sso'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode(ssoConfig),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to configure SSO: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error configuring SSO: $e');
    }
  }

  // Compliance and Audit

  /// Get audit logs for the organization
  Future<List<Map<String, dynamic>>> getAuditLogs(
    String organizationId, {
    String? startDate,
    String? endDate,
    String? userId,
    String? action,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (startDate != null) queryParams['start_date'] = startDate;
      if (endDate != null) queryParams['end_date'] = endDate;
      if (userId != null) queryParams['user_id'] = userId;
      if (action != null) queryParams['action'] = action;

      final uri = Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/audit-logs')
          .replace(queryParameters: queryParams);

      final response = await _client.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['logs'] ?? []);
      } else {
        throw Exception('Failed to get audit logs: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error getting audit logs: $e');
    }
  }

  /// Export data for compliance (GDPR, etc.)
  Future<String> exportComplianceData(
    String organizationId, {
    required String userId,
    required String format, // 'json', 'csv', 'pdf'
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/enterprise/organizations/$organizationId/compliance/export'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'user_id': userId,
          'format': format,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['download_url'] ?? '';
      } else {
        throw Exception('Failed to export data: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error exporting compliance data: $e');
    }
  }

  // Helper Methods

  /// Get authentication token (implement based on your auth system)
  Future<String> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null && token.isNotEmpty) {
        return token;
      } else {
        throw Exception('No authentication token available');
      }
    } catch (e) {
      LoggingService.error('Error retrieving auth token', tag: 'EnterpriseService', error: e);
      throw Exception('Failed to retrieve authentication token');
    }
  }

  /// Dispose of resources
  void dispose() {
    _client.close();
  }
}
