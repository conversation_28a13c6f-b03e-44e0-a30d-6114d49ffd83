import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Logging levels
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Centralized logging service for the application
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  /// Log level threshold - only logs at or above this level will be output
  LogLevel _logLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  /// Set the minimum log level
  void setLogLevel(LogLevel level) {
    _logLevel = level;
  }

  /// Log a debug message
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an info message
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a critical error message
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _instance._log(LogLevel.critical, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Internal logging method
  void _log(LogLevel level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    // Check if this log level should be output
    if (level.index < _logLevel.index) return;

    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(8);
    final tagStr = tag != null ? '[$tag] ' : '';
    final logMessage = '$timestamp $levelStr $tagStr$message';

    // Use Flutter's developer.log for better integration with debugging tools
    developer.log(
      message,
      time: DateTime.now(),
      level: _getLogLevelValue(level),
      name: tag ?? 'App',
      error: error,
      stackTrace: stackTrace,
    );

    // Also output to console in debug mode
    if (kDebugMode) {
      switch (level) {
        case LogLevel.debug:
          debugPrint('🐛 $logMessage');
          break;
        case LogLevel.info:
          debugPrint('ℹ️ $logMessage');
          break;
        case LogLevel.warning:
          debugPrint('⚠️ $logMessage');
          break;
        case LogLevel.error:
          debugPrint('❌ $logMessage');
          if (error != null) debugPrint('   Error: $error');
          break;
        case LogLevel.critical:
          debugPrint('🚨 $logMessage');
          if (error != null) debugPrint('   Error: $error');
          if (stackTrace != null) debugPrint('   Stack: $stackTrace');
          break;
      }
    }
  }

  /// Convert LogLevel to developer.log level value
  int _getLogLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical:
        return 1200;
    }
  }

  /// Log authentication events
  static void auth(String message, {String? userId, Object? error}) {
    info(message, tag: 'Auth', error: error);
  }

  /// Log API events
  static void api(String message, {String? endpoint, Object? error}) {
    info(message, tag: 'API', error: error);
  }

  /// Log navigation events
  static void navigation(String message, {String? route}) {
    debug(message, tag: 'Navigation');
  }

  /// Log business logic events
  static void business(String message, {String? feature, Object? error}) {
    info(message, tag: feature ?? 'Business', error: error);
  }

  /// Log performance events
  static void performance(String message, {Duration? duration}) {
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    info('$message$durationStr', tag: 'Performance');
  }

  /// Log security events
  static void security(String message, {String? userId, Object? error}) {
    warning(message, tag: 'Security', error: error);
  }
}

/// Extension for easier logging from any class
extension LoggingExtension on Object {
  void logDebug(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.debug(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.info(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.warning(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.error(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }

  void logCritical(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.critical(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
}
