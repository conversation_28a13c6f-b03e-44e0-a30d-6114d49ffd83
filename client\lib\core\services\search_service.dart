import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:shared/shared.dart';
import 'api_service.dart';

/// Comprehensive search service for the Quester application
/// Provides unified search across quests, tasks, users, projects, courses, and more
class SearchService {
  final ApiService _apiService;
  final http.Client _client;
  
  // Search cache for performance
  final Map<String, SearchResult> _searchCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  SearchService({
    required ApiService apiService,
    http.Client? client,
  }) : _apiService = apiService,
       _client = client ?? http.Client();

  /// Perform unified search across all content types
  Future<SearchResult> search({
    required String query,
    List<SearchCategory>? categories,
    Map<String, dynamic>? filters,
    int page = 1,
    int limit = 20,
    SearchSortBy sortBy = SearchSortBy.relevance,
    bool useCache = true,
  }) async {
    try {
      // Check cache first
      final cacheKey = _generateCacheKey(query, categories, filters, page, limit, sortBy);
      if (useCache && _isCacheValid(cacheKey)) {
        return _searchCache[cacheKey]!;
      }

      // Perform search
      final response = await _apiService.post('/api/v1/search', body: {
        'query': query,
        'categories': categories?.map((c) => c.name).toList(),
        'filters': filters,
        'page': page,
        'limit': limit,
        'sort_by': sortBy.name,
      });

      if (response.success && response.data != null) {
        final result = SearchResult.fromJson(response.data as Map<String, dynamic>);
        
        // Cache the result
        if (useCache) {
          _searchCache[cacheKey] = result;
          _cacheTimestamps[cacheKey] = DateTime.now();
        }
        
        return result;
      } else {
        throw Exception(response.error ?? 'Search failed');
      }
    } catch (e) {
      developer.log('Search error: $e', name: 'SearchService');
      return SearchResult.empty(query);
    }
  }

  /// Search for quests specifically
  Future<List<Quest>> searchQuests({
    required String query,
    QuestStatus? status,
    QuestPriority? priority,
    List<String>? tags,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiService.get('/api/v1/search/quests', queryParameters: {
        'query': query,
        if (status != null) 'status': status.name,
        if (priority != null) 'priority': priority.name,
        if (tags != null) 'tags': tags.join(','),
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response.success && response.data != null) {
        final List<dynamic> questsData = response.data['quests'] as List<dynamic>;
        return questsData.map((json) => Quest.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      developer.log('Quest search error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Search for users
  Future<List<User>> searchUsers({
    required String query,
    UserRole? role,
    UserStatus? status,
    String? organizationId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiService.get('/api/v1/search/users', queryParameters: {
        'query': query,
        if (role != null) 'role': role.name,
        if (status != null) 'status': status.name,
        if (organizationId != null) 'organization_id': organizationId,
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response.success && response.data != null) {
        final List<dynamic> usersData = response.data['users'] as List<dynamic>;
        return usersData.map((json) => User.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      developer.log('User search error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Search for freelancing projects
  Future<List<Map<String, dynamic>>> searchProjects({
    required String query,
    String? category,
    List<String>? skills,
    String? budgetType,
    int? minBudget,
    int? maxBudget,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiService.get('/api/v1/search/projects', queryParameters: {
        'query': query,
        if (category != null) 'category': category,
        if (skills != null) 'skills': skills.join(','),
        if (budgetType != null) 'budget_type': budgetType,
        if (minBudget != null) 'min_budget': minBudget.toString(),
        if (maxBudget != null) 'max_budget': maxBudget.toString(),
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response.success && response.data != null) {
        return List<Map<String, dynamic>>.from(response.data['projects'] as List);
      }
      return [];
    } catch (e) {
      developer.log('Project search error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Search for learning courses
  Future<List<Map<String, dynamic>>> searchCourses({
    required String query,
    String? category,
    String? level,
    String? language,
    double? minRating,
    int? maxPrice,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiService.get('/api/v1/search/courses', queryParameters: {
        'query': query,
        if (category != null) 'category': category,
        if (level != null) 'level': level,
        if (language != null) 'language': language,
        if (minRating != null) 'min_rating': minRating.toString(),
        if (maxPrice != null) 'max_price': maxPrice.toString(),
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response.success && response.data != null) {
        return List<Map<String, dynamic>>.from(response.data['courses'] as List);
      }
      return [];
    } catch (e) {
      developer.log('Course search error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Get search suggestions based on partial query
  Future<List<String>> getSearchSuggestions(String partialQuery) async {
    try {
      final response = await _apiService.get('/api/v1/search/suggestions', queryParameters: {
        'query': partialQuery,
        'limit': '10',
      });

      if (response.success && response.data != null) {
        return List<String>.from(response.data['suggestions'] as List);
      }
      return [];
    } catch (e) {
      developer.log('Search suggestions error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Get popular search terms
  Future<List<String>> getPopularSearches() async {
    try {
      final response = await _apiService.get('/api/v1/search/popular');

      if (response.success && response.data != null) {
        return List<String>.from(response.data['popular_searches'] as List);
      }
      return [];
    } catch (e) {
      developer.log('Popular searches error: $e', name: 'SearchService');
      return [];
    }
  }

  /// Clear search cache
  void clearCache() {
    _searchCache.clear();
    _cacheTimestamps.clear();
  }

  /// Generate cache key for search parameters
  String _generateCacheKey(
    String query,
    List<SearchCategory>? categories,
    Map<String, dynamic>? filters,
    int page,
    int limit,
    SearchSortBy sortBy,
  ) {
    final params = {
      'query': query,
      'categories': categories?.map((c) => c.name).toList(),
      'filters': filters,
      'page': page,
      'limit': limit,
      'sort_by': sortBy.name,
    };
    return jsonEncode(params);
  }

  /// Check if cache entry is still valid
  bool _isCacheValid(String cacheKey) {
    if (!_searchCache.containsKey(cacheKey) || !_cacheTimestamps.containsKey(cacheKey)) {
      return false;
    }
    
    final timestamp = _cacheTimestamps[cacheKey]!;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  void dispose() {
    _client.close();
    clearCache();
  }
}

/// Search categories for filtering
enum SearchCategory {
  quests,
  tasks,
  users,
  projects,
  courses,
  achievements,
  organizations,
  all,
}

/// Search sorting options
enum SearchSortBy {
  relevance,
  date,
  popularity,
  rating,
  alphabetical,
}

/// Search result model
class SearchResult {
  final String query;
  final List<SearchResultItem> items;
  final int totalCount;
  final int page;
  final int limit;
  final bool hasMore;
  final Map<SearchCategory, int> categoryCounts;

  const SearchResult({
    required this.query,
    required this.items,
    required this.totalCount,
    required this.page,
    required this.limit,
    required this.hasMore,
    required this.categoryCounts,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      query: json['query'] as String,
      items: (json['items'] as List<dynamic>)
          .map((item) => SearchResultItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      totalCount: json['total_count'] as int,
      page: json['page'] as int,
      limit: json['limit'] as int,
      hasMore: json['has_more'] as bool,
      categoryCounts: Map<SearchCategory, int>.from(
        (json['category_counts'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(
            SearchCategory.values.firstWhere((e) => e.name == key),
            value as int,
          ),
        ),
      ),
    );
  }

  factory SearchResult.empty(String query) {
    return SearchResult(
      query: query,
      items: [],
      totalCount: 0,
      page: 1,
      limit: 20,
      hasMore: false,
      categoryCounts: {},
    );
  }
}

/// Individual search result item
class SearchResultItem {
  final String id;
  final SearchCategory category;
  final String title;
  final String? description;
  final String? imageUrl;
  final Map<String, dynamic> metadata;
  final double relevanceScore;

  const SearchResultItem({
    required this.id,
    required this.category,
    required this.title,
    this.description,
    this.imageUrl,
    required this.metadata,
    required this.relevanceScore,
  });

  factory SearchResultItem.fromJson(Map<String, dynamic> json) {
    return SearchResultItem(
      id: json['id'] as String,
      category: SearchCategory.values.firstWhere((e) => e.name == json['category']),
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      relevanceScore: (json['relevance_score'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
