import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Secure storage wrapper for authentication tokens
/// Provides encryption for sensitive data stored locally
class SecureStorage {
  static const String _keyPrefix = 'secure_';
  final String _encryptionKey;

  SecureStorage(this._encryptionKey);

  /// Store encrypted value
  Future<bool> setSecureString(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encrypted = _encrypt(value);
      return await prefs.setString('$_keyPrefix$key', encrypted);
    } catch (e) {
      Logger.error('Error storing secure string', tag: 'SecureStorage', error: e);
      return false;
    }
  }

  /// Retrieve and decrypt value
  Future<String?> getSecureString(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encrypted = prefs.getString('$_keyPrefix$key');
      if (encrypted == null) return null;
      return _decrypt(encrypted);
    } catch (e) {
      Logger.error('Error retrieving secure string', tag: 'SecureStorage', error: e);
      return null;
    }
  }

  /// Remove secure value
  Future<bool> removeSecure(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove('$_keyPrefix$key');
    } catch (e) {
      Logger.error('Error removing secure string', tag: 'SecureStorage', error: e);
      return false;
    }
  }

  /// Clear all secure values
  Future<void> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys()
          .where((key) => key.startsWith(_keyPrefix))
          .toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      Logger.error('Error clearing secure storage', tag: 'SecureStorage', error: e);
    }
  }

  /// Simple XOR encryption (for demonstration - use proper encryption in production)
  String _encrypt(String value) {
    final valueBytes = utf8.encode(value);
    final keyBytes = _getKeyBytes();
    final encrypted = Uint8List(valueBytes.length);
    
    for (int i = 0; i < valueBytes.length; i++) {
      encrypted[i] = valueBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    return base64.encode(encrypted);
  }

  /// Simple XOR decryption
  String _decrypt(String encryptedValue) {
    final encryptedBytes = base64.decode(encryptedValue);
    final keyBytes = _getKeyBytes();
    final decrypted = Uint8List(encryptedBytes.length);
    
    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    return utf8.decode(decrypted);
  }

  Uint8List _getKeyBytes() {
    // Create a consistent key from the encryption key
    return Uint8List.fromList(sha256.convert(utf8.encode(_encryptionKey)).bytes);
  }

  /// Create secure storage instance with device-specific key
  static Future<SecureStorage> create() async {
    // In a real app, you'd use device-specific identifiers
    // For now, use a simple approach
    const deviceKey = 'quester_device_key_v1';
    return SecureStorage(deviceKey);
  }
}

/// Enhanced auth service with secure storage
class SecureAuthStorage {
  static SecureStorage? _storage;
  
  static Future<SecureStorage> _getStorage() async {
    _storage ??= await SecureStorage.create();
    return _storage!;
  }

  /// Store authentication session securely
  static Future<bool> storeSession(String token, String refreshToken, String userData) async {
    final storage = await _getStorage();
    
    final results = await Future.wait([
      storage.setSecureString('auth_token', token),
      storage.setSecureString('refresh_token', refreshToken),
      storage.setSecureString('user_data', userData),
    ]);
    
    return results.every((result) => result);
  }

  /// Retrieve authentication session
  static Future<Map<String, String?>> getSession() async {
    final storage = await _getStorage();
    
    final results = await Future.wait([
      storage.getSecureString('auth_token'),
      storage.getSecureString('refresh_token'),
      storage.getSecureString('user_data'),
    ]);
    
    return {
      'token': results[0],
      'refreshToken': results[1],
      'userData': results[2],
    };
  }

  /// Clear stored session
  static Future<void> clearSession() async {
    final storage = await _getStorage();
    await Future.wait([
      storage.removeSecure('auth_token'),
      storage.removeSecure('refresh_token'),
      storage.removeSecure('user_data'),
    ]);
  }
}
