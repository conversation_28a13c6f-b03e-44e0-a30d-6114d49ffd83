import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'api_service.dart';

/// Data synchronization service for offline support and data consistency
/// Handles caching, offline operations, and background sync
class SyncService {
  final ApiService _apiService;
  final SharedPreferences _prefs;
  final Connectivity _connectivity;
  
  // Sync state
  bool _isOnline = true;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  
  // Pending operations queue
  final List<PendingOperation> _pendingOperations = [];
  
  // Stream controllers
  final StreamController<SyncStatus> _syncStatusController = StreamController<SyncStatus>.broadcast();
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  
  // Cache keys
  static const String _lastSyncKey = 'last_sync_time';
  static const String _pendingOpsKey = 'pending_operations';
  static const String _cachedDataPrefix = 'cached_';
  
  SyncService({
    required ApiService apiService,
    required SharedPreferences prefs,
    Connectivity? connectivity,
  }) : _apiService = apiService,
       _prefs = prefs,
       _connectivity = connectivity ?? Connectivity() {
    _initializeConnectivityListener();
    _loadPendingOperations();
    _loadLastSyncTime();
  }

  /// Stream of sync status updates
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;
  
  /// Stream of connectivity status
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  /// Current online status
  bool get isOnline => _isOnline;
  
  /// Current sync status
  bool get isSyncing => _isSyncing;
  
  /// Last sync time
  DateTime? get lastSyncTime => _lastSyncTime;
  
  /// Number of pending operations
  int get pendingOperationsCount => _pendingOperations.length;

  /// Initialize connectivity listener
  void _initializeConnectivityListener() {
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> result) {
      final wasOnline = _isOnline;
      _isOnline = !result.contains(ConnectivityResult.none);
      
      _connectivityController.add(_isOnline);
      
      if (!wasOnline && _isOnline) {
        // Came back online, trigger sync
        developer.log('Connection restored, triggering sync', name: 'SyncService');
        syncPendingOperations();
      }
    });
  }

  /// Cache data locally
  Future<void> cacheData(String key, Map<String, dynamic> data) async {
    try {
      final cacheKey = '$_cachedDataPrefix$key';
      final jsonData = jsonEncode({
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      await _prefs.setString(cacheKey, jsonData);
      developer.log('Data cached for key: $key', name: 'SyncService');
    } catch (e) {
      developer.log('Error caching data for key $key: $e', name: 'SyncService');
    }
  }

  /// Retrieve cached data
  Future<Map<String, dynamic>?> getCachedData(String key, {Duration? maxAge}) async {
    try {
      final cacheKey = '$_cachedDataPrefix$key';
      final jsonData = _prefs.getString(cacheKey);
      
      if (jsonData == null) return null;
      
      final cachedItem = jsonDecode(jsonData) as Map<String, dynamic>;
      final timestamp = DateTime.parse(cachedItem['timestamp'] as String);
      
      // Check if data is too old
      if (maxAge != null && DateTime.now().difference(timestamp) > maxAge) {
        await _prefs.remove(cacheKey);
        return null;
      }
      
      return cachedItem['data'] as Map<String, dynamic>;
    } catch (e) {
      developer.log('Error retrieving cached data for key $key: $e', name: 'SyncService');
      return null;
    }
  }

  /// Add operation to pending queue
  Future<void> addPendingOperation(PendingOperation operation) async {
    _pendingOperations.add(operation);
    await _savePendingOperations();
    
    developer.log('Added pending operation: ${operation.type}', name: 'SyncService');
    
    // Try to sync immediately if online
    if (_isOnline && !_isSyncing) {
      syncPendingOperations();
    }
  }

  /// Sync all pending operations
  Future<void> syncPendingOperations() async {
    if (!_isOnline || _isSyncing || _pendingOperations.isEmpty) {
      return;
    }

    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);
    
    developer.log('Starting sync of ${_pendingOperations.length} operations', name: 'SyncService');

    final failedOperations = <PendingOperation>[];
    
    for (final operation in List.from(_pendingOperations)) {
      try {
        final success = await _executeOperation(operation);
        
        if (success) {
          _pendingOperations.remove(operation);
          developer.log('Successfully synced operation: ${operation.id}', name: 'SyncService');
        } else {
          failedOperations.add(operation);
        }
      } catch (e) {
        developer.log('Failed to sync operation ${operation.id}: $e', name: 'SyncService');
        failedOperations.add(operation);
      }
    }

    await _savePendingOperations();
    
    _lastSyncTime = DateTime.now();
    await _saveLastSyncTime();
    
    _isSyncing = false;
    
    if (failedOperations.isEmpty) {
      _syncStatusController.add(SyncStatus.success);
      developer.log('Sync completed successfully', name: 'SyncService');
    } else {
      _syncStatusController.add(SyncStatus.partialFailure);
      developer.log('Sync completed with ${failedOperations.length} failures', name: 'SyncService');
    }
  }

  /// Execute a pending operation
  Future<bool> _executeOperation(PendingOperation operation) async {
    try {
      ApiResponse response;
      
      switch (operation.method) {
        case 'GET':
          response = await _apiService.get(operation.endpoint);
          break;
        case 'POST':
          response = await _apiService.post(operation.endpoint, body: operation.data);
          break;
        case 'PUT':
          // Add PUT method to ApiService if needed
          response = await _apiService.post(operation.endpoint, body: operation.data);
          break;
        case 'DELETE':
          // Add DELETE method to ApiService if needed
          response = await _apiService.post(operation.endpoint);
          break;
        default:
          throw Exception('Unsupported HTTP method: ${operation.method}');
      }
      
      return response.success;
    } catch (e) {
      developer.log('Error executing operation ${operation.id}: $e', name: 'SyncService');
      return false;
    }
  }

  /// Force full sync
  Future<void> forceSyncAll() async {
    if (!_isOnline) {
      throw Exception('Cannot sync while offline');
    }

    _syncStatusController.add(SyncStatus.syncing);
    
    try {
      // Sync user data
      await _syncUserData();
      
      // Sync quests
      await _syncQuests();
      
      // Sync achievements
      await _syncAchievements();
      
      // Sync pending operations
      await syncPendingOperations();
      
      _lastSyncTime = DateTime.now();
      await _saveLastSyncTime();
      
      _syncStatusController.add(SyncStatus.success);
      developer.log('Full sync completed successfully', name: 'SyncService');
    } catch (e) {
      _syncStatusController.add(SyncStatus.error);
      developer.log('Full sync failed: $e', name: 'SyncService');
      rethrow;
    }
  }

  /// Sync user data
  Future<void> _syncUserData() async {
    try {
      final response = await _apiService.get('/api/v1/user/profile');
      if (response.success && response.data != null) {
        await cacheData('user_profile', response.data as Map<String, dynamic>);
      }
    } catch (e) {
      developer.log('Error syncing user data: $e', name: 'SyncService');
    }
  }

  /// Sync quests
  Future<void> _syncQuests() async {
    try {
      final response = await _apiService.get('/api/v1/quests');
      if (response.success && response.data != null) {
        await cacheData('user_quests', response.data as Map<String, dynamic>);
      }
    } catch (e) {
      developer.log('Error syncing quests: $e', name: 'SyncService');
    }
  }

  /// Sync achievements
  Future<void> _syncAchievements() async {
    try {
      final response = await _apiService.get('/api/v1/achievements');
      if (response.success && response.data != null) {
        await cacheData('user_achievements', response.data as Map<String, dynamic>);
      }
    } catch (e) {
      developer.log('Error syncing achievements: $e', name: 'SyncService');
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cachedDataPrefix));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    developer.log('Cache cleared', name: 'SyncService');
  }

  /// Load pending operations from storage
  Future<void> _loadPendingOperations() async {
    try {
      final jsonData = _prefs.getString(_pendingOpsKey);
      if (jsonData != null) {
        final List<dynamic> opsData = jsonDecode(jsonData) as List<dynamic>;
        _pendingOperations.clear();
        _pendingOperations.addAll(
          opsData.map((data) => PendingOperation.fromJson(data as Map<String, dynamic>)),
        );
        developer.log('Loaded ${_pendingOperations.length} pending operations', name: 'SyncService');
      }
    } catch (e) {
      developer.log('Error loading pending operations: $e', name: 'SyncService');
    }
  }

  /// Save pending operations to storage
  Future<void> _savePendingOperations() async {
    try {
      final jsonData = jsonEncode(_pendingOperations.map((op) => op.toJson()).toList());
      await _prefs.setString(_pendingOpsKey, jsonData);
    } catch (e) {
      developer.log('Error saving pending operations: $e', name: 'SyncService');
    }
  }

  /// Load last sync time
  void _loadLastSyncTime() {
    final timestamp = _prefs.getString(_lastSyncKey);
    if (timestamp != null) {
      _lastSyncTime = DateTime.parse(timestamp);
    }
  }

  /// Save last sync time
  Future<void> _saveLastSyncTime() async {
    if (_lastSyncTime != null) {
      await _prefs.setString(_lastSyncKey, _lastSyncTime!.toIso8601String());
    }
  }

  /// Dispose resources
  void dispose() {
    _syncStatusController.close();
    _connectivityController.close();
  }
}

/// Sync status enumeration
enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  partialFailure,
}

/// Pending operation model
class PendingOperation {
  final String id;
  final String type;
  final String method;
  final String endpoint;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final int retryCount;

  const PendingOperation({
    required this.id,
    required this.type,
    required this.method,
    required this.endpoint,
    this.data,
    required this.createdAt,
    this.retryCount = 0,
  });

  factory PendingOperation.fromJson(Map<String, dynamic> json) {
    return PendingOperation(
      id: json['id'] as String,
      type: json['type'] as String,
      method: json['method'] as String,
      endpoint: json['endpoint'] as String,
      data: json['data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      retryCount: json['retry_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'method': method,
      'endpoint': endpoint,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'retry_count': retryCount,
    };
  }

  PendingOperation copyWithRetry() {
    return PendingOperation(
      id: id,
      type: type,
      method: method,
      endpoint: endpoint,
      data: data,
      createdAt: createdAt,
      retryCount: retryCount + 1,
    );
  }
}
