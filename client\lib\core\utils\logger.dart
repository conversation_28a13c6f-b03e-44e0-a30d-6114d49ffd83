import 'dart:developer' as dev_tools;

/// Simple logging utility with configurable levels for development and production
class Logger {
  static const String _name = 'Quester';
  
  /// Enable/disable logging (set to false in production)
  static bool _isEnabled = true;
  
  /// Current log level (higher numbers mean more verbose)
  static LogLevel _currentLevel = LogLevel.info;
  
  /// Configure logger for environment
  static void configure({
    bool enabled = true,
    LogLevel level = LogLevel.info,
  }) {
    _isEnabled = enabled;
    _currentLevel = level;
  }
  
  /// Log info message
  static void info(String message, {String? tag}) {
    _log(LogLevel.info, message, tag: tag);
  }
  
  /// Log warning message
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Log error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Log debug message (only shown in debug mode)
  static void debug(String message, {String? tag}) {
    _log(LogLevel.debug, message, tag: tag);
  }
  
  /// Internal logging method
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!_isEnabled || level.value < _currentLevel.value) {
      return;
    }
    
    final String prefix = tag != null ? '[$tag] ' : '';
    final String formattedMessage = '$prefix$message';
    
    // Use dart:developer for better integration with debugging tools
    dev_tools.log(
      formattedMessage,
      name: _name,
      level: level.value,
      error: error,
      stackTrace: stackTrace,
    );
  }
}

/// Log levels with corresponding values
enum LogLevel {
  debug(700),
  info(800),
  warning(900),
  error(1000);
  
  const LogLevel(this.value);
  final int value;
}