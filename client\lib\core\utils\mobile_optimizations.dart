import 'package:flutter/material.dart';

class MobileOptimizations {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  
  /// Check if current screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  /// Check if current screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  /// Check if current screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  /// Get appropriate padding based on screen size
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    } else if (isTablet(context)) {
      return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    } else {
      return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }
  
  /// Get appropriate grid column count based on screen size
  static int getGridColumns(BuildContext context) {
    if (isMobile(context)) {
      return 1;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 3;
    }
  }
  
  /// Get appropriate card height based on screen size
  static double getCardHeight(BuildContext context) {
    if (isMobile(context)) {
      return 180;
    } else if (isTablet(context)) {
      return 200;
    } else {
      return 220;
    }
  }
  
  /// Get appropriate text scale factor
  static double getTextScaleFactor(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) {
      return 0.9; // Small phones
    } else if (width > 800) {
      return 1.1; // Large screens
    } else {
      return 1.0; // Normal size
    }
  }
  
  /// Show optimized bottom sheet for mobile
  static Future<T?> showMobileBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
    bool enableDrag = true,
  }) {
    if (isMobile(context)) {
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: isScrollControlled,
        enableDrag: enableDrag,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) => child,
      );
    } else {
      // Show as dialog on larger screens
      return showDialog<T>(
        context: context,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: child,
          ),
        ),
      );
    }
  }
  
  /// Get appropriate app bar configuration
  static PreferredSizeWidget buildResponsiveAppBar({
    required BuildContext context,
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    bool automaticallyImplyLeading = true,
  }) {
    if (isMobile(context)) {
      return AppBar(
        title: Text(title),
        actions: actions,
        bottom: bottom,
        automaticallyImplyLeading: automaticallyImplyLeading,
        elevation: 0,
        scrolledUnderElevation: 4,
      );
    } else {
      return AppBar(
        title: Text(title),
        actions: actions,
        bottom: bottom,
        automaticallyImplyLeading: automaticallyImplyLeading,
        elevation: 1,
        centerTitle: false,
      );
    }
  }
  
  /// Get appropriate navigation type
  static Widget buildResponsiveNavigation({
    required BuildContext context,
    required List<NavigationItem> items,
    required int currentIndex,
    required ValueChanged<int> onItemSelected,
  }) {
    if (isMobile(context)) {
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onItemSelected,
        type: BottomNavigationBarType.fixed,
        items: items.map((item) => BottomNavigationBarItem(
          icon: Icon(item.icon),
          label: item.label,
        )).toList(),
      );
    } else {
      return NavigationRail(
        selectedIndex: currentIndex,
        onDestinationSelected: onItemSelected,
        extended: isDesktop(context),
        destinations: items.map((item) => NavigationRailDestination(
          icon: Icon(item.icon),
          label: Text(item.label),
        )).toList(),
      );
    }
  }
  
  /// Optimize list view for mobile scrolling
  static Widget buildOptimizedListView({
    required BuildContext context,
    required IndexedWidgetBuilder itemBuilder,
    required int itemCount,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding ?? getScreenPadding(context),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // Mobile optimizations
      physics: const BouncingScrollPhysics(),
      cacheExtent: isMobile(context) ? 500 : 1000,
      addAutomaticKeepAlives: !isMobile(context),
    );
  }
  
  /// Build responsive grid view
  static Widget buildResponsiveGridView({
    required BuildContext context,
    required IndexedWidgetBuilder itemBuilder,
    required int itemCount,
    ScrollController? controller,
    EdgeInsets? padding,
    double childAspectRatio = 1.0,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding ?? getScreenPadding(context),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: getGridColumns(context),
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: isMobile(context) ? 8 : 16,
        mainAxisSpacing: isMobile(context) ? 8 : 16,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      physics: const BouncingScrollPhysics(),
    );
  }
  
  /// Get appropriate FAB position
  static FloatingActionButtonLocation getFABLocation(BuildContext context) {
    if (isMobile(context)) {
      return FloatingActionButtonLocation.endFloat;
    } else {
      return FloatingActionButtonLocation.endTop;
    }
  }
  
  /// Build touch-optimized action buttons
  static Widget buildTouchOptimizedButton({
    required VoidCallback onPressed,
    required Widget child,
    double? minWidth,
    double? minHeight,
  }) {
    return SizedBox(
      width: minWidth ?? 48,
      height: minHeight ?? 48,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: child,
      ),
    );
  }
  
  /// Show context menu optimized for touch
  static Future<T?> showTouchOptimizedMenu<T>({
    required BuildContext context,
    required RelativeRect position,
    required List<PopupMenuEntry<T>> items,
  }) {
    return showMenu<T>(
      context: context,
      position: position,
      items: items,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
  
  /// Build swipe-to-action wrapper
  static Widget buildSwipeToAction({
    required Widget child,
    VoidCallback? onSwipeLeft,
    VoidCallback? onSwipeRight,
    String? leftActionLabel,
    String? rightActionLabel,
    Color? leftActionColor,
    Color? rightActionColor,
  }) {
    return Dismissible(
      key: UniqueKey(),
      background: leftActionLabel != null ? Container(
        color: leftActionColor ?? Colors.green,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: Icon(
          Icons.check,
          color: Colors.white,
        ),
      ) : null,
      secondaryBackground: rightActionLabel != null ? Container(
        color: rightActionColor ?? Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ) : null,
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd && onSwipeLeft != null) {
          onSwipeLeft();
        } else if (direction == DismissDirection.endToStart && onSwipeRight != null) {
          onSwipeRight();
        }
      },
      child: child,
    );
  }
  
  /// Build adaptive loading indicator
  static Widget buildLoadingIndicator(BuildContext context) {
    if (isMobile(context)) {
      return const Center(
        child: CircularProgressIndicator.adaptive(),
      );
    } else {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator.adaptive(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      );
    }
  }
  
  /// Build pull-to-refresh wrapper
  static Widget buildPullToRefresh({
    required Widget child,
    required Future<void> Function() onRefresh,
  }) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: child,
    );
  }
}

class NavigationItem {
  final IconData icon;
  final String label;
  
  const NavigationItem({
    required this.icon,
    required this.label,
  });
}

/// Responsive layout widget that adapts to screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    if (MobileOptimizations.isDesktop(context) && desktop != null) {
      return desktop!;
    } else if (MobileOptimizations.isTablet(context) && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

/// Adaptive text widget that scales based on screen size
class AdaptiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  
  const AdaptiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });
  
  @override
  Widget build(BuildContext context) {
    final scaleFactor = MobileOptimizations.getTextScaleFactor(context);
    
    return Text(
      text,
      style: style?.copyWith(
        fontSize: style?.fontSize != null ? style!.fontSize! * scaleFactor : null,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Safe area wrapper that adapts to screen size
class AdaptiveSafeArea extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;
  
  const AdaptiveSafeArea({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
  });
  
  @override
  Widget build(BuildContext context) {
    if (MobileOptimizations.isMobile(context)) {
      return SafeArea(
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        child: child,
      );
    } else {
      // No safe area needed on larger screens
      return child;
    }
  }
}
