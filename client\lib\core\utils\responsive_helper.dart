import 'package:flutter/widgets.dart';
import '../config/app_config.dart';
import '../enums/device_type.dart';

/// Helper class for responsive design utilities
class ResponsiveHelper {
  /// Get device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= AppConfig.largeDesktopBreakpoint) {
      return DeviceType.largeDesktop;
    } else if (width >= AppConfig.desktopBreakpoint) {
      return DeviceType.desktop;
    } else if (width >= AppConfig.tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }
  
  /// Check if device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context).isMobile;
  }
  
  /// Check if device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context).isTablet;
  }
  
  /// Check if device is desktop
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context).isDesktop;
  }
  
  /// Check if device is large desktop
  static bool isLargeDesktop(BuildContext context) {
    return getDeviceType(context).isLargeDesktop;
  }
  
  /// Get screen width
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
  
  /// Get screen height
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  
  /// Get responsive padding based on device type
  static EdgeInsets responsivePadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(24.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(32.0);
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(40.0);
    }
  }
  
  /// Get responsive margins based on device type
  static EdgeInsets responsiveMargin(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(8.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(16.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(24.0);
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(32.0);
    }
  }
  
  /// Get responsive font size
  static double responsiveFontSize(BuildContext context, double baseSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSize;
      case DeviceType.tablet:
        return baseSize * 1.1;
      case DeviceType.desktop:
        return baseSize * 1.2;
      case DeviceType.largeDesktop:
        return baseSize * 1.3;
    }
  }
  
  /// Get responsive icon size
  static double responsiveIconSize(BuildContext context, double baseSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSize;
      case DeviceType.tablet:
        return baseSize * 1.15;
      case DeviceType.desktop:
        return baseSize * 1.3;
      case DeviceType.largeDesktop:
        return baseSize * 1.5;
    }
  }
  
  /// Get responsive container constraints
  static BoxConstraints responsiveConstraints(BuildContext context) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;
    
    switch (deviceType) {
      case DeviceType.mobile:
        return BoxConstraints(maxWidth: screenWidth);
      case DeviceType.tablet:
        return BoxConstraints(maxWidth: screenWidth * 0.9);
      case DeviceType.desktop:
        return BoxConstraints(maxWidth: screenWidth * 0.8);
      case DeviceType.largeDesktop:
        return BoxConstraints(maxWidth: screenWidth * 0.7);
    }
  }
}