import 'dart:async';
import 'dart:convert';

import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/config/app_config.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../../core/services/logging_service.dart';
import '../models/notification_model.dart';
import '../models/user_model.dart';

/// WebSocket data source for real-time communication
/// Handles connection management, event streaming, and reconnection logic
class WebSocketDataSource {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  // Connection state
  bool _isConnected = false;
  bool _isConnecting = false;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 2);
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  
  // Event streams
  final StreamController<NotificationModel> _notificationController = 
      StreamController<NotificationModel>.broadcast();
  final StreamController<UserModel> _userUpdateController = 
      StreamController<UserModel>.broadcast();
  final StreamController<Map<String, dynamic>> _pointsUpdateController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _achievementController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _leaderboardController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();

  // Getters for streams
  Stream<NotificationModel> get notificationStream => _notificationController.stream;
  Stream<UserModel> get userUpdateStream => _userUpdateController.stream;
  Stream<Map<String, dynamic>> get pointsUpdateStream => _pointsUpdateController.stream;
  Stream<Map<String, dynamic>> get achievementStream => _achievementController.stream;
  Stream<Map<String, dynamic>> get leaderboardStream => _leaderboardController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect() async {
    if (_isConnected || _isConnecting) return;
    
    _isConnecting = true;
    
    try {
      Logger.info('Connecting to WebSocket: ${AppConfig.websocketUrl}', tag: 'WebSocket');
      
      _channel = IOWebSocketChannel.connect(
        Uri.parse(AppConfig.websocketUrl),
        headers: await _getHeaders(),
      );
      
      // Listen to messages
      _subscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
      
      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;
      
      _connectionController.add(true);
      _startHeartbeat();
      
      Logger.info('WebSocket connected successfully', tag: 'WebSocket');
      
      // Send initial connection message
      _sendConnectionMessage();
      
    } catch (e) {
      Logger.error('WebSocket connection failed', tag: 'WebSocket', error: e);
      _isConnecting = false;
      _connectionController.add(false);
      _scheduleReconnect();
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    Logger.info('Disconnecting WebSocket...', tag: 'WebSocket');
    
    _stopHeartbeat();
    _stopReconnectTimer();
    
    await _subscription?.cancel();
    await _channel?.sink.close();
    
    _isConnected = false;
    _isConnecting = false;
    _reconnectAttempts = 0;
    
    _connectionController.add(false);
    
    Logger.info('WebSocket disconnected', tag: 'WebSocket');
  }

  /// Send message to server
  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      Logger.warning('Cannot send message: WebSocket not connected', tag: 'WebSocket');
      return;
    }
    
    try {
      final jsonMessage = jsonEncode(message);
      _channel!.sink.add(jsonMessage);
      Logger.debug('Sent WebSocket message: ${message['type']}', tag: 'WebSocket');
    } catch (e) {
      Logger.error('Failed to send WebSocket message', tag: 'WebSocket', error: e);
    }
  }

  /// Subscribe to specific events
  void subscribeToNotifications(String userId) {
    sendMessage({
      'type': 'subscribe',
      'channel': 'notifications',
      'user_id': userId,
    });
  }

  void subscribeToUserUpdates(String userId) {
    sendMessage({
      'type': 'subscribe',
      'channel': 'user_updates',
      'user_id': userId,
    });
  }

  void subscribeToLeaderboard() {
    sendMessage({
      'type': 'subscribe',
      'channel': 'leaderboard',
    });
  }

  /// Handle incoming messages
  void _handleMessage(dynamic message) {
    try {
      final Map<String, dynamic> data = jsonDecode(message as String);
      final eventType = data['type'] as String?;
      
      Logger.debug('Received WebSocket message: $eventType', tag: 'WebSocket');
      
      switch (eventType) {
        case AppConstants.notificationEvent:
          _handleNotificationEvent(data);
          break;
        case AppConstants.userActivityEvent:
          _handleUserUpdateEvent(data);
          break;
        case AppConstants.pointsUpdatedEvent:
          _handlePointsUpdateEvent(data);
          break;
        case AppConstants.achievementUnlockedEvent:
          _handleAchievementEvent(data);
          break;
        case AppConstants.leaderboardUpdatedEvent:
          _handleLeaderboardEvent(data);
          break;
        case 'pong':
          _handlePong();
          break;
        case 'connection_ack':
          _handleConnectionAck(data);
          break;
        case 'error':
          _handleServerError(data);
          break;
        default:
          Logger.warning('Unknown WebSocket message type: $eventType', tag: 'WebSocket');
      }
    } catch (e) {
      Logger.error('Error parsing WebSocket message', tag: 'WebSocket', error: e);
    }
  }

  /// Handle notification events
  void _handleNotificationEvent(Map<String, dynamic> data) {
    try {
      final notificationData = data['notification'] as Map<String, dynamic>;
      final notification = NotificationModel.fromJson(notificationData);
      _notificationController.add(notification);
    } catch (e) {
      Logger.error('Error handling notification event', tag: 'WebSocket', error: e);
    }
  }

  /// Handle user update events
  void _handleUserUpdateEvent(Map<String, dynamic> data) {
    try {
      final userData = data['user'] as Map<String, dynamic>;
      final user = UserModel.fromJson(userData);
      _userUpdateController.add(user);
    } catch (e) {
      Logger.error('Error handling user update event', tag: 'WebSocket', error: e);
    }
  }

  /// Handle points update events
  void _handlePointsUpdateEvent(Map<String, dynamic> data) {
    _pointsUpdateController.add(data);
  }

  /// Handle achievement events
  void _handleAchievementEvent(Map<String, dynamic> data) {
    _achievementController.add(data);
  }

  /// Handle leaderboard events
  void _handleLeaderboardEvent(Map<String, dynamic> data) {
    _leaderboardController.add(data);
  }

  /// Handle pong response
  void _handlePong() {
    Logger.debug('Received pong from server', tag: 'WebSocket');
  }

  /// Handle connection acknowledgment
  void _handleConnectionAck(Map<String, dynamic> data) {
    Logger.info('Connection acknowledged by server', tag: 'WebSocket');
  }

  /// Handle server errors
  void _handleServerError(Map<String, dynamic> data) {
    final error = data['message'] as String? ?? 'Unknown server error';
    Logger.error('Server error: $error', tag: 'WebSocket');
  }

  /// Handle WebSocket errors
  void _handleError(error) {
    Logger.error('WebSocket error', tag: 'WebSocket', error: error);
    _connectionController.add(false);
    _scheduleReconnect();
  }

  /// Handle WebSocket disconnection
  void _handleDisconnection() {
    Logger.info('WebSocket disconnected', tag: 'WebSocket');
    _isConnected = false;
    _stopHeartbeat();
    _connectionController.add(false);
    
    if (_reconnectAttempts < _maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectTimer?.isActive == true) return;
    
    _reconnectAttempts++;
    final delay = _reconnectDelay * _reconnectAttempts;
    
    Logger.info('Scheduling reconnect attempt $_reconnectAttempts in ${delay.inSeconds}s', tag: 'WebSocket');
    
    _reconnectTimer = Timer(delay, () {
      if (_reconnectAttempts <= _maxReconnectAttempts) {
        connect();
      } else {
        Logger.warning('Max reconnection attempts reached', tag: 'WebSocket');
      }
    });
  }

  /// Stop reconnect timer
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Start heartbeat timer
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_isConnected) {
        sendMessage({'type': 'ping'});
      }
    });
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Send initial connection message
  void _sendConnectionMessage() {
    sendMessage({
      'type': 'connection',
      'client': 'flutter_web',
      'version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Get headers for WebSocket connection
  Future<Map<String, String>> _getHeaders() async {
    final headers = {
      'User-Agent': 'Quester-Flutter-Client/1.0.0',
      'Origin': 'http://localhost:3000',
    };

    // Add authentication token if available
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      LoggingService.warning('Failed to retrieve auth token for WebSocket', tag: 'WebSocketDataSource', error: e);
    }

    return headers;
  }

  /// Dispose of resources
  void dispose() {
    disconnect();
    _notificationController.close();
    _userUpdateController.close();
    _pointsUpdateController.close();
    _achievementController.close();
    _leaderboardController.close();
    _connectionController.close();
  }
}