import 'package:equatable/equatable.dart';

/// Notification types
enum NotificationType {
  achievement,
  pointsAwarded,
  questCompleted,
  levelUp,
  teamInvite,
  mention,
  system,
  reminder,
}

/// Notification priority levels
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// Notification model with real-time support
class NotificationModel extends Equatable {
  final String id;
  final String userId;
  final NotificationType type;
  final NotificationPriority priority;
  final String title;
  final String message;
  final String? imageUrl;
  final String? actionUrl;
  final Map<String, dynamic>? data;
  final bool isRead;
  final bool isArchived;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? expiresAt;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.title,
    required this.message,
    this.imageUrl,
    this.actionUrl,
    this.data,
    this.isRead = false,
    this.isArchived = false,
    required this.createdAt,
    this.readAt,
    this.expiresAt,
  });

  /// Create notification from JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: _parseNotificationType(json['type'] as String),
      priority: _parseNotificationPriority(json['priority'] as String?),
      title: json['title'] as String,
      message: json['message'] as String,
      imageUrl: json['image_url'] as String?,
      actionUrl: json['action_url'] as String?,
      data: json['data'] as Map<String, dynamic>?,
      isRead: json['is_read'] as bool? ?? false,
      isArchived: json['is_archived'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt: json['read_at'] != null
          ? DateTime.parse(json['read_at'] as String)
          : null,
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'] as String)
          : null,
    );
  }

  /// Convert notification to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.name,
      'priority': priority.name,
      'title': title,
      'message': message,
      'image_url': imageUrl,
      'action_url': actionUrl,
      'data': data,
      'is_read': isRead,
      'is_archived': isArchived,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  NotificationModel copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    NotificationPriority? priority,
    String? title,
    String? message,
    String? imageUrl,
    String? actionUrl,
    Map<String, dynamic>? data,
    bool? isRead,
    bool? isArchived,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      isArchived: isArchived ?? this.isArchived,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// Mark notification as read
  NotificationModel markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  /// Check if notification is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Get time since notification was created
  Duration get timeSinceCreated {
    return DateTime.now().difference(createdAt);
  }

  /// Get formatted time ago string
  String get timeAgo {
    final duration = timeSinceCreated;
    
    if (duration.inDays > 7) {
      return '${duration.inDays ~/ 7}w ago';
    } else if (duration.inDays > 0) {
      return '${duration.inDays}d ago';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get notification icon based on type
  String get iconName {
    switch (type) {
      case NotificationType.achievement:
        return 'trophy';
      case NotificationType.pointsAwarded:
        return 'stars';
      case NotificationType.questCompleted:
        return 'flag';
      case NotificationType.levelUp:
        return 'trending_up';
      case NotificationType.teamInvite:
        return 'group_add';
      case NotificationType.mention:
        return 'alternate_email';
      case NotificationType.system:
        return 'info';
      case NotificationType.reminder:
        return 'schedule';
    }
  }

  /// Get notification color based on type and priority
  String get colorHex {
    switch (priority) {
      case NotificationPriority.urgent:
        return '#EF4444'; // Red
      case NotificationPriority.high:
        return '#F59E0B'; // Orange
      case NotificationPriority.normal:
        return '#6366F1'; // Indigo
      case NotificationPriority.low:
        return '#6B7280'; // Gray
    }
  }

  /// Parse notification type from string
  static NotificationType _parseNotificationType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'achievement':
        return NotificationType.achievement;
      case 'points_awarded':
      case 'pointsawarded':
        return NotificationType.pointsAwarded;
      case 'quest_completed':
      case 'questcompleted':
        return NotificationType.questCompleted;
      case 'level_up':
      case 'levelup':
        return NotificationType.levelUp;
      case 'team_invite':
      case 'teaminvite':
        return NotificationType.teamInvite;
      case 'mention':
        return NotificationType.mention;
      case 'system':
        return NotificationType.system;
      case 'reminder':
        return NotificationType.reminder;
      default:
        return NotificationType.system;
    }
  }

  /// Parse notification priority from string
  static NotificationPriority _parseNotificationPriority(String? priorityString) {
    if (priorityString == null) return NotificationPriority.normal;
    
    switch (priorityString.toLowerCase()) {
      case 'urgent':
        return NotificationPriority.urgent;
      case 'high':
        return NotificationPriority.high;
      case 'normal':
        return NotificationPriority.normal;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.normal;
    }
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        priority,
        title,
        message,
        imageUrl,
        actionUrl,
        data,
        isRead,
        isArchived,
        createdAt,
        readAt,
        expiresAt,
      ];
}