import 'package:equatable/equatable.dart';

/// User model with real-time data support
class UserModel extends Equatable {
  final String id;
  final String username;
  final String email;
  final String? displayName;
  final String? profileImageUrl;
  final int totalPoints;
  final int availablePoints;
  final int currentLevel;
  final double levelProgress;
  final int currentStreak;
  final int longestStreak;
  final String role;
  final bool isOnline;
  final DateTime? lastSeen;
  final Map<String, dynamic>? preferences;
  final List<String> achievements;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.username,
    required this.email,
    this.displayName,
    this.profileImageUrl,
    this.totalPoints = 0,
    this.availablePoints = 0,
    this.currentLevel = 1,
    this.levelProgress = 0.0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.role = 'user',
    this.isOnline = false,
    this.lastSeen,
    this.preferences,
    this.achievements = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create user from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      displayName: json['display_name'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      totalPoints: json['total_points'] as int? ?? 0,
      availablePoints: json['available_points'] as int? ?? 0,
      currentLevel: json['current_level'] as int? ?? 1,
      levelProgress: (json['level_progress'] as num?)?.toDouble() ?? 0.0,
      currentStreak: json['current_streak'] as int? ?? 0,
      longestStreak: json['longest_streak'] as int? ?? 0,
      role: json['role'] as String? ?? 'user',
      isOnline: json['is_online'] as bool? ?? false,
      lastSeen: json['last_seen'] != null 
          ? DateTime.parse(json['last_seen'] as String)
          : null,
      preferences: json['preferences'] as Map<String, dynamic>?,
      achievements: (json['achievements'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert user to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'display_name': displayName,
      'profile_image_url': profileImageUrl,
      'total_points': totalPoints,
      'available_points': availablePoints,
      'current_level': currentLevel,
      'level_progress': levelProgress,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'role': role,
      'is_online': isOnline,
      'last_seen': lastSeen?.toIso8601String(),
      'preferences': preferences,
      'achievements': achievements,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    String? displayName,
    String? profileImageUrl,
    int? totalPoints,
    int? availablePoints,
    int? currentLevel,
    double? levelProgress,
    int? currentStreak,
    int? longestStreak,
    String? role,
    bool? isOnline,
    DateTime? lastSeen,
    Map<String, dynamic>? preferences,
    List<String>? achievements,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      totalPoints: totalPoints ?? this.totalPoints,
      availablePoints: availablePoints ?? this.availablePoints,
      currentLevel: currentLevel ?? this.currentLevel,
      levelProgress: levelProgress ?? this.levelProgress,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      role: role ?? this.role,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      preferences: preferences ?? this.preferences,
      achievements: achievements ?? this.achievements,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get display name or fallback to username
  String get displayNameOrUsername => displayName ?? username;

  /// Check if user is premium/pro
  bool get isPremium => role == 'premium' || role == 'pro';

  /// Get points to next level
  int get pointsToNextLevel {
    final nextLevelPoints = _getPointsForLevel(currentLevel + 1);
    return nextLevelPoints - totalPoints;
  }

  /// Get percentage progress to next level
  double get levelProgressPercentage {
    final nextLevelPoints = _getPointsForLevel(currentLevel + 1);
    final currentLevelPoints = _getPointsForLevel(currentLevel);
    final pointsInCurrentLevel = totalPoints - currentLevelPoints;
    final pointsNeededForLevel = nextLevelPoints - currentLevelPoints;
    
    if (pointsNeededForLevel <= 0) return 1.0;
    return (pointsInCurrentLevel / pointsNeededForLevel).clamp(0.0, 1.0);
  }

  /// Calculate points needed for a specific level
  int _getPointsForLevel(int level) {
    if (level <= 1) return 0;
    // Exponential point scaling: level 2 = 200, level 3 = 500, level 4 = 1000, etc.
    return ((level - 1) * (level - 1) * 200).round();
  }

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        displayName,
        profileImageUrl,
        totalPoints,
        availablePoints,
        currentLevel,
        levelProgress,
        currentStreak,
        longestStreak,
        role,
        isOnline,
        lastSeen,
        preferences,
        achievements,
        createdAt,
        updatedAt,
      ];
}