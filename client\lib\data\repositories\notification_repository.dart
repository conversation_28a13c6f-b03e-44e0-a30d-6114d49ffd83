import 'dart:convert';
import 'dart:async';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/config/app_config.dart';
import '../../core/services/logging_service.dart';
import '../models/notification_model.dart';
import '../datasources/websocket_datasource.dart';

/// Repository for notification data management
/// Handles API calls and real-time updates via WebSocket
class NotificationRepository {
  final http.Client _httpClient;
  final WebSocketDataSource _webSocketDataSource;
  
  NotificationRepository({
    required http.Client httpClient,
    required WebSocketDataSource webSocketDataSource,
  }) : _httpClient = httpClient,
       _webSocketDataSource = webSocketDataSource;

  /// Get notifications stream from WebSocket
  Stream<NotificationModel> get notificationStream => 
      _webSocketDataSource.notificationStream;

  /// Get notifications from API
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int limit = 20,
    String? type,
    bool unreadOnly = false,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        if (type != null) 'type': type,
        if (unreadOnly) 'unread_only': 'true',
      };
      
      final uri = Uri.parse(
        '${AppConfig.baseUrl}/api/v1/user/notifications',
      ).replace(queryParameters: queryParams);
      
      final response = await _httpClient.get(
        uri,
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final notificationsJson = data['notifications'] as List<dynamic>;
        
        return notificationsJson
            .map((json) => NotificationModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to load notifications: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching notifications: $e');
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/notifications/$notificationId/read'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to mark notification as read: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error marking notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/notifications/read-all'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to mark all notifications as read: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error marking all notifications as read: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      final response = await _httpClient.delete(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/notifications/$notificationId'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to delete notification: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting notification: $e');
    }
  }

  /// Get notification preferences
  Future<Map<String, dynamic>> getNotificationPreferences() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/notification-preferences'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return data['preferences'] as Map<String, dynamic>;
      } else {
        throw Exception('Failed to load notification preferences: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching notification preferences: $e');
    }
  }

  /// Update notification preferences
  Future<void> updateNotificationPreferences(Map<String, dynamic> preferences) async {
    try {
      final response = await _httpClient.put(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/notification-preferences'),
        headers: await _getHeaders(),
        body: jsonEncode({'preferences': preferences}),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to update notification preferences: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating notification preferences: $e');
    }
  }

  /// Subscribe to notifications via WebSocket
  void subscribeToNotifications(String userId) {
    _webSocketDataSource.subscribeToNotifications(userId);
  }

  /// Get headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'Quester-Flutter-Client/1.0.0',
    };

    // Add authentication token if available
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      LoggingService.warning('Failed to retrieve auth token', tag: 'NotificationRepository', error: e);
    }

    return headers;
  }
}