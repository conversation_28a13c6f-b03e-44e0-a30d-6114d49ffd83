import 'dart:convert';
import 'dart:async';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/config/app_config.dart';
import '../../core/services/logging_service.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../datasources/websocket_datasource.dart';

/// Repository for user data management
/// Handles API calls and real-time updates via WebSocket
class UserRepository {
  final http.Client _httpClient;
  final WebSocketDataSource _webSocketDataSource;
  
  UserRepository({
    required http.Client httpClient,
    required WebSocketDataSource webSocketDataSource,
  }) : _httpClient = httpClient,
       _webSocketDataSource = webSocketDataSource;

  /// Get user updates stream from WebSocket
  Stream<UserModel> get userUpdateStream => 
      _webSocketDataSource.userUpdateStream;

  /// Get current user data
  Future<UserModel?> getCurrentUser({String? userId}) async {
    try {
      final endpoint = userId != null 
          ? '/api/v1/user/$userId'
          : '/api/v1/user/me';
          
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.baseUrl}$endpoint'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return UserModel.fromJson(data['user'] as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to load user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching user: $e');
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats(String userId) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.baseUrl}/api/v1/gamification/user/$userId/stats'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('Failed to load user stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching user stats: $e');
    }
  }

  /// Update user data
  Future<UserModel> updateUser(UserModel user) async {
    try {
      final response = await _httpClient.put(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/${user.id}'),
        headers: await _getHeaders(),
        body: jsonEncode(user.toJson()),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return UserModel.fromJson(data['user'] as Map<String, dynamic>);
      } else {
        throw Exception('Failed to update user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating user: $e');
    }
  }

  /// Update user preferences
  Future<void> updateUserPreferences(String userId, Map<String, dynamic> preferences) async {
    try {
      final response = await _httpClient.put(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/$userId/preferences'),
        headers: await _getHeaders(),
        body: jsonEncode({'preferences': preferences}),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to update user preferences: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating user preferences: $e');
    }
  }

  /// Update user online status
  Future<void> updateOnlineStatus(bool isOnline) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/online-status'),
        headers: await _getHeaders(),
        body: jsonEncode({'is_online': isOnline}),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to update online status: ${response.statusCode}');
      }
    } catch (e) {
      Logger.warning('Could not update online status', tag: 'UserRepository', error: e);
      // Non-critical error, don't throw
    }
  }

  /// Get user achievements
  Future<List<Map<String, dynamic>>> getUserAchievements(String userId) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.baseUrl}/api/v1/gamification/user/$userId/achievements'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List<dynamic>;
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to load user achievements: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching user achievements: $e');
    }
  }

  /// Get user activity feed
  Future<List<Map<String, dynamic>>> getUserActivity(String userId, {int limit = 20}) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.baseUrl}/api/v1/gamification/activity/user/$userId?limit=$limit'),
        headers: await _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final activities = data['activities'] as List<dynamic>;
        return activities.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to load user activity: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching user activity: $e');
    }
  }

  /// Upload user profile image
  Future<String> uploadProfileImage(String userId, List<int> imageBytes, String fileName) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${AppConfig.baseUrl}/api/v1/user/$userId/profile-image'),
      );
      
      request.headers.addAll(await _getHeaders());
      request.files.add(
        http.MultipartFile.fromBytes(
          'image',
          imageBytes,
          filename: fileName,
        ),
      );
      
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return data['image_url'] as String;
      } else {
        throw Exception('Failed to upload profile image: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error uploading profile image: $e');
    }
  }

  /// Subscribe to user updates via WebSocket
  void subscribeToUserUpdates(String userId) {
    _webSocketDataSource.subscribeToUserUpdates(userId);
  }

  /// Clear user data (for logout)
  Future<void> clearUserData() async {
    try {
      // Clear cached user data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('refresh_token');
      await prefs.remove('user_id');
      await prefs.remove('user_profile');
      
      // Clear any other session data
      await prefs.remove('session_id');
      await prefs.remove('last_login');
      
      LoggingService.info('User data cleared successfully', tag: 'UserRepository');
    } catch (e) {
      LoggingService.error('Error clearing user data', tag: 'UserRepository', error: e);
    }
  }

  /// Get headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'Quester-Flutter-Client/1.0.0',
    };

    // Add authentication token if available
    try {
      // Get token from secure storage or preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      LoggingService.warning('Failed to retrieve auth token', tag: 'UserRepository', error: e);
    }

    return headers;
  }
}