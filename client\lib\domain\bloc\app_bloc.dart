import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

/// Application-wide state management
/// Handles global UI state like sidebars, overlays, and app-wide settings

// Events
abstract class AppEvent extends Equatable {
  const AppEvent();

  @override
  List<Object?> get props => [];
}

class ToggleNotificationSidebar extends AppEvent {
  const ToggleNotificationSidebar();
}

class OpenNotificationSidebar extends AppEvent {
  const OpenNotificationSidebar();
}

class CloseNotificationSidebar extends AppEvent {
  const CloseNotificationSidebar();
}

class ToggleUserSidebar extends AppEvent {
  const ToggleUserSidebar();
}

class OpenUserSidebar extends AppEvent {
  const OpenUserSidebar();
}

class CloseUserSidebar extends AppEvent {
  const CloseUserSidebar();
}

class UpdateThemeMode extends AppEvent {
  final bool isDarkMode;
  
  const UpdateThemeMode(this.isDarkMode);

  @override
  List<Object?> get props => [isDarkMode];
}

class UpdateConnectionStatus extends AppEvent {
  final bool isConnected;
  
  const UpdateConnectionStatus(this.isConnected);

  @override
  List<Object?> get props => [isConnected];
}

// State
class AppState extends Equatable {
  final bool isNotificationSidebarOpen;
  final bool isUserSidebarOpen;
  final bool isDarkMode;
  final bool isConnected;
  final DateTime lastUpdated;

  AppState({
    this.isNotificationSidebarOpen = false,
    this.isUserSidebarOpen = false,
    this.isDarkMode = false,
    this.isConnected = false,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.fromMicrosecondsSinceEpoch(0);

  AppState copyWith({
    bool? isNotificationSidebarOpen,
    bool? isUserSidebarOpen,
    bool? isDarkMode,
    bool? isConnected,
    DateTime? lastUpdated,
  }) {
    return AppState(
      isNotificationSidebarOpen: isNotificationSidebarOpen ?? this.isNotificationSidebarOpen,
      isUserSidebarOpen: isUserSidebarOpen ?? this.isUserSidebarOpen,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      isConnected: isConnected ?? this.isConnected,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  List<Object> get props => [
        isNotificationSidebarOpen,
        isUserSidebarOpen,
        isDarkMode,
        isConnected,
        lastUpdated,
      ];
}

// BLoC
class AppBloc extends Bloc<AppEvent, AppState> {
  AppBloc() : super(AppState()) {
    on<ToggleNotificationSidebar>(_onToggleNotificationSidebar);
    on<OpenNotificationSidebar>(_onOpenNotificationSidebar);
    on<CloseNotificationSidebar>(_onCloseNotificationSidebar);
    on<ToggleUserSidebar>(_onToggleUserSidebar);
    on<OpenUserSidebar>(_onOpenUserSidebar);
    on<CloseUserSidebar>(_onCloseUserSidebar);
    on<UpdateThemeMode>(_onUpdateThemeMode);
    on<UpdateConnectionStatus>(_onUpdateConnectionStatus);
  }

  void _onToggleNotificationSidebar(
    ToggleNotificationSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isNotificationSidebarOpen: !state.isNotificationSidebarOpen,
      isUserSidebarOpen: false, // Close user sidebar when opening notification
    ));
  }

  void _onOpenNotificationSidebar(
    OpenNotificationSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isNotificationSidebarOpen: true,
      isUserSidebarOpen: false,
    ));
  }

  void _onCloseNotificationSidebar(
    CloseNotificationSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isNotificationSidebarOpen: false,
    ));
  }

  void _onToggleUserSidebar(
    ToggleUserSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isUserSidebarOpen: !state.isUserSidebarOpen,
      isNotificationSidebarOpen: false, // Close notification sidebar when opening user
    ));
  }

  void _onOpenUserSidebar(
    OpenUserSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isUserSidebarOpen: true,
      isNotificationSidebarOpen: false,
    ));
  }

  void _onCloseUserSidebar(
    CloseUserSidebar event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isUserSidebarOpen: false,
    ));
  }

  void _onUpdateThemeMode(
    UpdateThemeMode event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isDarkMode: event.isDarkMode,
    ));
  }

  void _onUpdateConnectionStatus(
    UpdateConnectionStatus event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(
      isConnected: event.isConnected,
    ));
  }
}