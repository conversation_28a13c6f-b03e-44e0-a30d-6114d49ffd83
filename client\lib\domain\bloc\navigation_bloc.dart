import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

/// Navigation state management
/// Handles navigation between different sections of the app

// Events
abstract class NavigationEvent extends Equatable {
  const NavigationEvent();

  @override
  List<Object?> get props => [];
}

class NavigateToIndex extends NavigationEvent {
  final int index;
  
  const NavigateToIndex(this.index);

  @override
  List<Object?> get props => [index];
}

class NavigateToRoute extends NavigationEvent {
  final String route;
  
  const NavigateToRoute(this.route);

  @override
  List<Object?> get props => [route];
}

class UpdateCurrentRoute extends NavigationEvent {
  final String route;
  
  const UpdateCurrentRoute(this.route);

  @override
  List<Object?> get props => [route];
}

// State
class NavigationState extends Equatable {
  final int selectedIndex;
  final String currentRoute;
  final DateTime lastNavigated;

  NavigationState({
    this.selectedIndex = 0,
    this.currentRoute = '/',
    DateTime? lastNavigated,
  }) : lastNavigated = lastNavigated ?? DateTime.fromMicrosecondsSinceEpoch(0);

  NavigationState copyWith({
    int? selectedIndex,
    String? currentRoute,
    DateTime? lastNavigated,
  }) {
    return NavigationState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
      currentRoute: currentRoute ?? this.currentRoute,
      lastNavigated: lastNavigated ?? DateTime.now(),
    );
  }

  @override
  List<Object> get props => [
        selectedIndex,
        currentRoute,
        lastNavigated,
      ];
}

// BLoC
class NavigationBloc extends Bloc<NavigationEvent, NavigationState> {
  NavigationBloc() : super(NavigationState()) {
    on<NavigateToIndex>(_onNavigateToIndex);
    on<NavigateToRoute>(_onNavigateToRoute);
    on<UpdateCurrentRoute>(_onUpdateCurrentRoute);
  }

  void _onNavigateToIndex(
    NavigateToIndex event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(
      selectedIndex: event.index,
    ));
  }

  void _onNavigateToRoute(
    NavigateToRoute event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(
      currentRoute: event.route,
    ));
  }

  void _onUpdateCurrentRoute(
    UpdateCurrentRoute event,
    Emitter<NavigationState> emit,
  ) {
    // Update route and try to determine corresponding index
    final routeIndexMap = {
      '/': 0,
      '/quests': 1,
      '/gamification': 2,
      '/analytics': 3,
      '/enterprise': 4,
      '/settings': 5,
    };
    
    final index = routeIndexMap[event.route] ?? 0;
    
    emit(state.copyWith(
      currentRoute: event.route,
      selectedIndex: index,
    ));
  }
}