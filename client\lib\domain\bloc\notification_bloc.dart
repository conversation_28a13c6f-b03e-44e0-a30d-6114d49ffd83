import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../core/utils/logger.dart';
import '../../data/models/notification_model.dart';
import '../../data/repositories/notification_repository.dart';

/// Notification state management with real-time updates
/// Handles notification fetching, real-time updates via WebSocket, and UI state

// Events
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class LoadNotifications extends NotificationEvent {
  const LoadNotifications();
}

class RefreshNotifications extends NotificationEvent {
  const RefreshNotifications();
}

class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;
  
  const MarkNotificationAsRead(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

class DeleteNotification extends NotificationEvent {
  final String notificationId;
  
  const DeleteNotification(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

class AddNotificationFromWebSocket extends NotificationEvent {
  final NotificationModel notification;
  
  const AddNotificationFromWebSocket(this.notification);

  @override
  List<Object?> get props => [notification];
}

class UpdateNotificationFromWebSocket extends NotificationEvent {
  final NotificationModel notification;
  
  const UpdateNotificationFromWebSocket(this.notification);

  @override
  List<Object?> get props => [notification];
}

class LoadMoreNotifications extends NotificationEvent {
  const LoadMoreNotifications();
}

// State
abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object> get props => [];

  int get unreadCount => 0;
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final List<NotificationModel> notifications;
  final List<NotificationModel> recentNotifications;
  final bool hasMore;
  final bool isLoadingMore;
  final DateTime lastUpdated;

  NotificationLoaded({
    required this.notifications,
    this.recentNotifications = const [],
    this.hasMore = true,
    this.isLoadingMore = false,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.fromMicrosecondsSinceEpoch(0);

  @override
  int get unreadCount => notifications.where((n) => !n.isRead).length;

  NotificationLoaded copyWith({
    List<NotificationModel>? notifications,
    List<NotificationModel>? recentNotifications,
    bool? hasMore,
    bool? isLoadingMore,
    DateTime? lastUpdated,
  }) {
    return NotificationLoaded(
      notifications: notifications ?? this.notifications,
      recentNotifications: recentNotifications ?? this.recentNotifications,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  List<Object> get props => [
        notifications,
        recentNotifications,
        hasMore,
        isLoadingMore,
        lastUpdated,
      ];
}

class NotificationError extends NotificationState {
  final String message;
  
  const NotificationError(this.message);

  @override
  List<Object> get props => [message];
}

// BLoC
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationRepository _notificationRepository;
  int _currentPage = 1;
  static const int _pageSize = 20;

  NotificationBloc({
    required NotificationRepository notificationRepository,
  })  : _notificationRepository = notificationRepository,
        super(NotificationInitial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<RefreshNotifications>(_onRefreshNotifications);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<DeleteNotification>(_onDeleteNotification);
    on<AddNotificationFromWebSocket>(_onAddNotificationFromWebSocket);
    on<UpdateNotificationFromWebSocket>(_onUpdateNotificationFromWebSocket);
    on<LoadMoreNotifications>(_onLoadMoreNotifications);
    
    // Initialize WebSocket listener
    _initializeWebSocketListener();
  }

  void _initializeWebSocketListener() {
    // Listen to WebSocket notification stream
    _notificationRepository.notificationStream.listen(
      (notification) {
        add(AddNotificationFromWebSocket(notification));
      },
      onError: (error) {
        // Handle WebSocket errors
        Logger.error('WebSocket notification error', tag: 'NotificationBloc', error: error);
      },
    );
  }

  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());
      
      final notifications = await _notificationRepository.getNotifications(
        page: 1,
        limit: _pageSize,
      );
      
      final recentNotifications = notifications
          .where((n) => !n.isRead)
          .take(5)
          .toList();
      
      _currentPage = 1;
      
      emit(NotificationLoaded(
        notifications: notifications,
        recentNotifications: recentNotifications,
        hasMore: notifications.length >= _pageSize,
      ));
    } catch (e) {
      emit(NotificationError('Failed to load notifications: $e'));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final notifications = await _notificationRepository.getNotifications(
        page: 1,
        limit: _pageSize,
      );
      
      final recentNotifications = notifications
          .where((n) => !n.isRead)
          .take(5)
          .toList();
      
      _currentPage = 1;
      
      emit(NotificationLoaded(
        notifications: notifications,
        recentNotifications: recentNotifications,
        hasMore: notifications.length >= _pageSize,
      ));
    } catch (e) {
      emit(NotificationError('Failed to refresh notifications: $e'));
    }
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      try {
        await _notificationRepository.markAsRead(event.notificationId);
        
        final updatedNotifications = currentState.notifications.map((n) {
          if (n.id == event.notificationId) {
            return n.copyWith(isRead: true);
          }
          return n;
        }).toList();
        
        final recentNotifications = updatedNotifications
            .where((n) => !n.isRead)
            .take(5)
            .toList();
        
        emit(currentState.copyWith(
          notifications: updatedNotifications,
          recentNotifications: recentNotifications,
        ));
      } catch (e) {
        emit(NotificationError('Failed to mark notification as read: $e'));
      }
    }
  }

  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      try {
        await _notificationRepository.markAllAsRead();
        
        final updatedNotifications = currentState.notifications.map((n) {
          return n.copyWith(isRead: true);
        }).toList();
        
        emit(currentState.copyWith(
          notifications: updatedNotifications,
          recentNotifications: [],
        ));
      } catch (e) {
        emit(NotificationError('Failed to mark all notifications as read: $e'));
      }
    }
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<NotificationState> emit,
  ) async {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      try {
        await _notificationRepository.deleteNotification(event.notificationId);
        
        final updatedNotifications = currentState.notifications
            .where((n) => n.id != event.notificationId)
            .toList();
        
        final recentNotifications = updatedNotifications
            .where((n) => !n.isRead)
            .take(5)
            .toList();
        
        emit(currentState.copyWith(
          notifications: updatedNotifications,
          recentNotifications: recentNotifications,
        ));
      } catch (e) {
        emit(NotificationError('Failed to delete notification: $e'));
      }
    }
  }

  void _onAddNotificationFromWebSocket(
    AddNotificationFromWebSocket event,
    Emitter<NotificationState> emit,
  ) {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      final updatedNotifications = [event.notification, ...currentState.notifications];
      
      final recentNotifications = updatedNotifications
          .where((n) => !n.isRead)
          .take(5)
          .toList();
      
      emit(currentState.copyWith(
        notifications: updatedNotifications,
        recentNotifications: recentNotifications,
      ));
    }
  }

  void _onUpdateNotificationFromWebSocket(
    UpdateNotificationFromWebSocket event,
    Emitter<NotificationState> emit,
  ) {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      final updatedNotifications = currentState.notifications.map((n) {
        if (n.id == event.notification.id) {
          return event.notification;
        }
        return n;
      }).toList();
      
      final recentNotifications = updatedNotifications
          .where((n) => !n.isRead)
          .take(5)
          .toList();
      
      emit(currentState.copyWith(
        notifications: updatedNotifications,
        recentNotifications: recentNotifications,
      ));
    }
  }

  Future<void> _onLoadMoreNotifications(
    LoadMoreNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    if (state is NotificationLoaded) {
      final currentState = state as NotificationLoaded;
      
      if (currentState.isLoadingMore || !currentState.hasMore) {
        return;
      }
      
      emit(currentState.copyWith(isLoadingMore: true));
      
      try {
        final moreNotifications = await _notificationRepository.getNotifications(
          page: _currentPage + 1,
          limit: _pageSize,
        );
        
        final updatedNotifications = [
          ...currentState.notifications,
          ...moreNotifications,
        ];
        
        _currentPage++;
        
        emit(currentState.copyWith(
          notifications: updatedNotifications,
          isLoadingMore: false,
          hasMore: moreNotifications.length >= _pageSize,
        ));
      } catch (e) {
        emit(currentState.copyWith(isLoadingMore: false));
        emit(NotificationError('Failed to load more notifications: $e'));
      }
    }
  }
}