import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../core/utils/logger.dart';
import '../../data/models/user_model.dart';
import '../../data/repositories/user_repository.dart';

/// User state management with real-time updates
/// Handles user data, authentication state, and real-time updates via WebSocket

// Events
abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

class LoadUser extends UserEvent {
  final String? userId;
  
  const LoadUser({this.userId});

  @override
  List<Object?> get props => [userId];
}

class RefreshUser extends UserEvent {
  const RefreshUser();
}

class UpdateUser extends UserEvent {
  final UserModel user;
  
  const UpdateUser(this.user);

  @override
  List<Object?> get props => [user];
}

class UpdateUserFromWebSocket extends UserEvent {
  final UserModel user;
  
  const UpdateUserFromWebSocket(this.user);

  @override
  List<Object?> get props => [user];
}

class UpdateUserPreferences extends UserEvent {
  final Map<String, dynamic> preferences;
  
  const UpdateUserPreferences(this.preferences);

  @override
  List<Object?> get props => [preferences];
}

class UpdateUserOnlineStatus extends UserEvent {
  final bool isOnline;
  
  const UpdateUserOnlineStatus(this.isOnline);

  @override
  List<Object?> get props => [isOnline];
}

class LogoutUser extends UserEvent {
  const LogoutUser();
}

// State
abstract class UserState extends Equatable {
  const UserState();

  @override
  List<Object> get props => [];
}

class UserInitial extends UserState {}

class UserLoading extends UserState {}

class UserLoadedState extends UserState {
  final UserModel user;
  final DateTime lastUpdated;

  UserLoadedState({
    required this.user,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.fromMicrosecondsSinceEpoch(0);

  UserLoadedState copyWith({
    UserModel? user,
    DateTime? lastUpdated,
  }) {
    return UserLoadedState(
      user: user ?? this.user,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  List<Object> get props => [user, lastUpdated];
}

class UserError extends UserState {
  final String message;
  
  const UserError(this.message);

  @override
  List<Object> get props => [message];
}

class UserLoggedOut extends UserState {}

// BLoC
class UserBloc extends Bloc<UserEvent, UserState> {
  final UserRepository _userRepository;

  UserBloc({
    required UserRepository userRepository,
  })  : _userRepository = userRepository,
        super(UserInitial()) {
    on<LoadUser>(_onLoadUser);
    on<RefreshUser>(_onRefreshUser);
    on<UpdateUser>(_onUpdateUser);
    on<UpdateUserFromWebSocket>(_onUpdateUserFromWebSocket);
    on<UpdateUserPreferences>(_onUpdateUserPreferences);
    on<UpdateUserOnlineStatus>(_onUpdateUserOnlineStatus);
    on<LogoutUser>(_onLogoutUser);
    
    // Initialize WebSocket listener
    _initializeWebSocketListener();
  }

  void _initializeWebSocketListener() {
    // Listen to WebSocket user updates
    _userRepository.userUpdateStream.listen(
      (user) {
        add(UpdateUserFromWebSocket(user));
      },
      onError: (error) {
        // Handle WebSocket errors
        Logger.error('WebSocket user update error', tag: 'UserBloc', error: error);
      },
    );
  }

  Future<void> _onLoadUser(
    LoadUser event,
    Emitter<UserState> emit,
  ) async {
    try {
      emit(UserLoading());
      
      final user = await _userRepository.getCurrentUser(userId: event.userId);
      
      if (user != null) {
        emit(UserLoadedState(user: user));
      } else {
        emit(const UserError('User not found'));
      }
    } catch (e) {
      emit(UserError('Failed to load user: $e'));
    }
  }

  Future<void> _onRefreshUser(
    RefreshUser event,
    Emitter<UserState> emit,
  ) async {
    if (state is UserLoadedState) {
      final currentState = state as UserLoadedState;
      
      try {
        final user = await _userRepository.getCurrentUser(
          userId: currentState.user.id,
        );
        
        if (user != null) {
          emit(currentState.copyWith(user: user));
        }
      } catch (e) {
        emit(UserError('Failed to refresh user: $e'));
      }
    }
  }

  Future<void> _onUpdateUser(
    UpdateUser event,
    Emitter<UserState> emit,
  ) async {
    try {
      final updatedUser = await _userRepository.updateUser(event.user);
      
      emit(UserLoadedState(user: updatedUser));
    } catch (e) {
      emit(UserError('Failed to update user: $e'));
    }
  }

  void _onUpdateUserFromWebSocket(
    UpdateUserFromWebSocket event,
    Emitter<UserState> emit,
  ) {
    if (state is UserLoadedState) {
      final currentState = state as UserLoadedState;
      
      // Only update if this is the same user
      if (currentState.user.id == event.user.id) {
        emit(currentState.copyWith(user: event.user));
      }
    }
  }

  Future<void> _onUpdateUserPreferences(
    UpdateUserPreferences event,
    Emitter<UserState> emit,
  ) async {
    if (state is UserLoadedState) {
      final currentState = state as UserLoadedState;
      
      try {
        final updatedUser = currentState.user.copyWith(
          preferences: event.preferences,
          updatedAt: DateTime.now(),
        );
        
        await _userRepository.updateUserPreferences(
          currentState.user.id,
          event.preferences,
        );
        
        emit(currentState.copyWith(user: updatedUser));
      } catch (e) {
        emit(UserError('Failed to update user preferences: $e'));
      }
    }
  }

  void _onUpdateUserOnlineStatus(
    UpdateUserOnlineStatus event,
    Emitter<UserState> emit,
  ) {
    if (state is UserLoadedState) {
      final currentState = state as UserLoadedState;
      
      final updatedUser = currentState.user.copyWith(
        isOnline: event.isOnline,
        lastSeen: event.isOnline ? null : DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      emit(currentState.copyWith(user: updatedUser));
      
      // Update online status on server
      _userRepository.updateOnlineStatus(event.isOnline);
    }
  }

  void _onLogoutUser(
    LogoutUser event,
    Emitter<UserState> emit,
  ) {
    // Clear user data and emit logged out state
    _userRepository.clearUserData();
    emit(UserLoggedOut());
  }
}