import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'bloc/analytics_bloc.dart';
import 'widgets/analytics_dashboard.dart' as widgets;
import '../../core/services/api_service.dart';

/// Analytics Screen that provides access to the comprehensive analytics dashboard
/// 
/// Features:
/// - Full-screen analytics dashboard with tabbed interface
/// - Real-time data updates and WebSocket integration
/// - Time range selection and metric filtering
/// - Responsive design with drawer navigation
/// - BLoC state management with error handling
class AnalyticsScreen extends StatefulWidget {
  final String organizationId;
  final String? initialTab;

  const AnalyticsScreen({
    super.key,
    required this.organizationId,
    this.initialTab,
  });

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  late AnalyticsBloc _analyticsBloc;
  bool _isRealTimeEnabled = true;

  @override
  void initState() {
    super.initState();
    
    // Initialize analytics bloc with API service
    _analyticsBloc = AnalyticsBloc(
      apiService: context.read<ApiService>(),
    );

    // Load initial data
    _analyticsBloc.add(LoadDashboardMetrics(
      organizationId: widget.organizationId,
      timeRange: DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 7)),
        end: DateTime.now(),
      ),
    ));

    // Enable real-time updates by default
    if (_isRealTimeEnabled) {
      _analyticsBloc.add(EnableRealTimeUpdates(
        organizationId: widget.organizationId,
      ));
    }
  }

  @override
  void dispose() {
    _analyticsBloc.close();
    super.dispose();
  }

  void _toggleRealTimeUpdates() {
    setState(() {
      _isRealTimeEnabled = !_isRealTimeEnabled;
    });

    if (_isRealTimeEnabled) {
      _analyticsBloc.add(EnableRealTimeUpdates(
        organizationId: widget.organizationId,
      ));
    } else {
      _analyticsBloc.add(const DisableRealTimeUpdates());
    }
  }

  void _refreshData() {
    _analyticsBloc.add(RefreshDashboardMetrics(
      organizationId: widget.organizationId,
      timeRange: DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 7)),
        end: DateTime.now(),
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocProvider.value(
      value: _analyticsBloc,
      child: Scaffold(
        appBar: _buildAppBar(theme),
        drawer: _buildDrawer(theme),
        body: BlocBuilder<AnalyticsBloc, AnalyticsState>(
          builder: (context, state) {
            if (state is AnalyticsError) {
              return _buildErrorView(theme, state);
            }

            return widgets.AnalyticsDashboard(
              organizationId: widget.organizationId,
              initialTimeRange: DateTimeRange(
                start: DateTime.now().subtract(const Duration(days: 7)),
                end: DateTime.now(),
              ),
              showFilters: true,
              enableRealTime: _isRealTimeEnabled,
            );
          },
        ),
        floatingActionButton: _buildFloatingActionButton(theme),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: Row(
        children: [
          Icon(
            Icons.analytics,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('Analytics Dashboard'),
        ],
      ),
      actions: [
        // Real-time toggle
        BlocBuilder<AnalyticsBloc, AnalyticsState>(
          builder: (context, state) {
            final isConnected = state is AnalyticsLoaded ? state.isConnected : false;
            
            return IconButton(
              icon: Icon(
                _isRealTimeEnabled 
                    ? (isConnected ? Icons.wifi : Icons.wifi_off)
                    : Icons.pause_circle_outline,
                color: _isRealTimeEnabled
                    ? (isConnected ? Colors.green : Colors.red)
                    : theme.colorScheme.onSurfaceVariant,
              ),
              onPressed: _toggleRealTimeUpdates,
              tooltip: _isRealTimeEnabled 
                  ? (isConnected ? 'Real-time ON' : 'Connection Lost')
                  : 'Real-time OFF',
            );
          },
        ),
        
        // Refresh button
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _refreshData,
          tooltip: 'Refresh Data',
        ),
        
        // Settings menu
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'export',
              child: const Row(
                children: [
                  Icon(Icons.file_download),
                  SizedBox(width: 8),
                  Text('Export Data'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: const Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('Dashboard Settings'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'help',
              child: const Row(
                children: [
                  Icon(Icons.help_outline),
                  SizedBox(width: 8),
                  Text('Help & Support'),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            switch (value) {
              case 'export':
                _handleExportData();
                break;
              case 'settings':
                _showDashboardSettings();
                break;
              case 'help':
                _showHelpDialog();
                break;
            }
          },
        ),
      ],
    );
  }

  Widget _buildDrawer(ThemeData theme) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.secondary,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.analytics,
                  color: theme.colorScheme.onPrimary,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  'Analytics Dashboard',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Organization: ${widget.organizationId}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Overview'),
            onTap: () => Navigator.pop(context),
          ),
          
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('User Analytics'),
            onTap: () => Navigator.pop(context),
          ),
          
          ListTile(
            leading: const Icon(Icons.trending_up),
            title: const Text('Performance'),
            onTap: () => Navigator.pop(context),
          ),
          
          ListTile(
            leading: const Icon(Icons.lightbulb_outline),
            title: const Text('Insights'),
            onTap: () => Navigator.pop(context),
          ),
          
          const Divider(),
          
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              _showDashboardSettings();
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.file_download),
            title: const Text('Export Data'),
            onTap: () {
              Navigator.pop(context);
              _handleExportData();
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.help_outline),
            title: const Text('Help'),
            onTap: () {
              Navigator.pop(context);
              _showHelpDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(ThemeData theme, AnalyticsError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to Load Analytics',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _refreshData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton(ThemeData theme) {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoaded && state.isRefreshing) {
          return FloatingActionButton(
            onPressed: null,
            child: CircularProgressIndicator(
              color: theme.colorScheme.onPrimaryContainer,
            ),
          );
        }
        
        return FloatingActionButton.extended(
          onPressed: _refreshData,
          icon: const Icon(Icons.refresh),
          label: const Text('Refresh'),
          tooltip: 'Refresh Analytics Data',
        );
      },
    );
  }

  void _handleExportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Analytics Data'),
        content: const Text(
          'Choose the format and time range for your analytics export.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export functionality coming soon!'),
                ),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showDashboardSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dashboard Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Real-time Updates'),
              subtitle: const Text('Enable live data updates'),
              value: _isRealTimeEnabled,
              onChanged: (value) {
                Navigator.pop(context);
                _toggleRealTimeUpdates();
              },
            ),
            const ListTile(
              title: Text('Refresh Interval'),
              subtitle: Text('30 seconds'),
              trailing: Icon(Icons.schedule),
            ),
            const ListTile(
              title: Text('Theme'),
              subtitle: Text('System default'),
              trailing: Icon(Icons.palette),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Analytics Help'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Understanding Your Analytics:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• KPI Cards show key performance metrics'),
              Text('• Charts provide trend analysis over time'),
              Text('• Insights offer AI-powered recommendations'),
              SizedBox(height: 16),
              Text(
                'Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Real-time data updates'),
              Text('• Interactive charts with zoom'),
              Text('• Time range filtering'),
              Text('• Data export capabilities'),
              SizedBox(height: 16),
              Text(
                'Need help? Contact <NAME_EMAIL>',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}