import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart' hide DateTimeRange;
import 'package:flutter/material.dart' show DateTimeRange;
import '../../../core/services/api_service.dart';
import '../../../core/utils/logger.dart';

part 'analytics_event.dart';
part 'analytics_state.dart';

/// Analytics BLoC for managing analytics dashboard state and API interactions
/// 
/// Features:
/// - Dashboard metrics loading and caching
/// - Real-time data updates with WebSocket integration
/// - Historical data management with time-based filtering
/// - Event tracking and analytics aggregation
/// - Error handling and retry mechanisms
/// - Optimistic UI updates for better UX
class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final ApiService _apiService;
  StreamSubscription<Map<String, dynamic>>? _metricsSubscription;
  Timer? _refreshTimer;
  
  // Cache management
  final Map<String, DashboardMetrics> _metricsCache = {};
  final Map<String, List<AnalyticsMetric>> _historicalCache = {};
  
  AnalyticsBloc({
    required ApiService apiService,
  }) : _apiService = apiService,
       super(AnalyticsInitial()) {
    
    // Event handlers
    on<LoadDashboardMetrics>(_onLoadDashboardMetrics);
    on<RefreshDashboardMetrics>(_onRefreshDashboardMetrics);
    on<LoadHistoricalMetrics>(_onLoadHistoricalMetrics);
    on<TrackAnalyticsEvent>(_onTrackAnalyticsEvent);
    on<FilterMetricsByCategory>(_onFilterMetricsByCategory);
    on<UpdateTimeRange>(_onUpdateTimeRange);
    on<EnableRealTimeUpdates>(_onEnableRealTimeUpdates);
    on<DisableRealTimeUpdates>(_onDisableRealTimeUpdates);
    on<_MetricsUpdated>(_onMetricsUpdated);
    on<_ConnectionStatusChanged>(_onConnectionStatusChanged);
  }

  @override
  Future<void> close() {
    _metricsSubscription?.cancel();
    _refreshTimer?.cancel();
    return super.close();
  }

  /// Load dashboard metrics with caching and error handling
  Future<void> _onLoadDashboardMetrics(
    LoadDashboardMetrics event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      // Check cache first
      final cacheKey = '${event.organizationId}_${event.timeRange.start}_${event.timeRange.end}';
      final cachedMetrics = _metricsCache[cacheKey];
      
      if (cachedMetrics != null && _isCacheValid(cacheKey)) {
        emit(AnalyticsLoaded(
          dashboardMetrics: cachedMetrics,
          historicalMetrics: _historicalCache[cacheKey] ?? [],
          insights: _generateInsights(cachedMetrics),
          lastUpdated: DateTime.now(),
          isRealTimeEnabled: state is AnalyticsLoaded 
              ? (state as AnalyticsLoaded).isRealTimeEnabled
              : false,
        ));
        return;
      }

      // Show loading state if no cached data
      if (state is! AnalyticsLoaded) {
        emit(AnalyticsLoading());
      }

      // Load fresh data from API
      final dashboardMetrics = await _loadDashboardMetricsFromApi(
        event.organizationId,
        event.timeRange,
      );

      final historicalMetrics = await _loadHistoricalMetricsFromApi(
        event.organizationId,
        event.timeRange,
      );

      // Update cache
      _metricsCache[cacheKey] = dashboardMetrics;
      _historicalCache[cacheKey] = historicalMetrics;

      // Generate insights
      final insights = _generateInsights(dashboardMetrics);

      emit(AnalyticsLoaded(
        dashboardMetrics: dashboardMetrics,
        historicalMetrics: historicalMetrics,
        insights: insights,
        lastUpdated: DateTime.now(),
        isRealTimeEnabled: state is AnalyticsLoaded 
            ? (state as AnalyticsLoaded).isRealTimeEnabled
            : false,
      ));

    } catch (error) {
      emit(AnalyticsError(
        message: 'Failed to load dashboard metrics: ${error.toString()}',
        errorType: AnalyticsErrorType.networkError,
      ));
    }
  }

  /// Refresh dashboard metrics with optimistic updates
  Future<void> _onRefreshDashboardMetrics(
    RefreshDashboardMetrics event,
    Emitter<AnalyticsState> emit,
  ) async {
    if (state is! AnalyticsLoaded) {
      add(LoadDashboardMetrics(
        organizationId: event.organizationId,
        timeRange: event.timeRange,
      ));
      return;
    }

    final currentState = state as AnalyticsLoaded;
    
    try {
      // Keep current state while refreshing
      emit(currentState.copyWith(isRefreshing: true));

      // Load fresh data
      final dashboardMetrics = await _loadDashboardMetricsFromApi(
        event.organizationId,
        event.timeRange,
      );

      final historicalMetrics = await _loadHistoricalMetricsFromApi(
        event.organizationId,
        event.timeRange,
      );

      // Update cache
      final cacheKey = '${event.organizationId}_${event.timeRange.start}_${event.timeRange.end}';
      _metricsCache[cacheKey] = dashboardMetrics;
      _historicalCache[cacheKey] = historicalMetrics;

      // Generate insights
      final insights = _generateInsights(dashboardMetrics);

      emit(AnalyticsLoaded(
        dashboardMetrics: dashboardMetrics,
        historicalMetrics: historicalMetrics,
        insights: insights,
        lastUpdated: DateTime.now(),
        isRealTimeEnabled: currentState.isRealTimeEnabled,
        isRefreshing: false,
      ));

    } catch (error) {
      emit(AnalyticsError(
        message: 'Failed to refresh metrics: ${error.toString()}',
        errorType: AnalyticsErrorType.refreshError,
      ));
    }
  }

  /// Load historical metrics for trend analysis
  Future<void> _onLoadHistoricalMetrics(
    LoadHistoricalMetrics event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final historicalMetrics = await _loadHistoricalMetricsFromApi(
        event.organizationId,
        event.timeRange,
        event.metricType,
      );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          historicalMetrics: historicalMetrics,
          lastUpdated: DateTime.now(),
        ));
      }

    } catch (error) {
      emit(AnalyticsError(
        message: 'Failed to load historical metrics: ${error.toString()}',
        errorType: AnalyticsErrorType.dataError,
      ));
    }
  }

  /// Track analytics events for real-time updates
  Future<void> _onTrackAnalyticsEvent(
    TrackAnalyticsEvent event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      // Send event to API  
      await _apiService.post('/analytics/events', body: {
        'eventType': 'custom_event',
        'data': event.analyticsEvent.toString(),
      });
      
      // Update local state optimistically if we're in loaded state
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        
        // Update metrics based on event type
        final updatedMetrics = _updateMetricsFromEvent(
          currentState.dashboardMetrics,
          event.analyticsEvent,
        );
        
        emit(currentState.copyWith(
          dashboardMetrics: updatedMetrics,
          lastUpdated: DateTime.now(),
        ));
      }

    } catch (error) {
      // Event tracking failures shouldn't break the UI
      Logger.error('Failed to track analytics event', tag: 'AnalyticsBloc', error: error);
    }
  }

  /// Filter metrics by category
  void _onFilterMetricsByCategory(
    FilterMetricsByCategory event,
    Emitter<AnalyticsState> emit,
  ) {
    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      
      // Filter historical metrics by category  
      final filteredMetrics = currentState.historicalMetrics
          .where((metric) => event.category == 'all' || true) // Simplified filtering for now
          .toList();

      emit(currentState.copyWith(
        historicalMetrics: filteredMetrics,
        filteredCategory: event.category,
      ));
    }
  }

  /// Update time range for metrics
  void _onUpdateTimeRange(
    UpdateTimeRange event,
    Emitter<AnalyticsState> emit,
  ) {
    // Trigger reload with new time range
    add(LoadDashboardMetrics(
      organizationId: event.organizationId,
      timeRange: event.timeRange,
    ));
  }

  /// Enable real-time updates via WebSocket
  Future<void> _onEnableRealTimeUpdates(
    EnableRealTimeUpdates event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(isRealTimeEnabled: true));
      }

      // Setup WebSocket subscription for real-time metrics
      _metricsSubscription = _apiService.subscribeToMetricsUpdates(
        event.organizationId,
      ).listen(
        (metricsData) {
          add(_MetricsUpdated(metricsData));
        },
        onError: (error) {
          add(_ConnectionStatusChanged(false));
        },
      );

      // Setup periodic refresh as fallback
      _refreshTimer = Timer.periodic(
        const Duration(seconds: 30),
        (_) {
          if (state is AnalyticsLoaded) {
            add(RefreshDashboardMetrics(
              organizationId: event.organizationId,
              timeRange: DateTimeRange(
                start: DateTime.now().subtract(const Duration(days: 7)),
                end: DateTime.now(),
              ),
            ));
          }
        },
      );

    } catch (error) {
      emit(AnalyticsError(
        message: 'Failed to enable real-time updates: ${error.toString()}',
        errorType: AnalyticsErrorType.connectionError,
      ));
    }
  }

  /// Disable real-time updates
  void _onDisableRealTimeUpdates(
    DisableRealTimeUpdates event,
    Emitter<AnalyticsState> emit,
  ) {
    _metricsSubscription?.cancel();
    _refreshTimer?.cancel();

    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      emit(currentState.copyWith(isRealTimeEnabled: false));
    }
  }

  /// Handle real-time metrics updates
  void _onMetricsUpdated(
    _MetricsUpdated event,
    Emitter<AnalyticsState> emit,
  ) {
    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      
      try {
        // Parse updated metrics from WebSocket data
        final updatedMetrics = DashboardMetrics.fromJson(event.metricsData);
        
        // Generate new insights
        final insights = _generateInsights(updatedMetrics);
        
        emit(currentState.copyWith(
          dashboardMetrics: updatedMetrics,
          insights: insights,
          lastUpdated: DateTime.now(),
        ));
        
      } catch (error) {
        Logger.error('Failed to parse metrics update', tag: 'AnalyticsBloc', error: error);
      }
    }
  }

  /// Handle connection status changes
  void _onConnectionStatusChanged(
    _ConnectionStatusChanged event,
    Emitter<AnalyticsState> emit,
  ) {
    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      emit(currentState.copyWith(
        isConnected: event.isConnected,
      ));
    }
  }

  /// Load dashboard metrics from API
  Future<DashboardMetrics> _loadDashboardMetricsFromApi(
    String organizationId,
    DateTimeRange timeRange,
  ) async {
    final response = await _apiService.get('/analytics/dashboard/$organizationId', queryParameters: {
      'start_date': timeRange.start.toIso8601String(),
      'end_date': timeRange.end.toIso8601String(),
    });

    return DashboardMetrics.fromJson(response.data);
  }

  /// Load historical metrics from API
  Future<List<AnalyticsMetric>> _loadHistoricalMetricsFromApi(
    String organizationId,
    DateTimeRange timeRange, [
    String? metricType,
  ]) async {
    final queryParams = {
      'start_date': timeRange.start.toIso8601String(),
      'end_date': timeRange.end.toIso8601String(),
    };
    
    if (metricType != null) {
      queryParams['type'] = metricType;
    }

    final response = await _apiService.get('/analytics/metrics/$organizationId', queryParameters: queryParams);
    
    final List<dynamic> metricsJson = response.data['metrics'] ?? [];
    return metricsJson.map((json) => AnalyticsMetric.fromJson(json)).toList();
  }

  /// Generate insights from dashboard metrics
  List<Map<String, dynamic>> _generateInsights(DashboardMetrics metrics) {
    final insights = <Map<String, dynamic>>[];
    
    // Engagement insights
    if (metrics.avgEngagementScore > 8.0) {
      insights.add({
        'id': 'engagement_high',
        'title': 'Exceptional Engagement',
        'description': 'User engagement is significantly above average. Great work!',
        'category': 'engagement',
        'priority': 'low',
        'impactScore': 0.3,
        'timestamp': DateTime.now().toIso8601String(),
        'actionRequired': false,
      });
    } else if (metrics.avgEngagementScore < 5.0) {
      insights.add({
        'id': 'engagement_low',
        'title': 'Low Engagement Alert',
        'description': 'User engagement has dropped below optimal levels. Consider reviewing content strategy.',
        'category': 'engagement',
        'priority': 'high',
        'impactScore': 0.8,
        'timestamp': DateTime.now().toIso8601String(),
        'actionRequired': true,
      });
    }

    // Task completion insights
    if (metrics.taskCompletionRate < 0.6) {
      insights.add({
        'id': 'completion_low',
        'title': 'Task Completion Below Target',
        'description': 'Task completion rate is below 60%. Review task difficulty and user support.',
        'category': 'productivity',
        'priority': 'medium',
        'impactScore': 0.7,
        'timestamp': DateTime.now().toIso8601String(),
        'actionRequired': true,
      });
    }

    // Active users trend
    if (metrics.activeUsers > 1000) {
      insights.add({
        'id': 'users_milestone',
        'title': 'User Milestone Reached',
        'description': 'Congratulations! You\'ve reached over 1000 active users.',
        'category': 'performance',
        'priority': 'low',
        'impactScore': 0.4,
        'timestamp': DateTime.now().toIso8601String(),
        'actionRequired': false,
      });
    }

    return insights;
  }

  /// Update metrics optimistically from analytics events
  DashboardMetrics _updateMetricsFromEvent(
    DashboardMetrics currentMetrics,
    AnalyticsEvent event,
  ) {
    // Create a copy of current metrics
    var updatedMetrics = currentMetrics;

    switch (event.toString()) {
      case 'task_completed':
        // Increment total events - simplified for now
        updatedMetrics = currentMetrics;
        break;
      
      case 'user_login':
        // Could affect active users count
        break;
        
      case 'quest_completed':
        // Update quest completion rate
        break;
        
      default:
        // Generic event increment - simplified for now
        updatedMetrics = currentMetrics;
    }

    return updatedMetrics;
  }

  /// Check if cache is still valid
  bool _isCacheValid(String cacheKey) {
    // For now, assume cache is valid for the timeout duration
    // In a real implementation, you'd track cache timestamps
    return true;
  }
}