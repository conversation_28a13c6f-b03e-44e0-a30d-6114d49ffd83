part of 'analytics_bloc.dart';

/// Base class for all analytics events
abstract class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();

  @override
  List<Object?> get props => [];
}

/// Load dashboard metrics for a specific organization and time range
class LoadDashboardMetrics extends AnalyticsEvent {
  final String organizationId;
  final DateTimeRange timeRange;

  const LoadDashboardMetrics({
    required this.organizationId,
    required this.timeRange,
  });

  @override
  List<Object> get props => [organizationId, timeRange];
}

/// Refresh current dashboard metrics
class RefreshDashboardMetrics extends AnalyticsEvent {
  final String organizationId;
  final DateTimeRange timeRange;

  const RefreshDashboardMetrics({
    required this.organizationId,
    required this.timeRange,
  });

  @override
  List<Object> get props => [organizationId, timeRange];
}

/// Load historical metrics for trend analysis
class LoadHistoricalMetrics extends AnalyticsEvent {
  final String organizationId;
  final DateTimeRange timeRange;
  final String? metricType;

  const LoadHistoricalMetrics({
    required this.organizationId,
    required this.timeRange,
    this.metricType,
  });

  @override
  List<Object?> get props => [organizationId, timeRange, metricType];
}

/// Track a new analytics event
class TrackAnalyticsEvent extends AnalyticsEvent {
  final AnalyticsEvent analyticsEvent;

  const TrackAnalyticsEvent({
    required this.analyticsEvent,
  });

  @override
  List<Object> get props => [analyticsEvent];
}

/// Filter metrics by category
class FilterMetricsByCategory extends AnalyticsEvent {
  final String category;

  const FilterMetricsByCategory(this.category);

  @override
  List<Object> get props => [category];
}

/// Update time range for analytics
class UpdateTimeRange extends AnalyticsEvent {
  final String organizationId;
  final DateTimeRange timeRange;

  const UpdateTimeRange({
    required this.organizationId,
    required this.timeRange,
  });

  @override
  List<Object> get props => [organizationId, timeRange];
}

/// Enable real-time updates for analytics
class EnableRealTimeUpdates extends AnalyticsEvent {
  final String organizationId;

  const EnableRealTimeUpdates({
    required this.organizationId,
  });

  @override
  List<Object> get props => [organizationId];
}

/// Disable real-time updates for analytics
class DisableRealTimeUpdates extends AnalyticsEvent {
  const DisableRealTimeUpdates();

  @override
  List<Object> get props => [];
}

/// Internal event for handling metrics updates from WebSocket
class _MetricsUpdated extends AnalyticsEvent {
  final Map<String, dynamic> metricsData;

  const _MetricsUpdated(this.metricsData);

  @override
  List<Object> get props => [metricsData];
}

/// Internal event for handling connection status changes
class _ConnectionStatusChanged extends AnalyticsEvent {
  final bool isConnected;

  const _ConnectionStatusChanged(this.isConnected);

  @override
  List<Object> get props => [isConnected];
}