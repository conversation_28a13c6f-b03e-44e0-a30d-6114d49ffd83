part of 'analytics_bloc.dart';

/// Base class for all analytics states
abstract class AnalyticsState extends Equatable {
  const AnalyticsState();

  @override
  List<Object?> get props => [];
}

/// Initial state when analytics bloc is first created
class AnalyticsInitial extends AnalyticsState {
  const AnalyticsInitial();
}

/// Loading state while fetching analytics data
class AnalyticsLoading extends AnalyticsState {
  final String? message;

  const AnalyticsLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// Successfully loaded analytics data
class AnalyticsLoaded extends AnalyticsState {
  final DashboardMetrics dashboardMetrics;
  final List<AnalyticsMetric> historicalMetrics;
  final List<Map<String, dynamic>> insights;
  final DateTime lastUpdated;
  final bool isRealTimeEnabled;
  final bool isRefreshing;
  final bool isConnected;
  final String? filteredCategory;

  const AnalyticsLoaded({
    required this.dashboardMetrics,
    required this.historicalMetrics,
    required this.insights,
    required this.lastUpdated,
    this.isRealTimeEnabled = false,
    this.isRefreshing = false,
    this.isConnected = true,
    this.filteredCategory,
  });

  /// Create a copy of this state with updated fields
  AnalyticsLoaded copyWith({
    DashboardMetrics? dashboardMetrics,
    List<AnalyticsMetric>? historicalMetrics,
    List<Map<String, dynamic>>? insights,
    DateTime? lastUpdated,
    bool? isRealTimeEnabled,
    bool? isRefreshing,
    bool? isConnected,
    String? filteredCategory,
  }) {
    return AnalyticsLoaded(
      dashboardMetrics: dashboardMetrics ?? this.dashboardMetrics,
      historicalMetrics: historicalMetrics ?? this.historicalMetrics,
      insights: insights ?? this.insights,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isRealTimeEnabled: isRealTimeEnabled ?? this.isRealTimeEnabled,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isConnected: isConnected ?? this.isConnected,
      filteredCategory: filteredCategory ?? this.filteredCategory,
    );
  }

  @override
  List<Object?> get props => [
    dashboardMetrics,
    historicalMetrics,
    insights,
    lastUpdated,
    isRealTimeEnabled,
    isRefreshing,
    isConnected,
    filteredCategory,
  ];
}

/// Error state when analytics operations fail
class AnalyticsError extends AnalyticsState {
  final String message;
  final AnalyticsErrorType errorType;
  final dynamic error;
  final StackTrace? stackTrace;

  const AnalyticsError({
    required this.message,
    required this.errorType,
    this.error,
    this.stackTrace,
  });

  @override
  List<Object?> get props => [message, errorType, error];
}

/// Types of analytics errors
enum AnalyticsErrorType {
  networkError,
  dataError,
  authenticationError,
  validationError,
  refreshError,
  connectionError,
  unknownError,
}