import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/analytics_bloc.dart';
import 'kpi_metrics_row.dart';
import 'charts_panel.dart';
import 'insights_panel.dart';

/// Advanced Analytics Dashboard with real-time metrics and interactive visualizations
/// 
/// Features:
/// - KPI metrics cards with trend indicators
/// - Interactive charts with drill-down capabilities  
/// - Real-time updates via WebSocket
/// - Time range selection and filtering
/// - Responsive Material Design 3 layout
class AnalyticsDashboard extends StatefulWidget {
  final String organizationId;
  final DateTimeRange? initialTimeRange;
  final bool showFilters;
  final bool enableRealTime;

  const AnalyticsDashboard({
    super.key,
    required this.organizationId,
    this.initialTimeRange,
    this.showFilters = true,
    this.enableRealTime = true,
  });

  @override
  State<AnalyticsDashboard> createState() => _AnalyticsDashboardState();
}

class _AnalyticsDashboardState extends State<AnalyticsDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _refreshController;
  DateTimeRange _selectedTimeRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 7)),
    end: DateTime.now(),
  );
  String _selectedMetricCategory = 'all';

  // Dashboard refresh animation
  late Animation<double> _refreshAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize time range
    _selectedTimeRange = widget.initialTimeRange ?? DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 7)),
      end: DateTime.now(),
    );
    
    // Setup tab controller for different views
    _tabController = TabController(length: 4, vsync: this);
    
    // Setup refresh animation
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _refreshAnimation = CurvedAnimation(
      parent: _refreshController,
      curve: Curves.easeInOut,
    );

    // Load initial data
    _loadAnalyticsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _loadAnalyticsData() {
    context.read<AnalyticsBloc>().add(LoadDashboardMetrics(
      organizationId: widget.organizationId,
      timeRange: _selectedTimeRange,
    ));
  }

  void _refreshData() {
    _refreshController.forward().then((_) {
      _refreshController.reverse();
    });
    
    context.read<AnalyticsBloc>().add(RefreshDashboardMetrics(
      organizationId: widget.organizationId,
      timeRange: _selectedTimeRange,
    ));
  }

  void _onTimeRangeChanged(DateTimeRange newRange) {
    setState(() {
      _selectedTimeRange = newRange;
    });
    _loadAnalyticsData();
  }

  void _onMetricCategoryChanged(String category) {
    setState(() {
      _selectedMetricCategory = category;
    });
    // Filter existing data or reload with category filter
    context.read<AnalyticsBloc>().add(FilterMetricsByCategory(category));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header with filters and controls
          if (widget.showFilters) _buildHeader(theme),
          
          // Tab bar for different views
          _buildTabBar(theme),
          
          // Main dashboard content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildEngagementTab(),
                _buildPerformanceTab(), 
                _buildInsightsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: widget.enableRealTime
          ? _buildRefreshButton(theme)
          : null,
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Dashboard title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analytics Dashboard',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                BlocBuilder<AnalyticsBloc, AnalyticsState>(
                  builder: (context, state) {
                    if (state is AnalyticsLoaded) {
                      return Text(
                        'Last updated: ${_formatLastUpdate(state.lastUpdated)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          
          // Time range selector
          _buildTimeRangeSelector(theme),
          
          const SizedBox(width: 16),
          
          // Metric category filter
          _buildMetricCategoryFilter(theme),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector(ThemeData theme) {
    return PopupMenuButton<DateTimeRange>(
      onSelected: _onTimeRangeChanged,
      itemBuilder: (context) => [
        PopupMenuItem(
          value: DateTimeRange(
            start: DateTime.now().subtract(const Duration(days: 7)),
            end: DateTime.now(),
          ),
          child: const Text('Last 7 days'),
        ),
        PopupMenuItem(
          value: DateTimeRange(
            start: DateTime.now().subtract(const Duration(days: 30)),
            end: DateTime.now(),
          ),
          child: const Text('Last 30 days'),
        ),
        PopupMenuItem(
          value: DateTimeRange(
            start: DateTime.now().subtract(const Duration(days: 90)),
            end: DateTime.now(),
          ),
          child: const Text('Last 90 days'),
        ),
        PopupMenuItem(
          value: DateTimeRange(
            start: DateTime(DateTime.now().year, DateTime.now().month, 1),
            end: DateTime.now(),
          ),
          child: const Text('This month'),
        ),
      ],
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.date_range,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              _formatTimeRangeLabel(_selectedTimeRange),
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCategoryFilter(ThemeData theme) {
    const categories = [
      ('all', 'All Metrics'),
      ('engagement', 'Engagement'),
      ('productivity', 'Productivity'),
      ('gamification', 'Gamification'),
      ('collaboration', 'Collaboration'),
    ];

    return PopupMenuButton<String>(
      onSelected: _onMetricCategoryChanged,
      itemBuilder: (context) => categories
          .map((category) => PopupMenuItem(
                value: category.$1,
                child: Text(category.$2),
              ))
          .toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.filter_list,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              categories.firstWhere((c) => c.$1 == _selectedMetricCategory).$2,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
          Tab(text: 'Engagement', icon: Icon(Icons.people)),
          Tab(text: 'Performance', icon: Icon(Icons.trending_up)),
          Tab(text: 'Insights', icon: Icon(Icons.lightbulb_outline)),
        ],
        indicatorColor: theme.colorScheme.primary,
        labelColor: theme.colorScheme.primary,
        unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildOverviewTab() {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is AnalyticsError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load analytics data',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _refreshData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is AnalyticsLoaded) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // KPI Metrics Row
                KPIMetricsRow(
                  dashboardMetrics: {
                    'activeUsers': state.dashboardMetrics.activeUsers,
                    'totalEvents': state.dashboardMetrics.totalEvents,
                    'taskCompletionRate': state.dashboardMetrics.taskCompletionRate,
                    'questCompletionRate': state.dashboardMetrics.questCompletionRate,
                    'avgEngagementScore': state.dashboardMetrics.avgEngagementScore,
                    'achievementUnlocks': state.dashboardMetrics.achievementUnlocks,
                  },
                  timeRange: _selectedTimeRange,
                ),
                
                const SizedBox(height: 24),
                
                // Main charts section
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left panel - Main charts (75% width)
                    Expanded(
                      flex: 3,
                      child: ChartsPanel(
                        dashboardMetrics: {
                          'activeUsers': state.dashboardMetrics.activeUsers,
                          'totalEvents': state.dashboardMetrics.totalEvents,
                          'taskCompletionRate': state.dashboardMetrics.taskCompletionRate,
                          'avgEngagementScore': state.dashboardMetrics.avgEngagementScore,
                        },
                        historicalMetrics: state.historicalMetrics.map((metric) => {
                          'timestamp': metric.createdAt.toIso8601String(),
                          'value': 1.0, // Default value since AnalyticsMetric doesn't have a value field
                          'metricType': metric.name,
                        }).toList(),
                        timeRange: _selectedTimeRange,
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Right panel - Insights and additional info (25% width)
                    Expanded(
                      flex: 1,
                      child: InsightsPanel(
                        insights: state.insights,
                        topUsers: state.dashboardMetrics.topUsers.map((user) => {
                          'name': user.userId,
                          'organizationId': '', // UserMetricsSummary doesn't have organizationId
                          'role': 'User',
                          'avatarUrl': null,
                        }).toList(),
                        trends: const <String, dynamic>{},
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildEngagementTab() {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoaded) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'User Engagement Analytics',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                
                // Engagement-specific widgets will be added here
                Center(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.people,
                            size: 48,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Engagement Analytics',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Advanced engagement metrics coming soon',
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildPerformanceTab() {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoaded) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Performance Analytics',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                
                // Performance-specific widgets will be added here
                Center(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.trending_up,
                            size: 48,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Performance Analytics',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Performance metrics and optimization insights coming soon',
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildInsightsTab() {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoaded) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: InsightsPanel(
              insights: state.insights,
              topUsers: state.dashboardMetrics.topUsers.map((user) => {
                'name': user.userId,
                'organizationId': '', // UserMetricsSummary doesn't have organizationId
                'role': 'User',
                'avatarUrl': null,
              }).toList(),
              trends: const <String, dynamic>{},
              expanded: true, // Full-width insights view
            ),
          );
        }
        
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildRefreshButton(ThemeData theme) {
    return AnimatedBuilder(
      animation: _refreshAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _refreshAnimation.value * 2 * 3.14159,
          child: FloatingActionButton(
            onPressed: _refreshData,
            tooltip: 'Refresh Analytics Data',
            child: const Icon(Icons.refresh),
          ),
        );
      },
    );
  }

  String _formatTimeRangeLabel(DateTimeRange range) {
    final days = range.duration.inDays;
    if (days <= 7) return 'Last 7 days';
    if (days <= 30) return 'Last 30 days';
    if (days <= 90) return 'Last 90 days';
    return 'Custom range';
  }

  String _formatLastUpdate(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);
    
    if (difference.inMinutes < 1) return 'Just now';
    if (difference.inMinutes < 60) return '${difference.inMinutes}m ago';
    if (difference.inHours < 24) return '${difference.inHours}h ago';
    return '${difference.inDays}d ago';
  }
}