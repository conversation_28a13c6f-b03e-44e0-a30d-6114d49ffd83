import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// Interactive Charts Panel displaying analytics data with real-time updates
/// 
/// Features:
/// - Line charts for trend analysis with smooth curves
/// - Bar charts for comparative metrics
/// - Pie charts for distribution analysis
/// - Interactive zoom and pan capabilities
/// - Real-time data updates with smooth transitions
/// - Material Design 3 theming and responsive layout
class ChartsPanel extends StatefulWidget {
  final Map<String, dynamic> dashboardMetrics;
  final List<Map<String, dynamic>> historicalMetrics;
  final DateTimeRange timeRange;
  final EdgeInsets? padding;
  final double? height;

  const ChartsPanel({
    super.key,
    required this.dashboardMetrics,
    required this.historicalMetrics,
    required this.timeRange,
    this.padding,
    this.height = 600,
  });

  @override
  State<ChartsPanel> createState() => _ChartsPanelState();
}

class _ChartsPanelState extends State<ChartsPanel>
    with TickerProviderStateMixin {
  late AnimationController _chartController;
  late Animation<double> _chartAnimation;
  
  int _selectedChartIndex = 0;
  bool _showAnimations = true;

  final List<String> _chartTypes = [
    'User Activity Trends',
    'Task Completion Metrics', 
    'Engagement Distribution',
    'Performance Analytics',
  ];

  @override
  void initState() {
    super.initState();
    
    _chartController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _chartAnimation = CurvedAnimation(
      parent: _chartController,
      curve: Curves.easeOutCubic,
    );

    if (_showAnimations) {
      _chartController.forward();
    }
  }

  @override
  void dispose() {
    _chartController.dispose();
    super.dispose();
  }

  void _onChartTypeChanged(int index) {
    setState(() {
      _selectedChartIndex = index;
    });
    
    if (_showAnimations) {
      _chartController.reset();
      _chartController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: widget.height,
      padding: widget.padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart selector header
          _buildChartHeader(theme),
          
          const SizedBox(height: 16),
          
          // Main chart display area
          Expanded(
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: AnimatedBuilder(
                  animation: _chartAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _chartAnimation.value,
                      child: Transform.translate(
                        offset: Offset(0, 20 * (1 - _chartAnimation.value)),
                        child: _buildSelectedChart(theme),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartHeader(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Analytics Charts',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Chart type selector
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: theme.dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: _selectedChartIndex,
              items: _chartTypes.asMap().entries.map((entry) {
                return DropdownMenuItem(
                  value: entry.key,
                  child: Text(
                    entry.value,
                    style: theme.textTheme.bodyMedium,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) _onChartTypeChanged(value);
              },
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Animation toggle
        IconButton(
          icon: Icon(
            _showAnimations ? Icons.motion_photos_on : Icons.motion_photos_off,
            color: theme.colorScheme.primary,
          ),
          onPressed: () {
            setState(() {
              _showAnimations = !_showAnimations;
            });
          },
          tooltip: 'Toggle Animations',
        ),
      ],
    );
  }

  Widget _buildSelectedChart(ThemeData theme) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildUserActivityChart(theme);
      case 1:
        return _buildTaskCompletionChart(theme);
      case 2:
        return _buildEngagementDistribution(theme);
      case 3:
        return _buildPerformanceChart(theme);
      default:
        return _buildUserActivityChart(theme);
    }
  }

  Widget _buildUserActivityChart(ThemeData theme) {
    final lineChartData = _generateUserActivityData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.trending_up,
              color: theme.colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'User Activity Trends',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                drawVerticalLine: true,
                getDrawingHorizontalLine: (value) => FlLine(
                  color: theme.dividerColor,
                  strokeWidth: 1,
                ),
                getDrawingVerticalLine: (value) => FlLine(
                  color: theme.dividerColor,
                  strokeWidth: 1,
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    interval: 1,
                    getTitlesWidget: (value, meta) {
                      return SideTitleWidget(
                        axisSide: meta.axisSide,
                        child: Text(
                          _formatDateLabel(value.toInt()),
                          style: theme.textTheme.bodySmall,
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    interval: 100,
                    reservedSize: 50,
                    getTitlesWidget: (value, meta) {
                      return SideTitleWidget(
                        axisSide: meta.axisSide,
                        child: Text(
                          value.toInt().toString(),
                          style: theme.textTheme.bodySmall,
                        ),
                      );
                    },
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: true,
                border: Border.all(color: theme.dividerColor),
              ),
              minX: 0,
              maxX: 6,
              minY: 0,
              maxY: 500,
              lineBarsData: [
                LineChartBarData(
                  spots: lineChartData,
                  isCurved: true,
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                  ),
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: 4,
                        color: theme.colorScheme.primary,
                        strokeWidth: 2,
                        strokeColor: theme.colorScheme.surface,
                      );
                    },
                  ),
                  belowBarData: BarAreaData(
                    show: true,
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary.withValues(alpha: 0.3),
                        theme.colorScheme.primary.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTaskCompletionChart(ThemeData theme) {
    final barData = _generateTaskCompletionData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.task_alt,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Task Completion Metrics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: 100,
              barTouchData: BarTouchData(
                enabled: true,
                touchTooltipData: BarTouchTooltipData(
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    return BarTooltipItem(
                      '${rod.toY.round()}%',
                      TextStyle(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      const categories = [
                        'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly'
                      ];
                      if (value.toInt() < categories.length) {
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            categories[value.toInt()],
                            style: theme.textTheme.bodySmall,
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                    reservedSize: 30,
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                    interval: 20,
                    getTitlesWidget: (value, meta) {
                      return SideTitleWidget(
                        axisSide: meta.axisSide,
                        child: Text(
                          '${value.toInt()}%',
                          style: theme.textTheme.bodySmall,
                        ),
                      );
                    },
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: true,
                border: Border.all(color: theme.dividerColor),
              ),
              barGroups: barData,
              gridData: FlGridData(
                show: true,
                drawHorizontalLine: true,
                drawVerticalLine: false,
                getDrawingHorizontalLine: (value) => FlLine(
                  color: theme.dividerColor,
                  strokeWidth: 1,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEngagementDistribution(ThemeData theme) {
    final pieData = _generateEngagementData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.pie_chart,
              color: Colors.orange,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Engagement Distribution',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: Row(
            children: [
              // Pie chart
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    sectionsSpace: 2,
                    centerSpaceRadius: 60,
                    sections: pieData,
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        // Handle touch interactions
                      },
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 20),
              
              // Legend
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLegendItem(theme, Colors.blue, 'High Engagement', '45%'),
                    const SizedBox(height: 12),
                    _buildLegendItem(theme, Colors.green, 'Medium Engagement', '35%'),
                    const SizedBox(height: 12),
                    _buildLegendItem(theme, Colors.orange, 'Low Engagement', '20%'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceChart(ThemeData theme) {
    final performanceData = _generatePerformanceData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.speed,
              color: Colors.purple,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Performance Analytics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Expanded(
          child: LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                drawVerticalLine: true,
                getDrawingHorizontalLine: (value) => FlLine(
                  color: theme.dividerColor,
                  strokeWidth: 1,
                ),
                getDrawingVerticalLine: (value) => FlLine(
                  color: theme.dividerColor,
                  strokeWidth: 1,
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    interval: 1,
                    getTitlesWidget: (value, meta) {
                      return SideTitleWidget(
                        axisSide: meta.axisSide,
                        child: Text(
                          _formatTimeLabel(value.toInt()),
                          style: theme.textTheme.bodySmall,
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    interval: 0.2,
                    reservedSize: 50,
                    getTitlesWidget: (value, meta) {
                      return SideTitleWidget(
                        axisSide: meta.axisSide,
                        child: Text(
                          '${(value * 100).toInt()}%',
                          style: theme.textTheme.bodySmall,
                        ),
                      );
                    },
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: true,
                border: Border.all(color: theme.dividerColor),
              ),
              minX: 0,
              maxX: 6,
              minY: 0,
              maxY: 1,
              lineBarsData: performanceData,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(ThemeData theme, Color color, String label, String value) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<FlSpot> _generateUserActivityData() {
    // Generate realistic user activity data points
    final baseActivity = (widget.dashboardMetrics['activeUsers'] ?? 100).toDouble();
    return [
      FlSpot(0, baseActivity * 0.7),
      FlSpot(1, baseActivity * 0.8),
      FlSpot(2, baseActivity * 1.1),
      FlSpot(3, baseActivity * 0.9),
      FlSpot(4, baseActivity * 1.2),
      FlSpot(5, baseActivity * 1.0),
      FlSpot(6, baseActivity * 1.15),
    ];
  }

  List<BarChartGroupData> _generateTaskCompletionData() {
    final baseCompletion = (widget.dashboardMetrics['taskCompletionRate'] ?? 0.8) * 100;
    return [
      BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: baseCompletion * 0.9, color: Colors.green, width: 20)]),
      BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: baseCompletion * 0.85, color: Colors.green.shade400, width: 20)]),
      BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: baseCompletion * 1.1, color: Colors.green.shade600, width: 20)]),
      BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: baseCompletion * 0.95, color: Colors.green.shade300, width: 20)]),
      BarChartGroupData(x: 4, barRods: [BarChartRodData(toY: baseCompletion, color: Colors.green.shade500, width: 20)]),
    ];
  }

  List<PieChartSectionData> _generateEngagementData() {
    // Simplified engagement data generation
    return [
      PieChartSectionData(
        color: Colors.blue,
        value: 45,
        title: '45%',
        radius: 80,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.green,
        value: 35,
        title: '35%',
        radius: 70,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: 20,
        title: '20%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  List<LineChartBarData> _generatePerformanceData() {
    return [
      LineChartBarData(
        spots: [
          const FlSpot(0, 0.8),
          const FlSpot(1, 0.75),
          const FlSpot(2, 0.9),
          const FlSpot(3, 0.85),
          const FlSpot(4, 0.95),
          const FlSpot(5, 0.88),
          const FlSpot(6, 0.92),
        ],
        isCurved: true,
        color: Colors.purple,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: 4,
              color: Colors.purple,
              strokeWidth: 2,
              strokeColor: Colors.white,
            );
          },
        ),
      ),
      LineChartBarData(
        spots: [
          const FlSpot(0, 0.7),
          const FlSpot(1, 0.8),
          const FlSpot(2, 0.82),
          const FlSpot(3, 0.78),
          const FlSpot(4, 0.85),
          const FlSpot(5, 0.83),
          const FlSpot(6, 0.87),
        ],
        isCurved: true,
        color: Colors.deepPurple,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: 4,
              color: Colors.deepPurple,
              strokeWidth: 2,
              strokeColor: Colors.white,
            );
          },
        ),
      ),
    ];
  }

  String _formatDateLabel(int dayIndex) {
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayIndex < days.length ? days[dayIndex] : '';
  }

  String _formatTimeLabel(int hourIndex) {
    return '${(hourIndex * 4)}:00';
  }
}