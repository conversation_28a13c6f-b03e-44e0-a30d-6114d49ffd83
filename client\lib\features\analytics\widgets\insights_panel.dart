import 'package:flutter/material.dart';

/// Insights Panel displaying analytics insights, trends, and actionable recommendations
/// 
/// Features:
/// - AI-generated insights and recommendations
/// - Top performing users and teams
/// - Trend analysis with visual indicators
/// - Action items and improvement suggestions
/// - Expandable view for detailed analysis
/// - Real-time insight updates
class InsightsPanel extends StatefulWidget {
  final List<Map<String, dynamic>> insights;
  final List<Map<String, dynamic>> topUsers;
  final Map<String, dynamic> trends;
  final bool expanded;
  final EdgeInsets? padding;
  final VoidCallback? onInsightTap;

  const InsightsPanel({
    super.key,
    required this.insights,
    required this.topUsers,
    required this.trends,
    this.expanded = false,
    this.padding,
    this.onInsightTap,
  });

  @override
  State<InsightsPanel> createState() => _InsightsPanelState();
}

class _InsightsPanelState extends State<InsightsPanel>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  String _selectedInsightCategory = 'all';
  bool _showTrends = true;
  bool _showRecommendations = true;

  final List<String> _insightCategories = [
    'all',
    'performance',
    'engagement', 
    'productivity',
    'trends',
  ];

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    // Start animations with slight delay
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
    
    Future.delayed(const Duration(milliseconds: 400), () {
      _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with controls
          if (!widget.expanded) _buildCompactHeader(theme),
          if (widget.expanded) _buildExpandedHeader(theme),
          
          const SizedBox(height: 16),
          
          // Main insights content
          Expanded(
            child: SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: widget.expanded 
                    ? _buildExpandedView(theme)
                    : _buildCompactView(theme),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactHeader(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Key Insights',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Icon(
          Icons.lightbulb_outline,
          color: theme.colorScheme.primary,
          size: 20,
        ),
      ],
    );
  }

  Widget _buildExpandedHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Analytics Insights & Recommendations',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Icon(
              Icons.psychology,
              color: theme.colorScheme.primary,
              size: 24,
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Category filter
        Wrap(
          spacing: 8.0,
          children: _insightCategories.map((category) {
            final isSelected = category == _selectedInsightCategory;
            return FilterChip(
              label: Text(category.substring(0, 1).toUpperCase() + category.substring(1)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedInsightCategory = category;
                  });
                }
              },
              selectedColor: theme.colorScheme.primaryContainer,
              checkmarkColor: theme.colorScheme.primary,
            );
          }).toList(),
        ),
        
        const SizedBox(height: 8),
        
        // View toggles
        Row(
          children: [
            Switch(
              value: _showTrends,
              onChanged: (value) {
                setState(() {
                  _showTrends = value;
                });
              },
            ),
            const SizedBox(width: 8),
            Text('Show Trends', style: theme.textTheme.bodySmall),
            
            const SizedBox(width: 24),
            
            Switch(
              value: _showRecommendations,
              onChanged: (value) {
                setState(() {
                  _showRecommendations = value;
                });
              },
            ),
            const SizedBox(width: 8),
            Text('Show Recommendations', style: theme.textTheme.bodySmall),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactView(ThemeData theme) {
    return Column(
      children: [
        // Key insights summary (top 3)
        Expanded(
          child: ListView.builder(
            itemCount: 3,
            itemBuilder: (context, index) {
              if (index < widget.insights.length) {
                return _buildInsightCard(
                  theme, 
                  widget.insights[index], 
                  compact: true,
                );
              }
              return _buildPlaceholderInsight(theme, compact: true);
            },
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Top performer preview
        _buildTopPerformerPreview(theme),
        
        const SizedBox(height: 16),
        
        // Trend indicator
        if (_showTrends) _buildTrendIndicator(theme),
      ],
    );
  }

  Widget _buildExpandedView(ThemeData theme) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filtered insights list
          _buildInsightsList(theme),
          
          const SizedBox(height: 24),
          
          // Top performers section
          if (_showTrends) _buildTopPerformersSection(theme),
          
          const SizedBox(height: 24),
          
          // Trends and recommendations
          if (_showRecommendations) _buildRecommendationsSection(theme),
        ],
      ),
    );
  }

  Widget _buildInsightsList(ThemeData theme) {
    final filteredInsights = _getFilteredInsights();
    
    if (filteredInsights.isEmpty) {
      return _buildEmptyInsightsState(theme);
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analytics Insights',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        ...filteredInsights.map((insight) => 
          _buildInsightCard(theme, insight, compact: false)
        ),
      ],
    );
  }

  Widget _buildInsightCard(ThemeData theme, Map<String, dynamic> insight, {required bool compact}) {
    final priorityColor = _getPriorityColor(insight['priority'] ?? 'low');
    final categoryIcon = _getCategoryIcon(insight['category'] ?? 'general');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: widget.onInsightTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with priority and category
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: priorityColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    categoryIcon,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    (insight['category'] ?? 'GENERAL').toUpperCase(),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatTimestamp(DateTime.parse(insight['timestamp'] ?? DateTime.now().toIso8601String())),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Insight title and description
              Text(
                insight['title'] ?? 'Insight',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: compact ? 1 : null,
                overflow: compact ? TextOverflow.ellipsis : null,
              ),
              
              if (!compact || (insight['description'] ?? '').length < 100) ...[
                const SizedBox(height: 4),
                Text(
                  insight['description'] ?? 'No description available',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: compact ? 2 : null,
                  overflow: compact ? TextOverflow.ellipsis : null,
                ),
              ],
              
              // Impact score and actions
              if (!compact) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildImpactChip(theme, (insight['impactScore'] ?? 0.0).toDouble()),
                    const Spacer(),
                    if (insight['actionRequired'] == true) 
                      TextButton.icon(
                        onPressed: () {
                          // Handle action
                        },
                        icon: const Icon(Icons.arrow_forward, size: 16),
                        label: const Text('Take Action'),
                        style: TextButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderInsight(ThemeData theme, {required bool compact}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'AI Insight',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Analytics insights are being generated based on your data patterns and user behavior.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              maxLines: compact ? 2 : null,
              overflow: compact ? TextOverflow.ellipsis : null,
            ),
            if (!compact) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation(theme.colorScheme.primary),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTopPerformerPreview(ThemeData theme) {
    if (widget.topUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    final topUser = widget.topUsers.first;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: Colors.amber,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  'Top Performer',
                  style: theme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundImage: topUser['avatarUrl'] != null 
                      ? NetworkImage(topUser['avatarUrl']!)
                      : null,
                  child: topUser['avatarUrl'] == null 
                      ? Text((topUser['name'] ?? 'U').substring(0, 1))
                      : null,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        topUser['name'] ?? 'Unknown User',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        '${topUser['organizationId'] ?? 'N/A'} • Top this week',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendIndicator(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(
              Icons.trending_up,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Engagement Trending Up',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '+15% this week',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopPerformersSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Top Performers',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...widget.topUsers.take(5).map((user) => _buildUserListTile(theme, user)),
      ],
    );
  }

  Widget _buildRecommendationsSection(ThemeData theme) {
    final recommendations = _getRecommendations();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recommendations',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...recommendations.map((rec) => _buildRecommendationCard(theme, rec)),
      ],
    );
  }

  Widget _buildUserListTile(ThemeData theme, Map<String, dynamic> user) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        radius: 20,
        backgroundImage: user['avatarUrl'] != null 
            ? NetworkImage(user['avatarUrl']!)
            : null,
        child: user['avatarUrl'] == null 
            ? Text((user['name'] ?? 'U').substring(0, 1))
            : null,
      ),
      title: Text(
        user['name'] ?? 'Unknown User',
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        '${user['organizationId'] ?? 'N/A'} • ${user['role'] ?? 'User'}',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
      trailing: _buildPerformanceIndicator(theme),
    );
  }

  Widget _buildRecommendationCard(ThemeData theme, Map<String, dynamic> recommendation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          Icons.lightbulb,
          color: Colors.orange,
        ),
        title: Text(
          recommendation['title'] ?? 'Recommendation',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          recommendation['description'] ?? 'Action recommended based on analytics',
          style: theme.textTheme.bodySmall,
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        onTap: () {
          // Handle recommendation tap
        },
      ),
    );
  }

  Widget _buildImpactChip(ThemeData theme, double impactScore) {
    final impact = impactScore >= 0.8 ? 'High' : impactScore >= 0.5 ? 'Medium' : 'Low';
    final color = impactScore >= 0.8 ? Colors.red : impactScore >= 0.5 ? Colors.orange : Colors.green;
    
    return Chip(
      label: Text(
        'Impact: $impact',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color, width: 1),
    );
  }

  Widget _buildPerformanceIndicator(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '+23%',
        style: theme.textTheme.bodySmall?.copyWith(
          color: Colors.green,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyInsightsState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.insights,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No insights available',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Insights will appear as data is analyzed',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredInsights() {
    if (_selectedInsightCategory == 'all') {
      return widget.insights;
    }
    return widget.insights.where((insight) => 
      (insight['category'] ?? '') == _selectedInsightCategory
    ).toList();
  }

  List<Map<String, dynamic>> _getRecommendations() {
    return [
      {
        'title': 'Increase Engagement Activities',
        'description': 'Consider adding more interactive elements to boost user engagement by 20%',
      },
      {
        'title': 'Optimize Task Difficulty',
        'description': 'Balance task complexity to maintain optimal completion rates',
      },
      {
        'title': 'Enhance Collaboration',
        'description': 'Promote team-based activities to improve productivity scores',
      },
    ];
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'performance':
        return Icons.speed;
      case 'engagement':
        return Icons.favorite;
      case 'productivity':
        return Icons.trending_up;
      case 'trends':
        return Icons.timeline;
      default:
        return Icons.info_outline;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}