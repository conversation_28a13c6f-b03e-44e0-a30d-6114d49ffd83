import 'package:flutter/material.dart';

/// KPI Metrics Row displaying key performance indicators with trend analysis
/// 
/// Features:
/// - Animated counters with smooth transitions
/// - Trend indicators with color-coded arrows
/// - Percentage change calculations
/// - Responsive card layout
/// - Material Design 3 styling
class KPIMetricsRow extends StatefulWidget {
  final Map<String, dynamic> dashboardMetrics;
  final DateTimeRange timeRange;
  final EdgeInsets? padding;
  final double? cardElevation;

  const KPIMetricsRow({
    super.key,
    required this.dashboardMetrics,
    required this.timeRange,
    this.padding,
    this.cardElevation = 2.0,
  });

  @override
  State<KPIMetricsRow> createState() => _KPIMetricsRowState();
}

class _KPIMetricsRowState extends State<KPIMetricsRow>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    const int metricCount = 6; // Number of KPI cards
    
    _controllers = List.generate(
      metricCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 100)),
        vsync: this,
      ),
    );

    _animations = _controllers
        .map((controller) => Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(parent: controller, curve: Curves.easeOutQuart),
            ))
        .toList();

    // Start animations with staggered timing
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) _controllers[i].forward();
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? const EdgeInsets.all(0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Determine number of columns based on screen width
          int columns = constraints.maxWidth > 1200
              ? 6
              : constraints.maxWidth > 900
                  ? 3
                  : constraints.maxWidth > 600
                      ? 2
                      : 1;

          return Wrap(
            spacing: 16.0,
            runSpacing: 16.0,
            children: [
              _buildKPICard(
                context,
                index: 0,
                title: 'Active Users',
                value: (widget.dashboardMetrics['activeUsers'] ?? 0).toDouble(),
                icon: Icons.people,
                color: Colors.blue,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['activeUsers'] ?? 0).toDouble(),
                  0.15, // 15% growth simulation
                ),
                formatter: (value) => value.toInt().toString(),
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
              _buildKPICard(
                context,
                index: 1,
                title: 'Total Events',
                value: (widget.dashboardMetrics['totalEvents'] ?? 0).toDouble(),
                icon: Icons.analytics,
                color: Colors.green,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['totalEvents'] ?? 0).toDouble(),
                  0.08,
                ),
                formatter: (value) => _formatLargeNumber(value.toInt()),
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
              _buildKPICard(
                context,
                index: 2,
                title: 'Task Completion',
                value: (widget.dashboardMetrics['taskCompletionRate'] ?? 0.0) * 100,
                icon: Icons.task_alt,
                color: Colors.orange,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['taskCompletionRate'] ?? 0.0) * 100,
                  0.05,
                ),
                formatter: (value) => '${value.toStringAsFixed(1)}%',
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
              _buildKPICard(
                context,
                index: 3,
                title: 'Quest Success',
                value: (widget.dashboardMetrics['questCompletionRate'] ?? 0.0) * 100,
                icon: Icons.flag,
                color: Colors.purple,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['questCompletionRate'] ?? 0.0) * 100,
                  0.12,
                ),
                formatter: (value) => '${value.toStringAsFixed(1)}%',
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
              _buildKPICard(
                context,
                index: 4,
                title: 'Engagement Score',
                value: (widget.dashboardMetrics['avgEngagementScore'] ?? 0.0),
                icon: Icons.favorite,
                color: Colors.red,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['avgEngagementScore'] ?? 0.0),
                  0.03,
                ),
                formatter: (value) => value.toStringAsFixed(1),
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
              _buildKPICard(
                context,
                index: 5,
                title: 'Achievements',
                value: (widget.dashboardMetrics['achievementUnlocks'] ?? 0).toDouble(),
                icon: Icons.emoji_events,
                color: Colors.amber,
                previousValue: _calculatePreviousValue(
                  (widget.dashboardMetrics['achievementUnlocks'] ?? 0).toDouble(),
                  0.25,
                ),
                formatter: (value) => value.toInt().toString(),
                width: _calculateCardWidth(constraints.maxWidth, columns),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildKPICard(
    BuildContext context, {
    required int index,
    required String title,
    required double value,
    required IconData icon,
    required Color color,
    required double previousValue,
    required String Function(double) formatter,
    required double width,
  }) {
    final theme = Theme.of(context);
    final percentageChange = _calculatePercentageChange(value, previousValue);
    final isPositive = percentageChange >= 0;

    return AnimatedBuilder(
      animation: _animations[index],
      builder: (context, child) {
        final animationValue = _animations[index].value;
        
        return Transform.translate(
          offset: Offset(0, 20 * (1 - animationValue)),
          child: Opacity(
            opacity: animationValue,
            child: SizedBox(
              width: width,
              child: Card(
                elevation: widget.cardElevation,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with icon and trend
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Icon(
                              icon,
                              color: color,
                              size: 24,
                            ),
                          ),
                          _buildTrendIndicator(
                            percentageChange,
                            isPositive,
                            theme,
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Main value with animation
                      AnimatedBuilder(
                        animation: _animations[index],
                        builder: (context, child) {
                          final animatedValue = value * _animations[index].value;
                          return Text(
                            formatter(animatedValue),
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          );
                        },
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Title
                      Text(
                        title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Change indicator text
                      Text(
                        '${isPositive ? '+' : ''}${percentageChange.toStringAsFixed(1)}% vs previous period',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isPositive 
                              ? Colors.green.shade600 
                              : Colors.red.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTrendIndicator(
    double percentageChange,
    bool isPositive,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: isPositive 
            ? Colors.green.shade50 
            : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: isPositive 
              ? Colors.green.shade200 
              : Colors.red.shade200,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            size: 16,
            color: isPositive 
                ? Colors.green.shade600 
                : Colors.red.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            '${percentageChange.abs().toStringAsFixed(1)}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: isPositive 
                  ? Colors.green.shade600 
                  : Colors.red.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateCardWidth(double totalWidth, int columns) {
    // Calculate card width based on available space and columns
    final spacing = 16.0 * (columns - 1);
    return (totalWidth - spacing) / columns;
  }

  double _calculatePreviousValue(double currentValue, double changeRate) {
    // Simulate previous value for trend calculation
    // In real implementation, this would come from historical data
    return currentValue / (1 + changeRate);
  }

  double _calculatePercentageChange(double current, double previous) {
    if (previous == 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  String _formatLargeNumber(int value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    }
    return value.toString();
  }
}