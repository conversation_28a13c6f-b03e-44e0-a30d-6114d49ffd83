import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../../core/services/auth_service.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// Authentication BLoC for managing authentication state
class AuthBloc extends Bloc<AuthEvent, AuthenticationState> {
  final AuthService _authService;

  AuthBloc({AuthService? authService}) 
      : _authService = authService ?? AuthService(),
        super(const AuthUnauthenticated()) {
    
    // Register event handlers
    on<AuthInitialized>(_onAuthInitialized);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthTwoFactorVerificationRequested>(_onAuthTwoFactorVerificationRequested);
    on<AuthEmailVerificationRequested>(_onAuthEmailVerificationRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthPasswordResetConfirmed>(_onAuthPasswordResetConfirmed);
    on<AuthPasswordChangeRequested>(_onAuthPasswordChangeRequested);
    on<AuthProfileUpdateRequested>(_onAuthProfileUpdateRequested);
    on<AuthTwoFactorSetupRequested>(_onAuthTwoFactorSetupRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthSessionValidationRequested>(_onAuthSessionValidationRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthErrorCleared>(_onAuthErrorCleared);
    on<AuthSuccessMessageCleared>(_onAuthSuccessMessageCleared);
    on<AuthDemoLoginRequested>(_onAuthDemoLoginRequested);
    on<AuthDemoRegisterRequested>(_onAuthDemoRegisterRequested);
    
    // Initialize authentication service
    _initializeAuthService();
  }

  /// Initialize authentication state
  Future<void> _onAuthInitialized(
    AuthInitialized event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final isValid = await _authService.validateSession();
      if (isValid && _authService.currentSession != null) {
        emit(AuthAuthenticated(
          session: _authService.currentSession!,
        ));
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(
        message: 'Failed to initialize authentication',
        errorCode: 'INIT_ERROR',
      ));
    }
  }

  /// Handle login request
  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.login(
        email: event.email,
        password: event.password,
        rememberMe: event.rememberMe,
        twoFactorCode: event.twoFactorCode,
        organizationId: event.organizationId,
      );

      if (result.isSuccess && result.session != null) {
        emit(AuthAuthenticated(
          session: result.session!,
          requiresEmailVerification: result.requiresEmailVerification,
          message: result.message,
        ));
      } else if (result.requiresTwoFactor) {
        emit(AuthRequiresTwoFactor(
          sessionToken: result.sessionToken!,
          availableMethods: result.availableTwoFactorMethods!,
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Login failed',
          errorCode: result.errorCode ?? 'LOGIN_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during login',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle registration request
  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.register(
        email: event.email,
        password: event.password,
        displayName: event.displayName,
        firstName: event.firstName,
        lastName: event.lastName,
        organizationName: event.organizationName,
        invitationToken: event.invitationToken,
        userPreferences: event.userPreferences,
        acceptTerms: event.acceptTerms,
        subscribeToNewsletter: event.subscribeToNewsletter,
      );

      if (result.isSuccess) {
        emit(AuthRegistrationSuccess(
          userId: result.userId!,
          requiresEmailVerification: result.requiresEmailVerification,
          organizationCreated: result.organizationCreated,
          organizationId: result.organizationId,
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Registration failed',
          errorCode: result.errorCode ?? 'REGISTRATION_ERROR',
          passwordPolicy: result.passwordPolicy,
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during registration',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle two-factor verification request
  Future<void> _onAuthTwoFactorVerificationRequested(
    AuthTwoFactorVerificationRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.verifyTwoFactor(
        sessionToken: event.sessionToken,
        method: event.method,
        code: event.code,
      );

      if (result.isSuccess && result.session != null) {
        emit(AuthAuthenticated(
          session: result.session!,
          requiresEmailVerification: result.requiresEmailVerification,
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Two-factor verification failed',
          errorCode: result.errorCode ?? '2FA_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during two-factor verification',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle email verification request
  Future<void> _onAuthEmailVerificationRequested(
    AuthEmailVerificationRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.verifyEmail(
        verificationToken: event.verificationToken,
        email: event.email,
      );

      if (result.isSuccess) {
        // Update current session if user is authenticated
        if (state is AuthAuthenticated) {
          final currentState = state as AuthAuthenticated;
          final updatedUser = currentState.session.user.copyWith(
            status: UserStatus.active,
          );
          final updatedSession = currentState.session.copyWith(user: updatedUser);
          
          emit(AuthAuthenticated(
            session: updatedSession,
            message: result.message,
            emailVerified: true,
          ));
        } else {
          emit(AuthEmailVerified(
            message: result.message,
          ));
        }
      } else {
        emit(AuthError(
          message: result.message ?? 'Email verification failed',
          errorCode: result.errorCode ?? 'VERIFICATION_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during email verification',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle password reset request
  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.requestPasswordReset(event.email);

      if (result.isSuccess) {
        emit(AuthPasswordResetSent(
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Password reset request failed',
          errorCode: result.errorCode ?? 'RESET_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during password reset request',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle password reset confirmation
  Future<void> _onAuthPasswordResetConfirmed(
    AuthPasswordResetConfirmed event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final result = await _authService.resetPassword(
        resetToken: event.resetToken,
        newPassword: event.newPassword,
      );

      if (result.isSuccess) {
        emit(AuthPasswordResetComplete(
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Password reset failed',
          errorCode: result.errorCode ?? 'RESET_ERROR',
          passwordPolicy: result.passwordPolicy,
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during password reset',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle password change request
  Future<void> _onAuthPasswordChangeRequested(
    AuthPasswordChangeRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state is! AuthAuthenticated) {
      emit(const AuthError(
        message: 'Must be authenticated to change password',
        errorCode: 'AUTH_REQUIRED',
      ));
      return;
    }

    emit(const AuthLoading());
    
    try {
      final result = await _authService.changePassword(
        currentPassword: event.currentPassword,
        newPassword: event.newPassword,
      );

      if (result.isSuccess) {
        final currentState = state as AuthAuthenticated;
        emit(AuthAuthenticated(
          session: currentState.session,
          message: result.message,
          passwordChanged: true,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Password change failed',
          errorCode: result.errorCode ?? 'CHANGE_ERROR',
          passwordPolicy: result.passwordPolicy,
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during password change',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle profile update request
  Future<void> _onAuthProfileUpdateRequested(
    AuthProfileUpdateRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state is! AuthAuthenticated) {
      emit(const AuthError(
        message: 'Must be authenticated to update profile',
        errorCode: 'AUTH_REQUIRED',
      ));
      return;
    }

    emit(const AuthLoading());
    
    try {
      final result = await _authService.updateProfile(
        displayName: event.displayName,
        firstName: event.firstName,
        lastName: event.lastName,
        preferences: event.preferences,
      );

      if (result.isSuccess) {
        // Updated session is handled by the service
        emit(AuthAuthenticated(
          session: _authService.currentSession!,
          message: result.message,
          profileUpdated: true,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Profile update failed',
          errorCode: result.errorCode ?? 'PROFILE_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during profile update',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle two-factor setup request
  Future<void> _onAuthTwoFactorSetupRequested(
    AuthTwoFactorSetupRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state is! AuthAuthenticated) {
      emit(const AuthError(
        message: 'Must be authenticated to setup two-factor authentication',
        errorCode: 'AUTH_REQUIRED',
      ));
      return;
    }

    emit(const AuthLoading());
    
    try {
      final result = await _authService.setupTwoFactor(
        method: event.method,
        phoneNumber: event.phoneNumber,
        email: event.email,
      );

      if (result.isSuccess) {
        emit(AuthTwoFactorSetupComplete(
          method: result.method!,
          secret: result.secret,
          qrCode: result.qrCode,
          backupCodes: result.backupCodes,
          phoneNumber: result.phoneNumber,
          email: result.email,
          message: result.message,
        ));
      } else {
        emit(AuthError(
          message: result.message ?? 'Two-factor setup failed',
          errorCode: result.errorCode ?? '2FA_SETUP_ERROR',
        ));
      }
    } catch (e) {
      emit(AuthError(
        message: 'Network error during two-factor setup',
        errorCode: 'NETWORK_ERROR',
      ));
    }
  }

  /// Handle logout request
  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authService.logout();
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails on server, clear local state
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle session validation request
  Future<void> _onAuthSessionValidationRequested(
    AuthSessionValidationRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final isValid = await _authService.validateSession();
      if (!isValid) {
        emit(const AuthUnauthenticated());
      }
      // If valid, keep current state
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle token refresh request
  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final success = await _authService.refreshToken();
      if (success && _authService.currentSession != null) {
        emit(AuthAuthenticated(
          session: _authService.currentSession!,
        ));
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }

  /// Check if user has specific permission
  bool hasPermission(Permission permission) {
    return _authService.hasPermission(permission);
  }

  /// Check if user has permission in specific organization
  bool hasPermissionInOrganization(String organizationId, Permission permission) {
    return _authService.hasPermissionInOrganization(organizationId, permission);
  }

  /// Check if user is admin
  bool get isAdmin => _authService.isAdmin;

  /// Check if user is system admin
  bool get isSystemAdmin => _authService.isSystemAdmin;

  /// Get current user
  User? get currentUser => _authService.currentSession?.user;

  /// Get current session
  AuthSession? get currentSession => _authService.currentSession;

  /// Initialize authentication service
  Future<void> _initializeAuthService() async {
    try {
      await _authService.initialize();
      add(const AuthInitialized());
    } catch (e) {
      add(const AuthInitialized()); // Still initialize to avoid blocking the app
    }
  }

  /// Clear error state
  void _onAuthErrorCleared(
    AuthErrorCleared event,
    Emitter<AuthenticationState> emit,
  ) {
    if (state is AuthError) {
      emit(const AuthUnauthenticated());
    }
  }

  /// Clear success message
  void _onAuthSuccessMessageCleared(
    AuthSuccessMessageCleared event,
    Emitter<AuthenticationState> emit,
  ) {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      emit(AuthAuthenticated(
        session: currentState.session,
        requiresEmailVerification: currentState.requiresEmailVerification,
      ));
    }
  }

  /// Handle demo login request
  Future<void> _onAuthDemoLoginRequested(
    AuthDemoLoginRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    // Simulate a slight delay for realistic demo experience
    await Future.delayed(const Duration(milliseconds: 1500));
    
    try {
      // Create a demo session with mock data
      final demoUser = User(
        id: 'demo-user-001',
        email: '<EMAIL>',
        displayName: 'Demo User',
        firstName: 'Demo',
        lastName: 'User',
        role: UserRole.apprentice,
        status: UserStatus.active,
        totalPoints: 2500,
        currentLevelPoints: 500,
        level: 3,
        currentStreak: 7,
        longestStreak: 14,
        achievementCount: 8,
        questsCompleted: 12,
        tasksCompleted: 45,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        preferences: const {
          'language': 'en',
          'timezone': 'UTC',
          'theme': 'system',
          'notifications': {
            'email': true,
            'push': true,
            'questUpdates': true,
            'achievements': true,
            'weeklyDigest': true,
          },
        },
      );

      final demoSession = AuthSession(
        token: 'demo-access-token',
        refreshToken: 'demo-refresh-token',
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
        user: demoUser,
        organizationRoles: [],
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        lastActivityAt: DateTime.now(),
      );

      emit(AuthAuthenticated(
        session: demoSession,
        message: 'Welcome to the Quester demo!',
      ));
    } catch (e) {
      emit(const AuthError(
        message: 'Demo login failed. Please try again.',
        errorCode: 'DEMO_ERROR',
      ));
    }
  }

  /// Handle demo registration request
  Future<void> _onAuthDemoRegisterRequested(
    AuthDemoRegisterRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(const AuthLoading());
    
    // Simulate a slight delay for realistic demo experience
    await Future.delayed(const Duration(milliseconds: 2000));
    
    try {
      final demoUserId = 'demo-user-${DateTime.now().millisecondsSinceEpoch}';
      
      emit(AuthRegistrationSuccess(
        userId: demoUserId,
        requiresEmailVerification: false,
        organizationCreated: true,
        organizationId: 'demo-org-$demoUserId',
        message: 'Demo account created successfully! You can now explore all features.',
      ));
    } catch (e) {
      emit(const AuthError(
        message: 'Demo registration failed. Please try again.',
        errorCode: 'DEMO_REGISTER_ERROR',
      ));
    }
  }
}