import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Base authentication event
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize authentication system
class AuthInitialized extends AuthEvent {
  const AuthInitialized();
}

/// Login request event
class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;
  final String? twoFactorCode;
  final String? organizationId;

  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
    this.twoFactorCode,
    this.organizationId,
  });

  @override
  List<Object?> get props => [
        email,
        password,
        rememberMe,
        twoFactorCode,
        organizationId,
      ];
}

/// Registration request event
class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String displayName;
  final String? firstName;
  final String? lastName;
  final String? organizationName;
  final String? invitationToken;
  final Map<String, dynamic>? userPreferences;
  final bool acceptTerms;
  final bool subscribeToNewsletter;

  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.displayName,
    this.firstName,
    this.lastName,
    this.organizationName,
    this.invitationToken,
    this.userPreferences,
    required this.acceptTerms,
    this.subscribeToNewsletter = false,
  });

  @override
  List<Object?> get props => [
        email,
        password,
        displayName,
        firstName,
        lastName,
        organizationName,
        invitationToken,
        userPreferences,
        acceptTerms,
        subscribeToNewsletter,
      ];
}

/// Two-factor authentication verification request
class AuthTwoFactorVerificationRequested extends AuthEvent {
  final String sessionToken;
  final TwoFactorMethod method;
  final String code;

  const AuthTwoFactorVerificationRequested({
    required this.sessionToken,
    required this.method,
    required this.code,
  });

  @override
  List<Object?> get props => [sessionToken, method, code];
}

/// Email verification request
class AuthEmailVerificationRequested extends AuthEvent {
  final String verificationToken;
  final String email;

  const AuthEmailVerificationRequested({
    required this.verificationToken,
    required this.email,
  });

  @override
  List<Object?> get props => [verificationToken, email];
}

/// Password reset request
class AuthPasswordResetRequested extends AuthEvent {
  final String email;

  const AuthPasswordResetRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Password reset confirmation
class AuthPasswordResetConfirmed extends AuthEvent {
  final String resetToken;
  final String newPassword;

  const AuthPasswordResetConfirmed({
    required this.resetToken,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [resetToken, newPassword];
}

/// Password change request (authenticated user)
class AuthPasswordChangeRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const AuthPasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

/// Profile update request
class AuthProfileUpdateRequested extends AuthEvent {
  final String? displayName;
  final String? firstName;
  final String? lastName;
  final Map<String, dynamic>? preferences;

  const AuthProfileUpdateRequested({
    this.displayName,
    this.firstName,
    this.lastName,
    this.preferences,
  });

  @override
  List<Object?> get props => [
        displayName,
        firstName,
        lastName,
        preferences,
      ];
}

/// Two-factor authentication setup request
class AuthTwoFactorSetupRequested extends AuthEvent {
  final TwoFactorMethod method;
  final String? phoneNumber;
  final String? email;

  const AuthTwoFactorSetupRequested({
    required this.method,
    this.phoneNumber,
    this.email,
  });

  @override
  List<Object?> get props => [method, phoneNumber, email];
}

/// Logout request
class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

/// Session validation request
class AuthSessionValidationRequested extends AuthEvent {
  const AuthSessionValidationRequested();
}

/// Token refresh request
class AuthTokenRefreshRequested extends AuthEvent {
  const AuthTokenRefreshRequested();
}

/// OAuth login request
class AuthOAuthLoginRequested extends AuthEvent {
  final OAuthProvider provider;
  final String authorizationCode;
  final String? state;
  final String? redirectUri;
  final String? organizationId;

  const AuthOAuthLoginRequested({
    required this.provider,
    required this.authorizationCode,
    this.state,
    this.redirectUri,
    this.organizationId,
  });

  @override
  List<Object?> get props => [
        provider,
        authorizationCode,
        state,
        redirectUri,
        organizationId,
      ];
}

/// Resend verification email request
class AuthResendVerificationRequested extends AuthEvent {
  const AuthResendVerificationRequested();
}

/// Invitation acceptance request
class AuthInvitationAcceptanceRequested extends AuthEvent {
  final String invitationToken;
  final String? password;
  final String? displayName;

  const AuthInvitationAcceptanceRequested({
    required this.invitationToken,
    this.password,
    this.displayName,
  });

  @override
  List<Object?> get props => [invitationToken, password, displayName];
}

/// Check email availability request
class AuthEmailAvailabilityRequested extends AuthEvent {
  final String email;

  const AuthEmailAvailabilityRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Get password policy request
class AuthPasswordPolicyRequested extends AuthEvent {
  const AuthPasswordPolicyRequested();
}

/// User impersonation request (admin only)
class AuthUserImpersonationRequested extends AuthEvent {
  final String targetUserId;
  final String reason;
  final int? durationMinutes;

  const AuthUserImpersonationRequested({
    required this.targetUserId,
    required this.reason,
    this.durationMinutes,
  });

  @override
  List<Object?> get props => [targetUserId, reason, durationMinutes];
}

/// End user impersonation request
class AuthEndImpersonationRequested extends AuthEvent {
  const AuthEndImpersonationRequested();
}

/// Update avatar request
class AuthAvatarUpdateRequested extends AuthEvent {
  final String avatarPath; // Local file path or URL

  const AuthAvatarUpdateRequested({required this.avatarPath});

  @override
  List<Object?> get props => [avatarPath];
}

/// Delete account request
class AuthAccountDeletionRequested extends AuthEvent {
  final String password; // Confirmation password
  final String reason;

  const AuthAccountDeletionRequested({
    required this.password,
    required this.reason,
  });

  @override
  List<Object?> get props => [password, reason];
}

/// Disable two-factor authentication request
class AuthTwoFactorDisableRequested extends AuthEvent {
  final String password; // Confirmation password
  final String? backupCode; // Or backup code

  const AuthTwoFactorDisableRequested({
    required this.password,
    this.backupCode,
  });

  @override
  List<Object?> get props => [password, backupCode];
}

/// Session management request
class AuthSessionManagementRequested extends AuthEvent {
  final String action; // 'terminate', 'refresh', 'extend'
  final String? targetSessionId; // For admin actions
  final Map<String, dynamic>? parameters;

  const AuthSessionManagementRequested({
    required this.action,
    this.targetSessionId,
    this.parameters,
  });

  @override
  List<Object?> get props => [action, targetSessionId, parameters];
}

/// Clear error state
class AuthErrorCleared extends AuthEvent {
  const AuthErrorCleared();
}

/// Clear success message
class AuthSuccessMessageCleared extends AuthEvent {
  const AuthSuccessMessageCleared();
}

/// Demo login request event
class AuthDemoLoginRequested extends AuthEvent {
  const AuthDemoLoginRequested();
}

/// Demo registration request event
class AuthDemoRegisterRequested extends AuthEvent {
  const AuthDemoRegisterRequested();
}