import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Base authentication state
abstract class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object?> get props => [];


  // Helper getters
  bool get isAuthenticated => this is AuthAuthenticated;
  bool get isLoading => this is AuthLoading;
  bool get hasError => this is AuthError;
  bool get requiresTwoFactor => this is AuthRequiresTwoFactor;

  // Get session if authenticated
  AuthSession? get session {
    if (this is AuthAuthenticated) {
      return (this as AuthAuthenticated).session;
    }
    return null;
  }

  // Get current user if authenticated
  User? get user => session?.user;

  // Get error message if in error state
  String? get errorMessage {
    if (this is AuthError) {
      return (this as AuthError).message;
    }
    return null;
  }

  // Get success message if applicable
  String? get successMessage {
    if (this is AuthAuthenticated) return (this as AuthAuthenticated).message;
    if (this is AuthRegistrationSuccess) return (this as AuthRegistrationSuccess).message;
    if (this is AuthEmailVerified) return (this as AuthEmailVerified).message;
    if (this is AuthPasswordResetSent) return (this as AuthPasswordResetSent).message;
    if (this is AuthPasswordResetComplete) return (this as AuthPasswordResetComplete).message;
    if (this is AuthTwoFactorSetupComplete) return (this as AuthTwoFactorSetupComplete).message;
    if (this is AuthRequiresTwoFactor) return (this as AuthRequiresTwoFactor).message;
    return null;
  }
}

/// Initial authentication state
class AuthInitial extends AuthenticationState {
  const AuthInitial();

  @override
  String toString() => 'AuthInitial';
}

/// Loading state during authentication operations
class AuthLoading extends AuthenticationState {
  const AuthLoading();

  @override
  String toString() => 'AuthLoading';
}

/// User is not authenticated
class AuthUnauthenticated extends AuthenticationState {
  const AuthUnauthenticated();

  @override
  String toString() => 'AuthUnauthenticated';
}

/// User is successfully authenticated
class AuthAuthenticated extends AuthenticationState {
  @override
  final AuthSession session;
  final bool requiresEmailVerification;
  final String? message;
  final bool emailVerified;
  final bool passwordChanged;
  final bool profileUpdated;

  const AuthAuthenticated({
    required this.session,
    this.requiresEmailVerification = false,
    this.message,
    this.emailVerified = false,
    this.passwordChanged = false,
    this.profileUpdated = false,
  });

  @override
  List<Object?> get props => [
        session,
        requiresEmailVerification,
        message,
        emailVerified,
        passwordChanged,
        profileUpdated,
      ];

  @override
  String toString() => 'AuthAuthenticated(user: ${session.user.email})';
}

/// Two-factor authentication is required
class AuthRequiresTwoFactor extends AuthenticationState {
  final String sessionToken;
  final List<TwoFactorMethod> availableMethods;
  final String? message;

  const AuthRequiresTwoFactor({
    required this.sessionToken,
    required this.availableMethods,
    this.message,
  });

  @override
  List<Object?> get props => [sessionToken, availableMethods, message];

  @override
  String toString() => 'AuthRequiresTwoFactor(methods: $availableMethods)';
}

/// Registration was successful
class AuthRegistrationSuccess extends AuthenticationState {
  final String userId;
  final bool requiresEmailVerification;
  final bool organizationCreated;
  final String? organizationId;
  final String? message;

  const AuthRegistrationSuccess({
    required this.userId,
    this.requiresEmailVerification = true,
    this.organizationCreated = false,
    this.organizationId,
    this.message,
  });

  @override
  List<Object?> get props => [
        userId,
        requiresEmailVerification,
        organizationCreated,
        organizationId,
        message,
      ];

  @override
  String toString() => 'AuthRegistrationSuccess(userId: $userId)';
}

/// Email verification completed
class AuthEmailVerified extends AuthenticationState {
  final String? message;

  const AuthEmailVerified({this.message});

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'AuthEmailVerified';
}

/// Password reset email sent
class AuthPasswordResetSent extends AuthenticationState {
  final String? message;

  const AuthPasswordResetSent({this.message});

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'AuthPasswordResetSent';
}

/// Password reset completed
class AuthPasswordResetComplete extends AuthenticationState {
  final String? message;

  const AuthPasswordResetComplete({this.message});

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'AuthPasswordResetComplete';
}

/// Two-factor authentication setup completed
class AuthTwoFactorSetupComplete extends AuthenticationState {
  final TwoFactorMethod method;
  final String? secret;
  final String? qrCode;
  final List<String>? backupCodes;
  final String? phoneNumber;
  final String? email;
  final String? message;

  const AuthTwoFactorSetupComplete({
    required this.method,
    this.secret,
    this.qrCode,
    this.backupCodes,
    this.phoneNumber,
    this.email,
    this.message,
  });

  @override
  List<Object?> get props => [
        method,
        secret,
        qrCode,
        backupCodes,
        phoneNumber,
        email,
        message,
      ];

  @override
  String toString() => 'AuthTwoFactorSetupComplete(method: $method)';
}

/// Authentication error state
class AuthError extends AuthenticationState {
  final String message;
  final String errorCode;
  final Map<String, dynamic>? passwordPolicy;
  final Map<String, dynamic>? additionalData;

  const AuthError({
    required this.message,
    required this.errorCode,
    this.passwordPolicy,
    this.additionalData,
  });

  @override
  List<Object?> get props => [message, errorCode, passwordPolicy, additionalData];

  @override
  String toString() => 'AuthError(message: $message, code: $errorCode)';

  // Helper getters for specific error types
  bool get isNetworkError => errorCode == 'NETWORK_ERROR';
  bool get isInvalidCredentials => errorCode == 'AUTH_INVALID_CREDENTIALS';
  bool get isAccountInactive => errorCode == 'AUTH_ACCOUNT_INACTIVE';
  bool get isWeakPassword => errorCode == 'AUTH_WEAK_PASSWORD';
  bool get isUserExists => errorCode == 'AUTH_USER_EXISTS';
  bool get isInvalidToken => errorCode == 'AUTH_INVALID_TOKEN';
  bool get isInsufficientPermissions => errorCode == 'AUTH_INSUFFICIENT_PERMISSIONS';
  bool get requires2FA => errorCode == 'AUTH_INVALID_2FA';
  bool get isEmailVerificationRequired => errorCode == 'AUTH_EMAIL_VERIFICATION_REQUIRED';

  // Get user-friendly error message
  String get userFriendlyMessage {
    switch (errorCode) {
      case 'AUTH_INVALID_CREDENTIALS':
        return 'Invalid email or password. Please try again.';
      case 'AUTH_ACCOUNT_INACTIVE':
        return 'Your account is inactive. Please contact support.';
      case 'AUTH_WEAK_PASSWORD':
        return 'Password does not meet security requirements.';
      case 'AUTH_USER_EXISTS':
        return 'An account with this email already exists.';
      case 'AUTH_INVALID_TOKEN':
        return 'The verification link is invalid or expired.';
      case 'AUTH_INSUFFICIENT_PERMISSIONS':
        return 'You do not have permission to perform this action.';
      case 'AUTH_INVALID_2FA':
        return 'Invalid two-factor authentication code.';
      case 'NETWORK_ERROR':
        return 'Network error. Please check your connection and try again.';
      case 'AUTH_EMAIL_VERIFICATION_REQUIRED':
        return 'Please verify your email address to continue.';
      default:
        return message;
    }
  }

  // Check if error is recoverable
  bool get isRecoverable {
    switch (errorCode) {
      case 'NETWORK_ERROR':
      case 'AUTH_INVALID_CREDENTIALS':
      case 'AUTH_WEAK_PASSWORD':
      case 'AUTH_INVALID_2FA':
        return true;
      case 'AUTH_ACCOUNT_INACTIVE':
      case 'AUTH_INSUFFICIENT_PERMISSIONS':
        return false;
      default:
        return true;
    }
  }

  // Get suggested action for error
  String? get suggestedAction {
    switch (errorCode) {
      case 'AUTH_INVALID_CREDENTIALS':
        return 'Check your email and password, or use "Forgot Password" to reset.';
      case 'AUTH_WEAK_PASSWORD':
        return 'Use a stronger password with uppercase, lowercase, numbers, and symbols.';
      case 'AUTH_USER_EXISTS':
        return 'Try logging in instead, or use "Forgot Password" if needed.';
      case 'AUTH_INVALID_TOKEN':
        return 'Request a new verification email or reset link.';
      case 'AUTH_ACCOUNT_INACTIVE':
        return 'Contact support for assistance with your account.';
      case 'NETWORK_ERROR':
        return 'Check your internet connection and try again.';
      case 'AUTH_EMAIL_VERIFICATION_REQUIRED':
        return 'Check your email for a verification link.';
      default:
        return null;
    }
  }
}

/// Email availability check result
class AuthEmailAvailabilityChecked extends AuthenticationState {
  final String email;
  final bool isAvailable;

  const AuthEmailAvailabilityChecked({
    required this.email,
    required this.isAvailable,
  });

  @override
  List<Object?> get props => [email, isAvailable];

  @override
  String toString() => 'AuthEmailAvailabilityChecked(email: $email, available: $isAvailable)';
}

/// Password policy information
class AuthPasswordPolicyLoaded extends AuthenticationState {
  final Map<String, dynamic> policy;

  const AuthPasswordPolicyLoaded({required this.policy});

  @override
  List<Object?> get props => [policy];

  @override
  String toString() => 'AuthPasswordPolicyLoaded';
}

/// User sessions information (for admin/user management)
class AuthUserSessionsLoaded extends AuthenticationState {
  final List<Map<String, dynamic>> sessions;

  const AuthUserSessionsLoaded({required this.sessions});

  @override
  List<Object?> get props => [sessions];

  @override
  String toString() => 'AuthUserSessionsLoaded(sessions: ${sessions.length})';
}

/// Impersonation state (for admin)
class AuthImpersonationActive extends AuthenticationState {
  final AuthSession impersonatedSession;
  final String adminUserId;
  final String reason;
  final DateTime? expiresAt;

  const AuthImpersonationActive({
    required this.impersonatedSession,
    required this.adminUserId,
    required this.reason,
    this.expiresAt,
  });

  @override
  List<Object?> get props => [
        impersonatedSession,
        adminUserId,
        reason,
        expiresAt,
      ];

  @override
  String toString() => 'AuthImpersonationActive(target: ${impersonatedSession.user.email})';
}

/// Avatar update result
class AuthAvatarUpdated extends AuthenticationState {
  final String avatarUrl;
  final String? message;

  const AuthAvatarUpdated({
    required this.avatarUrl,
    this.message,
  });

  @override
  List<Object?> get props => [avatarUrl, message];

  @override
  String toString() => 'AuthAvatarUpdated';
}