import 'package:formz/formz.dart';

/// Email validation errors
enum EmailValidationError {
  empty,
  invalid,
  alreadyExists,
  notFound,
}

/// Email input with validation
class Email extends FormzInput<String, EmailValidationError> {
  const Email.pure([super.value = '']) : super.pure();
  const Email.dirty([super.value = '']) : super.dirty();

  static final RegExp _emailRegExp = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  @override
  EmailValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return EmailValidationError.empty;
    }
    
    if (!_emailRegExp.hasMatch(trimmed)) {
      return EmailValidationError.invalid;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case EmailValidationError.empty:
        return 'Email is required';
      case EmailValidationError.invalid:
        return 'Please enter a valid email address';
      case EmailValidationError.alreadyExists:
        return 'An account with this email already exists';
      case EmailValidationError.notFound:
        return 'No account found with this email address';
      case null:
        return null;
    }
  }
}

/// Password validation errors
enum PasswordValidationError {
  empty,
  tooShort,
  noUppercase,
  noLowercase,
  noDigit,
  noSpecialChar,
  tooWeak,
  incorrect,
}

/// Password input with validation
class Password extends FormzInput<String, PasswordValidationError> {
  const Password.pure([super.value = '']) : super.pure();
  const Password.dirty([super.value = '']) : super.dirty();

  static const int minLength = 8;
  static final RegExp _uppercaseRegExp = RegExp(r'[A-Z]');
  static final RegExp _lowercaseRegExp = RegExp(r'[a-z]');
  static final RegExp _digitRegExp = RegExp(r'\d');
  static final RegExp _specialCharRegExp = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  @override
  PasswordValidationError? validator(String value) {
    if (value.isEmpty) {
      return PasswordValidationError.empty;
    }
    
    if (value.length < minLength) {
      return PasswordValidationError.tooShort;
    }
    
    if (!_uppercaseRegExp.hasMatch(value)) {
      return PasswordValidationError.noUppercase;
    }
    
    if (!_lowercaseRegExp.hasMatch(value)) {
      return PasswordValidationError.noLowercase;
    }
    
    if (!_digitRegExp.hasMatch(value)) {
      return PasswordValidationError.noDigit;
    }
    
    if (!_specialCharRegExp.hasMatch(value)) {
      return PasswordValidationError.noSpecialChar;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case PasswordValidationError.empty:
        return 'Password is required';
      case PasswordValidationError.tooShort:
        return 'Password must be at least $minLength characters';
      case PasswordValidationError.noUppercase:
        return 'Password must contain at least one uppercase letter';
      case PasswordValidationError.noLowercase:
        return 'Password must contain at least one lowercase letter';
      case PasswordValidationError.noDigit:
        return 'Password must contain at least one number';
      case PasswordValidationError.noSpecialChar:
        return 'Password must contain at least one special character';
      case PasswordValidationError.tooWeak:
        return 'Password is too weak';
      case PasswordValidationError.incorrect:
        return 'Incorrect password';
      case null:
        return null;
    }
  }

  /// Get password strength score (0-4)
  int get strengthScore {
    int score = 0;
    
    if (value.length >= minLength) score++;
    if (_uppercaseRegExp.hasMatch(value)) score++;
    if (_lowercaseRegExp.hasMatch(value)) score++;
    if (_digitRegExp.hasMatch(value)) score++;
    if (_specialCharRegExp.hasMatch(value)) score++;
    
    return score;
  }

  /// Get password strength text
  String get strengthText {
    switch (strengthScore) {
      case 0:
      case 1:
        return 'Very Weak';
      case 2:
        return 'Weak';
      case 3:
        return 'Moderate';
      case 4:
        return 'Strong';
      case 5:
        return 'Very Strong';
      default:
        return 'Unknown';
    }
  }

  /// Get password strength color
  String get strengthColor {
    switch (strengthScore) {
      case 0:
      case 1:
        return 'error';
      case 2:
        return 'warning';
      case 3:
        return 'info';
      case 4:
      case 5:
        return 'success';
      default:
        return 'surface';
    }
  }
}

/// Confirm password validation errors
enum ConfirmPasswordValidationError {
  empty,
  mismatch,
}

/// Confirm password input with validation
class ConfirmPassword extends FormzInput<String, ConfirmPasswordValidationError> {
  final String originalPassword;
  
  const ConfirmPassword.pure([this.originalPassword = '', super.value = '']) : super.pure();
  const ConfirmPassword.dirty(this.originalPassword, [super.value = '']) : super.dirty();

  @override
  ConfirmPasswordValidationError? validator(String value) {
    if (value.isEmpty) {
      return ConfirmPasswordValidationError.empty;
    }
    
    if (value != originalPassword) {
      return ConfirmPasswordValidationError.mismatch;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case ConfirmPasswordValidationError.empty:
        return 'Please confirm your password';
      case ConfirmPasswordValidationError.mismatch:
        return 'Passwords do not match';
      case null:
        return null;
    }
  }
}

/// Username validation errors
enum UsernameValidationError {
  empty,
  tooShort,
  tooLong,
  invalidCharacters,
  alreadyExists,
  reserved,
}

/// Username input with validation
class Username extends FormzInput<String, UsernameValidationError> {
  const Username.pure([super.value = '']) : super.pure();
  const Username.dirty([super.value = '']) : super.dirty();

  static const int minLength = 3;
  static const int maxLength = 30;
  static final RegExp _usernameRegExp = RegExp(r'^[a-zA-Z0-9_.-]+$');
  static final Set<String> _reservedUsernames = {
    'admin', 'administrator', 'root', 'system', 'support', 'help',
    'api', 'www', 'mail', 'email', 'test', 'demo', 'guest', 'anonymous'
  };

  @override
  UsernameValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return UsernameValidationError.empty;
    }
    
    if (trimmed.length < minLength) {
      return UsernameValidationError.tooShort;
    }
    
    if (trimmed.length > maxLength) {
      return UsernameValidationError.tooLong;
    }
    
    if (!_usernameRegExp.hasMatch(trimmed)) {
      return UsernameValidationError.invalidCharacters;
    }
    
    if (_reservedUsernames.contains(trimmed.toLowerCase())) {
      return UsernameValidationError.reserved;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case UsernameValidationError.empty:
        return 'Username is required';
      case UsernameValidationError.tooShort:
        return 'Username must be at least $minLength characters';
      case UsernameValidationError.tooLong:
        return 'Username cannot exceed $maxLength characters';
      case UsernameValidationError.invalidCharacters:
        return 'Username can only contain letters, numbers, dots, hyphens, and underscores';
      case UsernameValidationError.alreadyExists:
        return 'This username is already taken';
      case UsernameValidationError.reserved:
        return 'This username is reserved and cannot be used';
      case null:
        return null;
    }
  }
}

/// Full name validation errors
enum FullNameValidationError {
  empty,
  tooShort,
  tooLong,
  invalidCharacters,
}

/// Full name input with validation
class FullName extends FormzInput<String, FullNameValidationError> {
  const FullName.pure([super.value = '']) : super.pure();
  const FullName.dirty([super.value = '']) : super.dirty();

  static const int minLength = 2;
  static const int maxLength = 100;
  static final RegExp _nameRegExp = RegExp(r"^[a-zA-Z\s\-'.]+$");

  @override
  FullNameValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return FullNameValidationError.empty;
    }
    
    if (trimmed.length < minLength) {
      return FullNameValidationError.tooShort;
    }
    
    if (trimmed.length > maxLength) {
      return FullNameValidationError.tooLong;
    }
    
    if (!_nameRegExp.hasMatch(trimmed)) {
      return FullNameValidationError.invalidCharacters;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case FullNameValidationError.empty:
        return 'Full name is required';
      case FullNameValidationError.tooShort:
        return 'Full name must be at least $minLength characters';
      case FullNameValidationError.tooLong:
        return 'Full name cannot exceed $maxLength characters';
      case FullNameValidationError.invalidCharacters:
        return 'Full name can only contain letters, spaces, hyphens, apostrophes, and dots';
      case null:
        return null;
    }
  }
}

/// Phone number validation errors
enum PhoneValidationError {
  invalid,
  tooShort,
  tooLong,
}

/// Phone number input with validation (optional field)
class PhoneNumber extends FormzInput<String, PhoneValidationError> {
  const PhoneNumber.pure([super.value = '']) : super.pure();
  const PhoneNumber.dirty([super.value = '']) : super.dirty();

  static const int minLength = 10;
  static const int maxLength = 15;
  static final RegExp _phoneRegExp = RegExp(r'^\+?[\d\s\-()]+$');

  @override
  PhoneValidationError? validator(String value) {
    final trimmed = value.trim();
    
    // Phone is optional, so empty is valid
    if (trimmed.isEmpty) return null;
    
    final digitsOnly = trimmed.replaceAll(RegExp(r'[\s\-()]+'), '');
    
    if (digitsOnly.length < minLength) {
      return PhoneValidationError.tooShort;
    }
    
    if (digitsOnly.length > maxLength) {
      return PhoneValidationError.tooLong;
    }
    
    if (!_phoneRegExp.hasMatch(trimmed)) {
      return PhoneValidationError.invalid;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case PhoneValidationError.invalid:
        return 'Please enter a valid phone number';
      case PhoneValidationError.tooShort:
        return 'Phone number must have at least $minLength digits';
      case PhoneValidationError.tooLong:
        return 'Phone number cannot exceed $maxLength digits';
      case null:
        return null;
    }
  }
}

/// Two-factor authentication code validation errors
enum TwoFactorCodeValidationError {
  empty,
  invalid,
  expired,
  incorrect,
}

/// Two-factor authentication code input with validation
class TwoFactorCode extends FormzInput<String, TwoFactorCodeValidationError> {
  const TwoFactorCode.pure([super.value = '']) : super.pure();
  const TwoFactorCode.dirty([super.value = '']) : super.dirty();

  static const int codeLength = 6;
  static final RegExp _codeRegExp = RegExp(r'^\d{6}$');

  @override
  TwoFactorCodeValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return TwoFactorCodeValidationError.empty;
    }
    
    if (!_codeRegExp.hasMatch(trimmed)) {
      return TwoFactorCodeValidationError.invalid;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case TwoFactorCodeValidationError.empty:
        return 'Verification code is required';
      case TwoFactorCodeValidationError.invalid:
        return 'Verification code must be $codeLength digits';
      case TwoFactorCodeValidationError.expired:
        return 'Verification code has expired';
      case TwoFactorCodeValidationError.incorrect:
        return 'Incorrect verification code';
      case null:
        return null;
    }
  }
}

/// Complete login form validation
class LoginFormValidation {
  final Email email;
  final Password password;

  const LoginFormValidation({
    this.email = const Email.pure(),
    this.password = const Password.pure(),
  });

  /// Check if the entire form is valid
  bool get isValid {
    return Formz.validate([email, password]);
  }

  /// Get all form inputs for validation
  List<FormzInput> get inputs => [email, password];

  /// Get form status
  // FormzStatus get status {
  //   return Formz.validate(inputs);
  // }
  bool get isValidLogin {
    return email.isPure == false && password.isPure == false;
  }

  /// Copy with new values
  LoginFormValidation copyWith({
    Email? email,
    Password? password,
  }) {
    return LoginFormValidation(
      email: email ?? this.email,
      password: password ?? this.password,
    );
  }

  /// Get field errors map for display
  Map<String, String> get fieldErrors {
    final errors = <String, String>{};
    
    if (email.errorMessage != null) {
      errors['email'] = email.errorMessage!;
    }
    
    if (password.errorMessage != null) {
      errors['password'] = password.errorMessage!;
    }
    
    return errors;
  }
}

/// Complete registration form validation
class RegistrationFormValidation {
  final Email email;
  final Username username;
  final FullName fullName;
  final Password password;
  final ConfirmPassword confirmPassword;
  final PhoneNumber phoneNumber;

  const RegistrationFormValidation({
    this.email = const Email.pure(),
    this.username = const Username.pure(),
    this.fullName = const FullName.pure(),
    this.password = const Password.pure(),
    this.confirmPassword = const ConfirmPassword.pure(),
    this.phoneNumber = const PhoneNumber.pure(),
  });

  /// Check if the entire form is valid
  bool get isValid {
    return Formz.validate([
      email,
      username,
      fullName,
      password,
      confirmPassword,
      phoneNumber,
    ]);
  }

  /// Get all form inputs for validation
  List<FormzInput> get inputs => [
    email,
    username,
    fullName,
    password,
    confirmPassword,
    phoneNumber,
  ];

  /// Get form status
  // FormzStatus get status {
  //   return Formz.validate(inputs);
  // }
  bool get isValidForm {
    return email.isPure == false && username.isPure == false && 
           password.isPure == false && confirmPassword.isPure == false;
  }

  /// Copy with new values
  RegistrationFormValidation copyWith({
    Email? email,
    Username? username,
    FullName? fullName,
    Password? password,
    ConfirmPassword? confirmPassword,
    PhoneNumber? phoneNumber,
  }) {
    return RegistrationFormValidation(
      email: email ?? this.email,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }

  /// Get field errors map for display
  Map<String, String> get fieldErrors {
    final errors = <String, String>{};
    
    if (email.errorMessage != null) {
      errors['email'] = email.errorMessage!;
    }
    
    if (username.errorMessage != null) {
      errors['username'] = username.errorMessage!;
    }
    
    if (fullName.errorMessage != null) {
      errors['fullName'] = fullName.errorMessage!;
    }
    
    if (password.errorMessage != null) {
      errors['password'] = password.errorMessage!;
    }
    
    if (confirmPassword.errorMessage != null) {
      errors['confirmPassword'] = confirmPassword.errorMessage!;
    }
    
    if (phoneNumber.errorMessage != null) {
      errors['phoneNumber'] = phoneNumber.errorMessage!;
    }
    
    return errors;
  }

  /// Get all error messages
  List<String> get allErrors {
    return [
      email.errorMessage,
      username.errorMessage,
      fullName.errorMessage,
      password.errorMessage,
      confirmPassword.errorMessage,
      phoneNumber.errorMessage,
    ].where((error) => error != null).cast<String>().toList();
  }

  /// Update confirm password with current password
  RegistrationFormValidation updateConfirmPassword(String confirmPasswordValue) {
    return copyWith(
      confirmPassword: ConfirmPassword.dirty(password.value, confirmPasswordValue),
    );
  }
}