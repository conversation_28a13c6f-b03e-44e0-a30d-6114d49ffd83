import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/responsive_helper.dart';
import '../../core/enums/device_type.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/bloc/user_bloc.dart';
import 'widgets/welcome_card.dart';
import 'widgets/quick_actions_grid.dart';
import 'widgets/recent_activity_card.dart';
import 'widgets/progress_overview_card.dart';
import 'widgets/platform_stats_card.dart';

/// Main dashboard screen providing comprehensive overview
/// Features unified view of all platform activities
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    // Load user data and dashboard metrics
    context.read<UserBloc>().add(const LoadUser());
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: RefreshIndicator(
        onRefresh: () async => _loadDashboardData(),
        child: SingleChildScrollView(
          padding: ResponsiveHelper.responsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context, deviceType),
              const SizedBox(height: 24),
              _buildMainContent(context, deviceType),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, DeviceType deviceType) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Dashboard',
                style: AppTextStyles.headlineLarge.copyWith(
                  fontSize: ResponsiveHelper.responsiveFontSize(context, 28),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Welcome back to your Quester platform',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
          ),
        ),
        if (deviceType.isDesktop) ...[
          const SizedBox(width: 16),
          _buildQuickAccessButtons(context),
        ],
      ],
    );
  }

  Widget _buildQuickAccessButtons(BuildContext context) {
    return Row(
      children: [
        _buildQuickButton(
          context,
          'New Quest',
          Icons.add_task_rounded,
          AppColors.primary,
          () => _navigateToRoute(context, AppConstants.questsRoute),
        ),
        const SizedBox(width: 8),
        _buildQuickButton(
          context,
          'Browse Projects',
          Icons.work_outline_rounded,
          AppColors.secondary,
          () => _navigateToRoute(context, AppConstants.freelancingRoute),
        ),
        const SizedBox(width: 8),
        _buildQuickButton(
          context,
          'Find Courses',
          Icons.school_outlined,
          AppColors.accent,
          () => _navigateToRoute(context, AppConstants.learningRoute),
        ),
      ],
    );
  }

  Widget _buildQuickButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: AppColors.onPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, DeviceType deviceType) {
    if (deviceType.isMobile) {
      return _buildMobileLayout(context);
    } else if (deviceType.isTablet) {
      return _buildTabletLayout(context);
    } else {
      return _buildDesktopLayout(context);
    }
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        const WelcomeCard(),
        const SizedBox(height: 16),
        const QuickActionsGrid(crossAxisCount: 2),
        const SizedBox(height: 16),
        const ProgressOverviewCard(),
        const SizedBox(height: 16),
        const PlatformStatsCard(),
        const SizedBox(height: 16),
        const RecentActivityCard(),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(flex: 2, child: WelcomeCard()),
            const SizedBox(width: 16),
            const Expanded(child: PlatformStatsCard()),
          ],
        ),
        const SizedBox(height: 16),
        const QuickActionsGrid(crossAxisCount: 3),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(child: ProgressOverviewCard()),
            const SizedBox(width: 16),
            const Expanded(child: RecentActivityCard()),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(flex: 2, child: WelcomeCard()),
            const SizedBox(width: 16),
            const Expanded(child: PlatformStatsCard()),
          ],
        ),
        const SizedBox(height: 16),
        const QuickActionsGrid(crossAxisCount: 4),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(flex: 2, child: ProgressOverviewCard()),
            const SizedBox(width: 16),
            const Expanded(child: RecentActivityCard()),
          ],
        ),
      ],
    );
  }

  void _navigateToRoute(BuildContext context, String route) {
    context.go(route);
  }
}