import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/constants/app_constants.dart';

/// Feature showcase card displaying key features with navigation
class FeatureShowcaseCard extends StatelessWidget {
  const FeatureShowcaseCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Explore Quester Features',
              style: AppTextStyles.titleLarge.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Your complete productivity and learning platform',
              style: AppTextStyles.bodyMedium.copyWith(color: AppColors.grey600),
            ),
            const SizedBox(height: 20),
            _buildFeatureGrid(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureGrid(BuildContext context) {
    final features = [
      FeatureItem(
        title: 'Quest Management',
        description: 'Create and track your tasks',
        icon: Icons.flag_rounded,
        color: AppColors.primary,
        route: AppConstants.questsRoute,
        progress: 0.75,
      ),
      FeatureItem(
        title: 'Freelancing Hub',
        description: 'Find projects and clients',
        icon: Icons.work_rounded,
        color: AppColors.secondary,
        route: AppConstants.freelancingRoute,
        progress: 1.0, // Complete
      ),
      FeatureItem(
        title: 'Learning Center',
        description: 'Courses and certifications',
        icon: Icons.school_rounded,
        color: AppColors.accent,
        route: AppConstants.learningRoute,
        progress: 1.0, // Complete
      ),
      FeatureItem(
        title: 'Gamification',
        description: 'Achievements and rewards',
        icon: Icons.emoji_events_rounded,
        color: AppColors.warning,
        route: AppConstants.gamificationRoute,
        progress: 0.5,
      ),
      FeatureItem(
        title: 'Analytics',
        description: 'Track your performance',
        icon: Icons.analytics_rounded,
        color: AppColors.info,
        route: AppConstants.analyticsRoute,
        progress: 0.6,
      ),
      FeatureItem(
        title: 'Enterprise',
        description: 'Business solutions',
        icon: Icons.business_rounded,
        color: AppColors.success,
        route: AppConstants.enterpriseRoute,
        progress: 0.8,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.8,
      ),
      itemCount: features.length,
      itemBuilder: (context, index) => _buildFeatureCard(context, features[index]),
    );
  }

  Widget _buildFeatureCard(BuildContext context, FeatureItem feature) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => context.go(feature.route),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: feature.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(feature.icon, color: feature.color, size: 16),
                  ),
                  const Spacer(),
                  if (feature.progress == 1.0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Ready',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                feature.title,
                style: AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                feature.description,
                style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey600),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              if (feature.progress < 1.0) ...[
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: feature.progress,
                  backgroundColor: AppColors.grey200,
                  valueColor: AlwaysStoppedAnimation<Color>(feature.color),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class FeatureItem {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final String route;
  final double progress;

  FeatureItem({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.route,
    required this.progress,
  });
}