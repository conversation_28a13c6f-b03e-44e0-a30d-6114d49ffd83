import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/constants/app_constants.dart';

/// Getting started card with onboarding steps
class GettingStartedCard extends StatelessWidget {
  const GettingStartedCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.accent.withValues(alpha: 0.1), AppColors.secondary.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.accent.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.rocket_launch_rounded, color: AppColors.accent, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Getting Started',
                    style: AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Welcome to Quester! Follow these steps to get the most out of your experience:',
                style: AppTextStyles.bodyMedium.copyWith(color: AppColors.grey700),
              ),
              const SizedBox(height: 16),
              _buildStepsList(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepsList(BuildContext context) {
    final steps = [
      GetStartedStep(
        title: 'Complete your profile',
        description: 'Set up your profile information',
        icon: Icons.person_rounded,
        route: AppConstants.profileRoute,
        isCompleted: false,
      ),
      GetStartedStep(
        title: 'Explore Freelancing',
        description: 'Browse projects and build your portfolio',
        icon: Icons.work_rounded,
        route: AppConstants.freelancingRoute,
        isCompleted: true,
      ),
      GetStartedStep(
        title: 'Start Learning',
        description: 'Discover courses and earn certificates',
        icon: Icons.school_rounded,
        route: AppConstants.learningRoute,
        isCompleted: true,
      ),
      GetStartedStep(
        title: 'Create your first quest',
        description: 'Set goals and track your progress',
        icon: Icons.flag_rounded,
        route: AppConstants.questsRoute,
        isCompleted: false,
      ),
    ];

    return Column(
      children: steps
          .map((step) => _buildStepItem(context, step))
          .toList(),
    );
  }

  Widget _buildStepItem(BuildContext context, GetStartedStep step) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => context.go(step.route),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: step.isCompleted 
                    ? AppColors.success.withValues(alpha: 0.2) 
                    : AppColors.grey200,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  step.isCompleted ? Icons.check_rounded : step.icon,
                  color: step.isCompleted ? AppColors.success : AppColors.grey600,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      step.title,
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: step.isCompleted ? AppColors.grey700 : AppColors.grey900,
                        decoration: step.isCompleted ? TextDecoration.lineThrough : null,
                      ),
                    ),
                    Text(
                      step.description,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right_rounded,
                color: AppColors.grey400,
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class GetStartedStep {
  final String title;
  final String description;
  final IconData icon;
  final String route;
  final bool isCompleted;

  GetStartedStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.route,
    required this.isCompleted,
  });
}