import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/constants/app_constants.dart';

/// Quick actions grid for easy access to main features
class QuickActionsGrid extends StatelessWidget {
  final int crossAxisCount;
  
  const QuickActionsGrid({
    super.key,
    required this.crossAxisCount,
  });

  @override
  Widget build(BuildContext context) {
    final actions = [
      QuickAction('Quests', Icons.flag_rounded, AppConstants.questsRoute, AppColors.primary),
      QuickAction('Gaming', Icons.emoji_events_rounded, AppConstants.gamificationRoute, AppColors.warning),
      QuickAction('Freelancing', Icons.work_rounded, AppConstants.freelancingRoute, AppColors.secondary),
      QuickAction('Learning', Icons.school_rounded, AppConstants.learningRoute, AppColors.accent),
      QuickAction('Analytics', Icons.analytics_rounded, AppConstants.analyticsRoute, AppColors.info),
      QuickAction('Enterprise', Icons.business_rounded, AppConstants.enterpriseRoute, AppColors.success),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) => _buildActionCard(context, actions[index]),
    );
  }

  Widget _buildActionCard(BuildContext context, QuickAction action) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => context.go(action.route),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: action.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(action.icon, color: action.color, size: 24),
              ),
              const SizedBox(height: 8),
              Text(
                action.title,
                style: AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class QuickAction {
  final String title;
  final IconData icon;
  final String route;
  final Color color;

  QuickAction(this.title, this.icon, this.route, this.color);
}