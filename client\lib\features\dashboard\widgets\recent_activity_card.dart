import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Recent activity card showing latest platform activities
class RecentActivityCard extends StatelessWidget {
  const RecentActivityCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._buildActivities(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActivities() {
    final activities = [
      'Completed "Flutter Development" quest',
      'Earned 150 points from project submission',
      'Started "UI/UX Design" course',
      'Joined freelancing project discussion',
    ];

    return activities.map((activity) => Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: AppColors.success, size: 16),
          const SizedBox(width: 8),
          Expanded(child: Text(activity, style: AppTextStyles.bodySmall)),
        ],
      ),
    )).toList();
  }
}