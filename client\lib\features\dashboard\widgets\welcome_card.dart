import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../domain/bloc/user_bloc.dart';

/// Welcome card displaying personalized greeting and user info
class WelcomeCard extends StatelessWidget {
  const WelcomeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: ResponsiveHelper.responsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildAvatar(context, state),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildWelcomeText(context, state),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildUserStats(context, state),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAvatar(BuildContext context, UserState state) {
    return CircleAvatar(
      radius: ResponsiveHelper.responsiveIconSize(context, 24),
      backgroundColor: AppColors.onPrimary.withValues(alpha: 0.2),
      child: Icon(
        Icons.person_rounded,
        size: ResponsiveHelper.responsiveIconSize(context, 24),
        color: AppColors.onPrimary,
      ),
    );
  }

  Widget _buildWelcomeText(BuildContext context, UserState state) {
    final userName = state is UserLoadedState ? state.user.displayName ?? 'User' : 'User';
    final timeOfDay = _getTimeOfDay();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$timeOfDay, $userName!',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.onPrimary,
            fontSize: ResponsiveHelper.responsiveFontSize(context, 18),
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Ready to achieve your goals today?',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onPrimary.withValues(alpha: 0.9),
            fontSize: ResponsiveHelper.responsiveFontSize(context, 14),
          ),
        ),
      ],
    );
  }

  Widget _buildUserStats(BuildContext context, UserState state) {
    if (state is! UserLoadedState) {
      return _buildLoadingStats(context);
    }

    return Row(
      children: [
        _buildStatItem(
          context,
          'Level',
          state.user.currentLevel.toString(),
          Icons.trending_up_rounded,
        ),
        const SizedBox(width: 24),
        _buildStatItem(
          context,
          'Points',
          _formatPoints(state.user.totalPoints),
          Icons.star_rounded,
        ),
        const SizedBox(width: 24),
        _buildStatItem(
          context,
          'Streak',
          '${state.user.currentStreak} days',
          Icons.local_fire_department_rounded,
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: ResponsiveHelper.responsiveIconSize(context, 16),
          color: AppColors.onPrimary.withValues(alpha: 0.8),
        ),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: AppTextStyles.titleSmall.copyWith(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.bold,
                fontSize: ResponsiveHelper.responsiveFontSize(context, 14),
              ),
            ),
            Text(
              label,
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.onPrimary.withValues(alpha: 0.8),
                fontSize: ResponsiveHelper.responsiveFontSize(context, 10),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingStats(BuildContext context) {
    return Row(
      children: [
        _buildLoadingStat(context),
        const SizedBox(width: 24),
        _buildLoadingStat(context),
        const SizedBox(width: 24),
        _buildLoadingStat(context),
      ],
    );
  }

  Widget _buildLoadingStat(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: AppColors.onPrimary.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 30,
              height: 12,
              decoration: BoxDecoration(
                color: AppColors.onPrimary.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 2),
            Container(
              width: 20,
              height: 8,
              decoration: BoxDecoration(
                color: AppColors.onPrimary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  String _formatPoints(int points) {
    if (points >= 1000) {
      return '${(points / 1000).toStringAsFixed(1)}k';
    }
    return points.toString();
  }
}