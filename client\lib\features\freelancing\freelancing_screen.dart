import 'package:flutter/material.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/responsive_helper.dart';
import '../../core/enums/device_type.dart';
import 'widgets/project_discovery_tab.dart';
import 'widgets/my_projects_tab.dart';
import 'widgets/proposals_tab.dart';
import 'widgets/freelancer_profile_tab.dart';

/// Main freelancing marketplace screen with tabbed interface
/// Features comprehensive freelancing platform functionality
class FreelancingScreen extends StatefulWidget {
  const FreelancingScreen({super.key});

  @override
  State<FreelancingScreen> createState() => _FreelancingScreenState();
}

class _FreelancingScreenState extends State<FreelancingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  final List<FreelancingTab> _tabs = [
    FreelancingTab(
      title: 'Discover',
      icon: Icons.search_rounded,
      description: 'Find projects that match your skills',
    ),
    FreelancingTab(
      title: 'My Projects',
      icon: Icons.work_outline_rounded,
      description: 'Manage your active projects',
    ),
    FreelancingTab(
      title: 'Proposals',
      icon: Icons.description_outlined,
      description: 'Track your project proposals',
    ),
    FreelancingTab(
      title: 'Profile',
      icon: Icons.person_outline_rounded,
      description: 'Manage your freelancer profile',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) return;
    setState(() {
      _selectedIndex = _tabController.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          _buildHeader(context, deviceType),
          _buildTabBar(context, deviceType),
          Expanded(
            child: _buildTabViews(context),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, DeviceType deviceType) {
    return Container(
      padding: ResponsiveHelper.responsivePadding(context),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            offset: Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.work_rounded,
                  color: AppColors.onPrimary,
                  size: ResponsiveHelper.responsiveIconSize(context, 24),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Freelancing Marketplace',
                      style: AppTextStyles.headlineMedium.copyWith(
                        fontSize: ResponsiveHelper.responsiveFontSize(context, 20),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Connect with clients and grow your freelance business',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
              if (deviceType.isDesktop) ...[
                const SizedBox(width: 16),
                _buildQuickStats(context),
              ],
            ],
          ),
          if (!deviceType.isDesktop) ...[
            const SizedBox(height: 16),
            _buildQuickStats(context),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Row(
      children: [
        _buildStatCard(context, 'Active', '12', Icons.work_outline),
        const SizedBox(width: 12),
        _buildStatCard(context, 'Proposals', '8', Icons.description_outlined),
        const SizedBox(width: 12),
        _buildStatCard(context, 'Rating', '4.8', Icons.star_rounded),
      ],
    );
  }

  Widget _buildStatCard(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: AppColors.primary),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                value,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.grey600,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, DeviceType deviceType) {
    if (deviceType.isMobile) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = index == _selectedIndex;
              
              return Padding(
                padding: EdgeInsets.only(right: index < _tabs.length - 1 ? 12 : 0),
                child: _buildMobileTabItem(context, tab, index, isSelected),
              );
            }).toList(),
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: _tabs.map((tab) => _buildDesktopTab(context, tab)).toList(),
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.grey600,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTextStyles.titleSmall,
        padding: const EdgeInsets.symmetric(horizontal: 16),
      ),
    );
  }

  Widget _buildMobileTabItem(BuildContext context, FreelancingTab tab, int index, bool isSelected) {
    return GestureDetector(
      onTap: () => _tabController.animateTo(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : AppColors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              tab.icon,
              size: 16,
              color: isSelected ? AppColors.primary : AppColors.grey600,
            ),
            const SizedBox(width: 6),
            Text(
              tab.title,
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected ? AppColors.primary : AppColors.grey600,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopTab(BuildContext context, FreelancingTab tab) {
    return Tab(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(tab.icon, size: 18),
            const SizedBox(width: 8),
            Text(tab.title),
          ],
        ),
      ),
    );
  }

  Widget _buildTabViews(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: const [
        ProjectDiscoveryTab(),
        MyProjectsTab(),
        ProposalsTab(),
        FreelancerProfileTab(),
      ],
    );
  }
}

/// Model class for freelancing tabs
class FreelancingTab {
  final String title;
  final IconData icon;
  final String description;

  const FreelancingTab({
    required this.title,
    required this.icon,
    required this.description,
  });
}