import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';

/// My Projects tab for managing active freelancing projects
class MyProjectsTab extends StatelessWidget {
  const MyProjectsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: <PERSON><PERSON><PERSON>(
        children: [
          Icon(Icons.work_outline_rounded, size: 64, color: AppColors.grey400),
          <PERSON><PERSON><PERSON><PERSON>(height: 16),
          Text(
            'My Projects',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Track and manage your active freelancing projects',
            style: TextStyle(color: AppColors.grey600),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          Text(
            'Features coming soon:',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          Sized<PERSON><PERSON>(height: 8),
          Text('• Active project dashboard'),
          Text('• Time tracking integration'),
          Text('• Project milestone management'),
          Text('• Client communication hub'),
        ],
      ),
    );
  }
}