import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';

/// Project discovery tab for browsing available projects
class ProjectDiscoveryTab extends StatefulWidget {
  const ProjectDiscoveryTab({super.key});

  @override
  State<ProjectDiscoveryTab> createState() => _ProjectDiscoveryTabState();
}

class _ProjectDiscoveryTabState extends State<ProjectDiscoveryTab> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All';
  String _selectedBudget = 'All';

  final List<String> _categories = [
    'All',
    'Web Development',
    'Mobile Development',
    'UI/UX Design',
    'Data Science',
    'Digital Marketing',
    'Content Writing',
  ];

  final List<String> _budgetRanges = [
    'All',
    'Under \$500',
    '\$500 - \$1,500',
    '\$1,500 - \$5,000',
    'Above \$5,000',
  ];

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return Column(
      children: [
        _buildSearchAndFilters(context, deviceType),
        Expanded(
          child: _buildProjectsList(context),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters(BuildContext context, deviceType) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search projects...',
              prefixIcon: const Icon(Icons.search_rounded),
              suffixIcon: IconButton(
                icon: const Icon(Icons.filter_list_rounded),
                onPressed: () => _showFilterDialog(context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: AppColors.grey50,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Quick Filters
          if (deviceType.isDesktop) ...[
            Row(
              children: [
                Expanded(
                  child: _buildDropdownFilter(
                    'Category',
                    _selectedCategory,
                    _categories,
                    (value) => setState(() => _selectedCategory = value!),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDropdownFilter(
                    'Budget',
                    _selectedBudget,
                    _budgetRanges,
                    (value) => setState(() => _selectedBudget = value!),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.add_rounded, size: 18),
                  label: const Text('Post Project'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.onPrimary,
                  ),
                ),
              ],
            ),
          ] else ...[
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _categories.map((category) {
                  final isSelected = category == _selectedCategory;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() => _selectedCategory = category);
                      },
                      backgroundColor: AppColors.grey100,
                      selectedColor: AppColors.primary.withValues(alpha: 0.2),
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : AppColors.grey700,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDropdownFilter(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: AppColors.grey50,
      ),
      items: options.map((option) {
        return DropdownMenuItem(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildProjectsList(BuildContext context) {
    // Mock project data
    final projects = List.generate(10, (index) => _createMockProject(index));
    
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: projects.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        return _buildProjectCard(context, projects[index]);
      },
    );
  }

  Widget _buildProjectCard(BuildContext context, Map<String, dynamic> project) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    project['title'],
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getBudgetColor(project['budget']).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    project['budget'],
                    style: AppTextStyles.labelSmall.copyWith(
                      color: _getBudgetColor(project['budget']),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Text(
              project['description'],
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey700,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: (project['skills'] as List<String>).map((skill) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    skill,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.accent,
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Icon(Icons.schedule_rounded, size: 14, color: AppColors.grey500),
                const SizedBox(width: 4),
                Text(
                  'Posted ${project['timeAgo']}',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.grey500,
                  ),
                ),
                const Spacer(),
                Text(
                  '${project['proposals']} proposals',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.onPrimary,
                    minimumSize: const Size(80, 32),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getBudgetColor(String budget) {
    if (budget.contains('5,000')) return AppColors.success;
    if (budget.contains('1,500')) return AppColors.warning;
    return AppColors.info;
  }

  Map<String, dynamic> _createMockProject(int index) {
    final titles = [
      'E-commerce Website Development',
      'Mobile App UI/UX Design',
      'Data Analysis Dashboard',
      'Content Management System',
      'Social Media Marketing Campaign',
    ];
    
    final descriptions = [
      'Looking for an experienced developer to create a modern e-commerce platform with payment integration.',
      'Need a talented designer to create intuitive mobile app interfaces for iOS and Android.',
      'Seeking a data scientist to build interactive dashboards for business analytics.',
      'Require a developer to build a custom CMS with user management and content workflows.',
      'Looking for a marketing expert to develop and execute social media strategies.',
    ];
    
    final skills = [
      ['React', 'Node.js', 'MongoDB'],
      ['UI/UX', 'Figma', 'Prototyping'],
      ['Python', 'Tableau', 'SQL'],
      ['PHP', 'MySQL', 'Laravel'],
      ['Social Media', 'Analytics', 'Content'],
    ];
    
    final budgets = ['\$2,000 - \$5,000', '\$500 - \$1,500', '\$1,500 - \$3,000'];
    
    return {
      'title': titles[index % titles.length],
      'description': descriptions[index % descriptions.length],
      'skills': skills[index % skills.length],
      'budget': budgets[index % budgets.length],
      'timeAgo': '${index + 1} ${index == 0 ? 'hour' : 'hours'} ago',
      'proposals': (index * 3 + 2).toString(),
    };
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Projects'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Advanced filtering options will be available here.'),
            const SizedBox(height: 16),
            const Text('Coming soon: Location, Experience level, Project duration filters'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}