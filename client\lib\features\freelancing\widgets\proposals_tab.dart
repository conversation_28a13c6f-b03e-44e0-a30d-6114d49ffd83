import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

/// Proposals tab for managing project proposals
class ProposalsTab extends StatefulWidget {
  const ProposalsTab({super.key});

  @override
  State<ProposalsTab> createState() => _ProposalsTabState();
}

class _ProposalsTabState extends State<ProposalsTab> with TickerProviderStateMixin {
  late TabController _tabController;

  // Mock data for proposals
  final List<Map<String, dynamic>> _activeProposals = [
    {
      'id': '1',
      'title': 'E-commerce Mobile App Development',
      'client': 'TechCorp Inc.',
      'amount': 5000,
      'status': 'pending',
      'submittedAt': DateTime.now().subtract(const Duration(days: 2)),
      'description': 'Looking for an experienced Flutter developer to build a comprehensive e-commerce mobile application.',
    },
    {
      'id': '2',
      'title': 'Restaurant Management System',
      'client': 'FoodieHub',
      'amount': 3500,
      'status': 'under_review',
      'submittedAt': DateTime.now().subtract(const Duration(days: 5)),
      'description': 'Need a complete restaurant management system with POS integration.',
    },
  ];

  final List<Map<String, dynamic>> _pastProposals = [
    {
      'id': '3',
      'title': 'Social Media Dashboard',
      'client': 'Marketing Pro',
      'amount': 2800,
      'status': 'accepted',
      'submittedAt': DateTime.now().subtract(const Duration(days: 15)),
      'respondedAt': DateTime.now().subtract(const Duration(days: 10)),
      'description': 'Build a comprehensive social media analytics dashboard.',
    },
    {
      'id': '4',
      'title': 'Fitness Tracking App',
      'client': 'HealthTech',
      'amount': 4200,
      'status': 'rejected',
      'submittedAt': DateTime.now().subtract(const Duration(days: 20)),
      'respondedAt': DateTime.now().subtract(const Duration(days: 18)),
      'description': 'Develop a fitness tracking app with wearable integration.',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildActiveProposals(),
              _buildPastProposals(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.send_outlined,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'My Proposals',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Track your submitted proposals and responses',
                  style: TextStyle(
                    color: AppColors.grey600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildStatsCard(),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final totalProposals = _activeProposals.length + _pastProposals.length;
    final acceptedProposals = _pastProposals.where((p) => p['status'] == 'accepted').length;
    final responseRate = totalProposals > 0 ? (acceptedProposals / totalProposals * 100).round() : 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            '$responseRate%',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const Text(
            'Success Rate',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(6),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.grey600,
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.pending_actions, size: 16),
                const SizedBox(width: 8),
                Text('Active (${_activeProposals.length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.history, size: 16),
                const SizedBox(width: 8),
                Text('Past (${_pastProposals.length})'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveProposals() {
    if (_activeProposals.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            SizedBox(height: 16),
            Text(
              'No Active Proposals',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.grey600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Start applying to projects to see your proposals here',
              style: TextStyle(color: AppColors.grey500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activeProposals.length,
      itemBuilder: (context, index) {
        final proposal = _activeProposals[index];
        return _buildProposalCard(proposal, isActive: true);
      },
    );
  }

  Widget _buildPastProposals() {
    if (_pastProposals.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: AppColors.grey400,
            ),
            SizedBox(height: 16),
            Text(
              'No Past Proposals',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.grey600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Your completed proposals will appear here',
              style: TextStyle(color: AppColors.grey500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pastProposals.length,
      itemBuilder: (context, index) {
        final proposal = _pastProposals[index];
        return _buildProposalCard(proposal, isActive: false);
      },
    );
  }

  Widget _buildProposalCard(Map<String, dynamic> proposal, {required bool isActive}) {
    final status = proposal['status'] as String;
    final statusColor = _getStatusColor(status);
    final statusIcon = _getStatusIcon(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    proposal['title'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 12, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        _getStatusText(status),
                        style: TextStyle(
                          fontSize: 12,
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Client: ${proposal['client']}',
              style: const TextStyle(
                color: AppColors.grey600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              proposal['description'],
              style: const TextStyle(fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  size: 16,
                  color: AppColors.grey600,
                ),
                Text(
                  '\$${proposal['amount']}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
                const Spacer(),
                Text(
                  'Submitted ${_formatDate(proposal['submittedAt'])}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.grey500,
                  ),
                ),
              ],
            ),
            if (!isActive && proposal['respondedAt'] != null) ...[
              const SizedBox(height: 8),
              Text(
                'Responded ${_formatDate(proposal['respondedAt'])}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.grey500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'under_review':
        return Colors.blue;
      case 'accepted':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return AppColors.grey500;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.schedule;
      case 'under_review':
        return Icons.visibility;
      case 'accepted':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'under_review':
        return 'Under Review';
      case 'accepted':
        return 'Accepted';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}