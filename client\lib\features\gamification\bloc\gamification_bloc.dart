import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../../core/services/api_service.dart';
import 'gamification_event.dart';
import 'gamification_state.dart';

/// BLoC for managing gamification state and interactions
class GamificationBloc extends Bloc<GamificationEvent, GamificationState> {
  final ApiService _apiService;
  
  // Cache for performance
  UserPoints? _cachedUserPoints;
  Map<String, dynamic>? _cachedUserStats;
  List<Achievement> _cachedUserAchievements = [];
  List<Achievement> _cachedAllAchievements = [];
  List<LeaderboardEntry>? _cachedLeaderboard;
  List<Reward> _cachedRewards = [];
  List<Map<String, dynamic>> _cachedActivityFeed = [];

  GamificationBloc({required ApiService apiService})
      : _apiService = apiService,
        super(const GamificationInitial()) {
    
    // Register event handlers
    on<LoadUserGamificationData>(_onLoadUserGamificationData);
    on<AwardPointsEvent>(_onAwardPoints);
    on<LoadAchievements>(_onLoadAchievements);
    on<CheckAchievements>(_onCheckAchievements);
    on<LoadLeaderboard>(_onLoadLeaderboard);
    on<LoadRewards>(_onLoadRewards);
    on<PurchaseReward>(_onPurchaseReward);
    on<LoadActivityFeed>(_onLoadActivityFeed);
    on<RefreshGamificationData>(_onRefreshGamificationData);
    on<UpdateUserPointsLocally>(_onUpdateUserPointsLocally);
    on<AchievementUnlocked>(_onAchievementUnlocked);
  }

  /// Load complete user gamification data
  Future<void> _onLoadUserGamificationData(
    LoadUserGamificationData event,
    Emitter<GamificationState> emit,
  ) async {
    emit(const GamificationLoading());

    try {
      // Load all data in parallel for better performance
      final futures = await Future.wait([
        _apiService.getUserPoints(event.userId),
        _apiService.getUserStats(event.userId),
        _apiService.getUserAchievements(event.userId),
        _apiService.getAllAchievements(),
        _apiService.getLeaderboard(),
        _apiService.getAllRewards(),
        _apiService.getGlobalActivity(limit: 20),
      ]);

      _cachedUserPoints = futures[0] as UserPoints?;
      _cachedUserStats = futures[1] as Map<String, dynamic>?;
      _cachedUserAchievements = futures[2] as List<Achievement>;
      _cachedAllAchievements = futures[3] as List<Achievement>;
      _cachedLeaderboard = futures[4] as List<LeaderboardEntry>?;
      _cachedRewards = futures[5] as List<Reward>;
      _cachedActivityFeed = futures[6] as List<Map<String, dynamic>>;

      emit(GamificationLoaded(
        userPoints: _cachedUserPoints,
        userStats: _cachedUserStats,
        userAchievements: _cachedUserAchievements,
        allAchievements: _cachedAllAchievements,
        leaderboard: _cachedLeaderboard,
        rewards: _cachedRewards,
        activityFeed: _cachedActivityFeed,
      ));
    } catch (e) {
      emit(GamificationError('Failed to load gamification data: $e'));
    }
  }

  /// Award points to user with optimistic update
  Future<void> _onAwardPoints(
    AwardPointsEvent event,
    Emitter<GamificationState> emit,
  ) async {
    // Optimistic update
    if (_cachedUserPoints != null) {
      final optimisticPoints = _cachedUserPoints!.copyWith(
        totalPoints: _cachedUserPoints!.totalPoints + event.points,
        availablePoints: _cachedUserPoints!.availablePoints + event.points,
      );
      
      emit(_getCurrentLoadedState().copyWith(
        userPoints: optimisticPoints,
      ));
    }

    try {
      final success = await _apiService.awardPoints(
        event.userId,
        event.points,
        event.activityType,
        event.description,
      );

      if (success) {
        // Reload user points to get accurate data including level/role updates
        final updatedPoints = await _apiService.getUserPoints(event.userId);
        if (updatedPoints != null) {
          _cachedUserPoints = updatedPoints;
          
          emit(PointsAwarded(
            pointsAwarded: event.points,
            updatedUserPoints: updatedPoints,
          ));

          // Update the loaded state with new points
          emit(_getCurrentLoadedState().copyWith(
            userPoints: updatedPoints,
          ));

          // Check for new achievements
          add(CheckAchievements(event.userId, {
            'total_points': updatedPoints.totalPoints,
            'current_level': updatedPoints.currentLevel,
            'activity_type': event.activityType,
          }));
        }
      } else {
        // Revert optimistic update on failure
        emit(_getCurrentLoadedState().copyWith(
          userPoints: _cachedUserPoints,
        ));
        emit(const GamificationError('Failed to award points'));
      }
    } catch (e) {
      // Revert optimistic update on error
      emit(_getCurrentLoadedState().copyWith(
        userPoints: _cachedUserPoints,
      ));
      emit(GamificationError('Error awarding points: $e'));
    }
  }

  /// Load all achievements
  Future<void> _onLoadAchievements(
    LoadAchievements event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final achievements = await _apiService.getAllAchievements();
      _cachedAllAchievements = achievements;
      
      emit(_getCurrentLoadedState().copyWith(
        allAchievements: achievements,
      ));
    } catch (e) {
      emit(GamificationError('Failed to load achievements: $e'));
    }
  }

  /// Check for new achievements
  Future<void> _onCheckAchievements(
    CheckAchievements event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final newAchievements = await _apiService.checkAchievements(
        event.userId,
        event.progressData,
      );

      if (newAchievements.isNotEmpty) {
        // Update cached achievements
        _cachedUserAchievements.addAll(newAchievements);
        
        // Emit achievement unlocked for each new achievement
        for (final achievement in newAchievements) {
          emit(AchievementUnlockedState(
            achievement: achievement,
            allUnlockedAchievements: newAchievements,
          ));
        }

        // Update the loaded state
        emit(_getCurrentLoadedState().copyWith(
          userAchievements: List.from(_cachedUserAchievements),
          recentAchievements: newAchievements,
        ));
      }
    } catch (e) {
      developer.log('Error checking achievements: $e', name: 'GamificationBloc');
    }
  }

  /// Load leaderboard
  Future<void> _onLoadLeaderboard(
    LoadLeaderboard event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final leaderboard = await _apiService.getLeaderboard(
        type: event.type,
        limit: event.limit,
      );
      
      if (leaderboard != null) {
        _cachedLeaderboard = leaderboard;
        
        emit(LeaderboardUpdated(leaderboard));
        
        emit(_getCurrentLoadedState().copyWith(
          leaderboard: leaderboard,
        ));
      }
    } catch (e) {
      emit(GamificationError('Failed to load leaderboard: $e'));
    }
  }

  /// Load rewards
  Future<void> _onLoadRewards(
    LoadRewards event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final rewards = await _apiService.getAllRewards();
      _cachedRewards = rewards;
      
      emit(_getCurrentLoadedState().copyWith(
        rewards: rewards,
      ));
    } catch (e) {
      emit(GamificationError('Failed to load rewards: $e'));
    }
  }

  /// Purchase reward
  Future<void> _onPurchaseReward(
    PurchaseReward event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final success = await _apiService.purchaseReward(
        event.rewardId,
        event.userId,
        event.userPoints,
      );

      if (success) {
        // Find the purchased reward
        final reward = _cachedRewards.firstWhere(
          (r) => r.id == event.rewardId,
          orElse: () => throw Exception('Reward not found'),
        );

        // Update user points (subtract cost)
        if (_cachedUserPoints != null) {
          final updatedPoints = _cachedUserPoints!.copyWith(
            availablePoints: _cachedUserPoints!.availablePoints - reward.pointsCost,
          );
          _cachedUserPoints = updatedPoints;

          emit(RewardPurchased(
            reward: reward,
            updatedUserPoints: updatedPoints,
          ));

          emit(_getCurrentLoadedState().copyWith(
            userPoints: updatedPoints,
          ));
        }
      } else {
        emit(const GamificationError('Failed to purchase reward'));
      }
    } catch (e) {
      emit(GamificationError('Error purchasing reward: $e'));
    }
  }

  /// Load activity feed
  Future<void> _onLoadActivityFeed(
    LoadActivityFeed event,
    Emitter<GamificationState> emit,
  ) async {
    try {
      final activities = event.userId != null
          ? await _apiService.getUserActivity(event.userId!, limit: event.limit, offset: event.offset)
          : await _apiService.getGlobalActivity(limit: event.limit, offset: event.offset);

      _cachedActivityFeed = activities;
      
      emit(_getCurrentLoadedState().copyWith(
        activityFeed: activities,
      ));
    } catch (e) {
      emit(GamificationError('Failed to load activity feed: $e'));
    }
  }

  /// Refresh all gamification data
  Future<void> _onRefreshGamificationData(
    RefreshGamificationData event,
    Emitter<GamificationState> emit,
  ) async {
    emit(_getCurrentLoadedState().copyWith(isRefreshing: true));
    
    // Clear cache and reload
    _clearCache();
    add(LoadUserGamificationData(event.userId));
  }

  /// Update user points locally (optimistic update)
  Future<void> _onUpdateUserPointsLocally(
    UpdateUserPointsLocally event,
    Emitter<GamificationState> emit,
  ) async {
    if (_cachedUserPoints != null) {
      final updatedPoints = _cachedUserPoints!.copyWith(
        totalPoints: _cachedUserPoints!.totalPoints + event.pointsToAdd,
        availablePoints: _cachedUserPoints!.availablePoints + event.pointsToAdd,
      );
      _cachedUserPoints = updatedPoints;

      emit(_getCurrentLoadedState().copyWith(
        userPoints: updatedPoints,
      ));
    }
  }

  /// Handle achievement unlocked event
  Future<void> _onAchievementUnlocked(
    AchievementUnlocked event,
    Emitter<GamificationState> emit,
  ) async {
    if (!_cachedUserAchievements.any((a) => a.id == event.achievement.id)) {
      _cachedUserAchievements.add(event.achievement);
      
      emit(AchievementUnlockedState(
        achievement: event.achievement,
        allUnlockedAchievements: [event.achievement],
      ));

      emit(_getCurrentLoadedState().copyWith(
        userAchievements: List.from(_cachedUserAchievements),
        recentAchievements: [event.achievement],
      ));
    }
  }

  /// Helper to get current loaded state or create new one
  GamificationLoaded _getCurrentLoadedState() {
    if (state is GamificationLoaded) {
      return state as GamificationLoaded;
    }
    return GamificationLoaded(
      userPoints: _cachedUserPoints,
      userStats: _cachedUserStats,
      userAchievements: _cachedUserAchievements,
      allAchievements: _cachedAllAchievements,
      leaderboard: _cachedLeaderboard,
      rewards: _cachedRewards,
      activityFeed: _cachedActivityFeed,
    );
  }

  /// Clear all cached data
  void _clearCache() {
    _cachedUserPoints = null;
    _cachedUserStats = null;
    _cachedUserAchievements.clear();
    _cachedAllAchievements.clear();
    _cachedLeaderboard = null;
    _cachedRewards.clear();
    _cachedActivityFeed.clear();
  }

  @override
  Future<void> close() {
    _apiService.dispose();
    return super.close();
  }
}
