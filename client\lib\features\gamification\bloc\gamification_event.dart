import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Events for the Gamification BLoC
abstract class GamificationEvent extends Equatable {
  const GamificationEvent();

  @override
  List<Object?> get props => [];
}

/// Load user gamification data
class LoadUserGamificationData extends GamificationEvent {
  final String userId;

  const LoadUserGamificationData(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Award points to user
class AwardPointsEvent extends GamificationEvent {
  final String userId;
  final int points;
  final String activityType;
  final String description;

  const AwardPointsEvent({
    required this.userId,
    required this.points,
    required this.activityType,
    required this.description,
  });

  @override
  List<Object?> get props => [userId, points, activityType, description];
}

/// Load achievements
class LoadAchievements extends GamificationEvent {
  const LoadAchievements();
}

/// Check for new achievements
class CheckAchievements extends GamificationEvent {
  final String userId;
  final Map<String, dynamic> progressData;

  const CheckAchievements(this.userId, this.progressData);

  @override
  List<Object?> get props => [userId, progressData];
}

/// Load leaderboard
class LoadLeaderboard extends GamificationEvent {
  final String type;
  final int limit;

  const LoadLeaderboard({
    this.type = 'global_points',
    this.limit = 50,
  });

  @override
  List<Object?> get props => [type, limit];
}

/// Load rewards
class LoadRewards extends GamificationEvent {
  const LoadRewards();
}

/// Purchase reward
class PurchaseReward extends GamificationEvent {
  final String rewardId;
  final String userId;
  final int userPoints;

  const PurchaseReward({
    required this.rewardId,
    required this.userId,
    required this.userPoints,
  });

  @override
  List<Object?> get props => [rewardId, userId, userPoints];
}

/// Load activity feed
class LoadActivityFeed extends GamificationEvent {
  final String? userId; // null for global feed
  final int limit;
  final int offset;

  const LoadActivityFeed({
    this.userId,
    this.limit = 50,
    this.offset = 0,
  });

  @override
  List<Object?> get props => [userId, limit, offset];
}

/// Refresh all gamification data
class RefreshGamificationData extends GamificationEvent {
  final String userId;

  const RefreshGamificationData(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Update user points locally (optimistic update)
class UpdateUserPointsLocally extends GamificationEvent {
  final int pointsToAdd;

  const UpdateUserPointsLocally(this.pointsToAdd);

  @override
  List<Object?> get props => [pointsToAdd];
}

/// Achievement unlocked event
class AchievementUnlocked extends GamificationEvent {
  final Achievement achievement;

  const AchievementUnlocked(this.achievement);

  @override
  List<Object?> get props => [achievement];
}
