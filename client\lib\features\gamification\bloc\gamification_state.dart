import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// States for the Gamification BLoC
abstract class GamificationState extends Equatable {
  const GamificationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class Gamification<PERSON><PERSON>tial extends GamificationState {
  const GamificationInitial();
}

/// Loading state
class GamificationLoading extends GamificationState {
  const GamificationLoading();
}

/// Loaded state with complete gamification data
class GamificationLoaded extends GamificationState {
  final UserPoints? userPoints;
  final Map<String, dynamic>? userStats;
  final List<Achievement> userAchievements;
  final List<Achievement> allAchievements;
  final List<LeaderboardEntry>? leaderboard;
  final List<Reward> rewards;
  final List<Map<String, dynamic>> activityFeed;
  final List<Achievement> recentAchievements;
  final bool isRefreshing;

  const GamificationLoaded({
    this.userPoints,
    this.userStats,
    this.userAchievements = const [],
    this.allAchievements = const [],
    this.leaderboard,
    this.rewards = const [],
    this.activityFeed = const [],
    this.recentAchievements = const [],
    this.isRefreshing = false,
  });

  GamificationLoaded copyWith({
    UserPoints? userPoints,
    Map<String, dynamic>? userStats,
    List<Achievement>? userAchievements,
    List<Achievement>? allAchievements,
    List<LeaderboardEntry>? leaderboard,
    List<Reward>? rewards,
    List<Map<String, dynamic>>? activityFeed,
    List<Achievement>? recentAchievements,
    bool? isRefreshing,
  }) {
    return GamificationLoaded(
      userPoints: userPoints ?? this.userPoints,
      userStats: userStats ?? this.userStats,
      userAchievements: userAchievements ?? this.userAchievements,
      allAchievements: allAchievements ?? this.allAchievements,
      leaderboard: leaderboard ?? this.leaderboard,
      rewards: rewards ?? this.rewards,
      activityFeed: activityFeed ?? this.activityFeed,
      recentAchievements: recentAchievements ?? this.recentAchievements,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        userPoints,
        userStats,
        userAchievements,
        allAchievements,
        leaderboard,
        rewards,
        activityFeed,
        recentAchievements,
        isRefreshing,
      ];
}

/// Error state
class GamificationError extends GamificationState {
  final String message;

  const GamificationError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Points awarded state
class PointsAwarded extends GamificationState {
  final int pointsAwarded;
  final UserPoints updatedUserPoints;

  const PointsAwarded({
    required this.pointsAwarded,
    required this.updatedUserPoints,
  });

  @override
  List<Object?> get props => [pointsAwarded, updatedUserPoints];
}

/// Achievement unlocked state
class AchievementUnlockedState extends GamificationState {
  final Achievement achievement;
  final List<Achievement> allUnlockedAchievements;

  const AchievementUnlockedState({
    required this.achievement,
    required this.allUnlockedAchievements,
  });

  @override
  List<Object?> get props => [achievement, allUnlockedAchievements];
}

/// Reward purchased state
class RewardPurchased extends GamificationState {
  final Reward reward;
  final UserPoints updatedUserPoints;

  const RewardPurchased({
    required this.reward,
    required this.updatedUserPoints,
  });

  @override
  List<Object?> get props => [reward, updatedUserPoints];
}

/// Leaderboard updated state
class LeaderboardUpdated extends GamificationState {
  final List<LeaderboardEntry> leaderboard;

  const LeaderboardUpdated(this.leaderboard);

  @override
  List<Object?> get props => [leaderboard];
}
