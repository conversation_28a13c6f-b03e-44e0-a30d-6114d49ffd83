import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';

import 'bloc/gamification_bloc.dart';
import 'bloc/gamification_event.dart';
import 'bloc/gamification_state.dart';
import 'widgets/points_display.dart';
import 'widgets/achievement_badge.dart';
import 'widgets/leaderboard_widget.dart';
import 'widgets/progress_bar.dart';
import 'widgets/reward_shop.dart';

/// Main gamification screen with tabs for different features
class GamificationScreen extends StatefulWidget {
  const GamificationScreen({super.key});

  @override
  State<GamificationScreen> createState() => _GamificationScreenState();
}

class _GamificationScreenState extends State<GamificationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load initial data with a more realistic user ID
    const currentUserId = 'user_001'; // In production, get from authentication service
    context.read<GamificationBloc>().add(LoadUserGamificationData(currentUserId));
    context.read<GamificationBloc>().add(LoadAchievements());
    context.read<GamificationBloc>().add(LoadLeaderboard());
    context.read<GamificationBloc>().add(LoadRewards());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gamification'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.emoji_events), text: 'Achievements'),
            Tab(icon: Icon(Icons.leaderboard), text: 'Leaderboard'),
            Tab(icon: Icon(Icons.redeem), text: 'Rewards'),
          ],
        ),
      ),
      body: BlocListener<GamificationBloc, GamificationState>(
        listener: (context, state) {
          if (state is GamificationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: theme.colorScheme.error,
              ),
            );
          }
        },
        child: TabBarView(
          controller: _tabController,
          children: const [
            _DashboardTab(),
            _AchievementsTab(),
            _LeaderboardTab(),
            _RewardsTab(),
          ],
        ),
      ),
    );
  }
}

/// Dashboard tab with overview of all gamification features
class _DashboardTab extends StatelessWidget {
  const _DashboardTab();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Points and Stats Section
              if (state is GamificationLoaded && 
                  state.userPoints != null && 
                  state.userStats != null)
                PointsDisplay(
                  userPoints: state.userPoints!,
                  userStats: state.userStats!,
                ),

              const SizedBox(height: 16),

              // Recent Achievements
              if (state is GamificationLoaded && 
                  state.allAchievements.isNotEmpty)
                _RecentAchievementsSection(
                  achievements: state.allAchievements.take(3).toList(),
                ),

              const SizedBox(height: 16),

              // Progress Section
              _ProgressSection(),

              const SizedBox(height: 16),

              // Quick Stats
              if (state is GamificationLoaded)
                _QuickStatsSection(state: state),

              const SizedBox(height: 16),

              // Featured Rewards
              if (state is GamificationLoaded && 
                  state.rewards.isNotEmpty)
                CompactRewardShowcase(
                  featuredRewards: state.rewards
                      .where((r) => r.rarity == RewardRarity.epic || 
                                   r.rarity == RewardRarity.legendary)
                      .take(3)
                      .toList(),
                  currentPoints: state.userPoints?.totalPoints ?? 0,
                  onPurchaseReward: (rewardId) {
                    context.read<GamificationBloc>().add(
                      PurchaseReward(
                        userId: 'user_001', // In production, get from authentication service
                        rewardId: rewardId,
                        userPoints: state.userPoints?.totalPoints ?? 0,
                      ),
                    );
                  },
                  onViewAll: () {
                    // Switch to rewards tab
                    DefaultTabController.of(context).animateTo(3);
                  },
                ),
            ],
          ),
        );
      },
    );
  }
}

/// Recent achievements section
class _RecentAchievementsSection extends StatelessWidget {
  final List<Achievement> achievements;

  const _RecentAchievementsSection({
    required this.achievements,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.new_releases,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Achievements',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Switch to achievements tab
                    DefaultTabController.of(context).animateTo(1);
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (achievements.isEmpty)
              Center(
                child: Text(
                  'No achievements yet. Complete quests to unlock!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              )
            else
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: achievements.map((achievement) => 
                  AchievementBadge(
                    achievement: achievement,
                    unlocked: true,
                    size: 60,
                    onTap: () {
                      // Show achievement details
                      _showAchievementDetails(context, achievement);
                    },
                  ),
                ).toList(),
              ),
          ],
        ),
      ),
    );
  }

  void _showAchievementDetails(BuildContext context, Achievement achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AchievementBadge(
              achievement: achievement,
              unlocked: true,
              size: 80,
            ),
            const SizedBox(height: 16),
            Text(
              achievement.name,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              achievement.description,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.amber),
                const SizedBox(width: 4),
                Text(
                  '${achievement.pointsAwarded} points',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Progress section showing various progress bars
class _ProgressSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Progress',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Level progress
            GamificationProgressBar(
              progress: 0.65,
              label: 'Level Progress',
              subLabel: 'Level 7 → Level 8',
              showPercentage: true,
              showGlow: true,
              leadingIcon: Icon(
                Icons.auto_awesome,
                color: Colors.amber,
                size: 20,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Weekly quest progress
            GamificationProgressBar(
              progress: 0.4,
              label: 'Weekly Quests',
              subLabel: '4 of 10 completed',
              progressColor: Colors.green,
            ),
            
            const SizedBox(height: 16),
            
            // Achievement progress
            GamificationProgressBar(
              progress: 0.8,
              label: 'Achievement Progress',
              subLabel: '12 of 15 unlocked',
              progressColor: Colors.purple,
            ),
          ],
        ),
      ),
    );
  }
}

/// Quick stats section
class _QuickStatsSection extends StatelessWidget {
  final GamificationLoaded state;

  const _QuickStatsSection({
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Stats',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _StatItem(
                    icon: Icons.emoji_events,
                    label: 'Achievements',
                    value: '${state.allAchievements.length}',
                    color: Colors.amber,
                  ),
                ),
                Expanded(
                  child: _StatItem(
                    icon: Icons.local_fire_department,
                    label: 'Streak',
                    value: '${state.userStats?['currentStreak'] ?? 0}',
                    color: Colors.orange,
                  ),
                ),
                Expanded(
                  child: _StatItem(
                    icon: Icons.leaderboard,
                    label: 'Rank',
                    value: '#${state.leaderboard?.isNotEmpty == true ? 1 : '-'}',
                    color: Colors.blue,
                  ),
                ),
                Expanded(
                  child: _StatItem(
                    icon: Icons.redeem,
                    label: 'Rewards',
                    value: '${state.rewards.length}',
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Individual stat item widget
class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// Achievements tab
class _AchievementsTab extends StatelessWidget {
  const _AchievementsTab();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GamificationLoaded) {
          return GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.0,
            ),
            itemCount: state.allAchievements.length,
            itemBuilder: (context, index) {
              final achievement = state.allAchievements[index];
              final isUnlocked = _isAchievementUnlocked(achievement, state);
              return AchievementBadge(
                achievement: achievement,
                unlocked: isUnlocked,
                onTap: () {
                  _showAchievementDetails(context, achievement, isUnlocked);
                },
              );
            },
          );
        }

        return const Center(
          child: Text('No achievements available'),
        );
      },
    );
  }

  /// Check if an achievement is unlocked for the current user
  bool _isAchievementUnlocked(Achievement achievement, GamificationLoaded state) {
    // Check if the achievement is in the user's unlocked achievements list
    if (state.userAchievements.any((userAchievement) => userAchievement.id == achievement.id)) {
      return true;
    }

    // Check if the achievement can be unlocked based on current progress
    if (state.userPoints != null && achievement.isActive) {
      final currentValue = _getCurrentValueForAchievement(achievement, state);
      return currentValue >= achievement.progressRequired;
    }

    return false;
  }

  /// Get current value for achievement based on its type
  int _getCurrentValueForAchievement(Achievement achievement, GamificationLoaded state) {
    // Use the achievement type to determine what value to check
    switch (achievement.type) {
      case AchievementType.progress:
        // For progress achievements, check quest/task completion
        return state.userStats?['quests_completed'] ?? 0;
      case AchievementType.consistency:
        // For consistency achievements, check current streak
        return state.userStats?['current_streak'] ?? 0;
      case AchievementType.skill:
        // For skill achievements, check total points as a proxy for skill level
        return state.userPoints?.totalPoints ?? 0;
      case AchievementType.collaboration:
        // Calculate collaboration progress based on team activities
        // This would typically come from a collaboration service
        final collaborationData = _getCollaborationData();
        return collaborationData['teamActivities'] ?? 0;
      case AchievementType.special:
        // Special achievements are manually awarded
        return 0;
    }
  }

  /// Get collaboration data for tracking team activities
  Map<String, dynamic> _getCollaborationData() {
    // Mock collaboration data - in a real app, this would come from a service
    return {
      'teamActivities': 15, // Number of team activities participated in
      'sharedQuests': 8,    // Number of quests shared with team members
      'helpedColleagues': 5, // Number of times helped colleagues
      'mentoringSessions': 3, // Number of mentoring sessions conducted
      'collaborativeProjects': 2, // Number of collaborative projects
    };
  }

  /// Show achievement details dialog
  void _showAchievementDetails(BuildContext context, Achievement achievement, bool isUnlocked) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isUnlocked ? Icons.emoji_events : Icons.lock_outline,
              color: isUnlocked ? Colors.amber : Colors.grey,
              size: 32,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                achievement.name,
                style: TextStyle(
                  color: isUnlocked ? null : Colors.grey,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              achievement.description,
              style: TextStyle(
                color: isUnlocked ? null : Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.stars,
                  color: Colors.amber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${achievement.pointsAwarded} points',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getRarityColor(achievement.rarity).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                achievement.rarity.name.toUpperCase(),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: _getRarityColor(achievement.rarity),
                ),
              ),
            ),
            if (!isUnlocked) ...[
              const SizedBox(height: 16),
              Text(
                'Progress: ${_getAchievementProgress(achievement, context)}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Get color for achievement rarity
  Color _getRarityColor(AchievementRarity rarity) {
    switch (rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.uncommon:
        return Colors.green;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.orange;
    }
  }

  /// Get achievement progress text
  String _getAchievementProgress(Achievement achievement, BuildContext context) {
    final state = context.read<GamificationBloc>().state;
    if (state is! GamificationLoaded) return 'Unknown';

    final currentValue = _getCurrentValueForAchievement(achievement, state);
    final requiredValue = achievement.progressRequired;

    return '$currentValue / $requiredValue';
  }
}

/// Leaderboard tab
class _LeaderboardTab extends StatelessWidget {
  const _LeaderboardTab();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GamificationLoaded) {
          return LeaderboardWidget(
            entries: state.leaderboard ?? [],
            currentUserId: 'user_001', // In production, get from authentication service
            onRefresh: () {
              context.read<GamificationBloc>().add(LoadLeaderboard());
            },
            onUserTap: (userId) {
              // Navigate to user profile or show details
            },
          );
        }

        return const Center(
          child: Text('Leaderboard not available'),
        );
      },
    );
  }
}

/// Rewards tab
class _RewardsTab extends StatelessWidget {
  const _RewardsTab();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        if (state is GamificationLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GamificationLoaded) {
          return RewardShop(
            rewards: state.rewards,
            currentPoints: state.userPoints?.totalPoints ?? 0,
            onRefresh: () {
              context.read<GamificationBloc>().add(LoadRewards());
            },
            onPurchaseReward: (rewardId) {
              context.read<GamificationBloc>().add(
                PurchaseReward(
                  userId: 'user_001', // In production, get from authentication service
                  rewardId: rewardId,
                  userPoints: state.userPoints?.totalPoints ?? 0,
                ),
              );
            },
            onRewardTap: (reward) {
              // Show reward details
            },
          );
        }

        return const Center(
          child: Text('Rewards not available'),
        );
      },
    );
  }

}
