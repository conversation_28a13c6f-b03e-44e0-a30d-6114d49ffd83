import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget that displays an achievement badge with unlock animations
class AchievementBadge extends StatefulWidget {
  final Achievement achievement;
  final bool unlocked;
  final bool showUnlockAnimation;
  final VoidCallback? onTap;
  final double size;
  final bool showProgress;
  final double progress; // 0.0 to 1.0

  const AchievementBadge({
    super.key,
    required this.achievement,
    this.unlocked = false,
    this.showUnlockAnimation = false,
    this.onTap,
    this.size = 80.0,
    this.showProgress = false,
    this.progress = 0.0,
  });

  @override
  State<AchievementBadge> createState() => _AchievementBadgeState();
}

class _AchievementBadgeState extends State<AchievementBadge>
    with TickerProviderStateMixin {
  late AnimationController _unlockController;
  late AnimationController _pulseController;
  late AnimationController _shineController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shineAnimation;

  @override
  void initState() {
    super.initState();
    
    _unlockController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _shineController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _unlockController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _unlockController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _shineAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shineController,
      curve: Curves.easeInOut,
    ));

    if (widget.showUnlockAnimation) {
      _playUnlockAnimation();
    }

    if (widget.unlocked) {
      _startIdleAnimations();
    }
  }

  @override
  void didUpdateWidget(AchievementBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (!oldWidget.unlocked && widget.unlocked) {
      _playUnlockAnimation();
    }
    
    if (widget.unlocked && !oldWidget.unlocked) {
      _startIdleAnimations();
    }
  }

  void _playUnlockAnimation() {
    _unlockController.forward();
    _shineController.repeat(period: const Duration(seconds: 3));
  }

  void _startIdleAnimations() {
    if (widget.achievement.rarity == AchievementRarity.legendary || widget.achievement.rarity == AchievementRarity.epic) {
      _pulseController.repeat(reverse: true, period: const Duration(seconds: 2));
    }
  }

  @override
  void dispose() {
    _unlockController.dispose();
    _pulseController.dispose();
    _shineController.dispose();
    super.dispose();
  }

  Color _getRarityColor() {
    switch (widget.achievement.rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.uncommon:
        return Colors.green;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.amber;
    }
  }

  IconData _getCategoryIcon() {
    switch (widget.achievement.type) {
      case AchievementType.progress:
        return Icons.task_alt;
      case AchievementType.skill:
        return Icons.star;
      case AchievementType.consistency:
        return Icons.trending_up;
      case AchievementType.collaboration:
        return Icons.group;
      case AchievementType.special:
        return Icons.celebration;
    }
  }

  List<BoxShadow> _getShadows() {
    final rarityColor = _getRarityColor();
    
    if (!widget.unlocked) {
      return [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ];
    }

    switch (widget.achievement.rarity) {
      case AchievementRarity.legendary:
        return [
          BoxShadow(
            color: rarityColor.withValues(alpha: 0.3),
            blurRadius: 15,
            spreadRadius: 2,
          ),
          BoxShadow(
            color: rarityColor.withValues(alpha: 0.1),
            blurRadius: 30,
            spreadRadius: 5,
          ),
        ];
      case AchievementRarity.epic:
        return [
          BoxShadow(
            color: rarityColor.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ];
      case AchievementRarity.rare:
        return [
          BoxShadow(
            color: rarityColor.withValues(alpha: 0.2),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ];
      default:
        return [
          BoxShadow(
            color: rarityColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];
    }
  }

  Widget _buildShineEffect() {
    if (!widget.unlocked || widget.achievement.rarity == AchievementRarity.common) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _shineAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: ClipOval(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0 + _shineAnimation.value * 2, -1.0),
                  end: Alignment(1.0 + _shineAnimation.value * 2, 1.0),
                  colors: [
                    Colors.transparent,
                    Colors.white.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressRing() {
    if (!widget.showProgress) return const SizedBox.shrink();

    return Positioned.fill(
      child: CircularProgressIndicator(
        value: widget.progress,
        strokeWidth: 3,
        backgroundColor: Colors.grey.withValues(alpha: 0.3),
        valueColor: AlwaysStoppedAnimation(_getRarityColor()),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final rarityColor = _getRarityColor();
    final categoryIcon = _getCategoryIcon();

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _unlockController,
          _pulseController,
        ]),
        builder: (context, child) {
          double scale = 1.0;
          double opacity = 1.0;

          if (widget.showUnlockAnimation) {
            scale = _scaleAnimation.value;
            opacity = _opacityAnimation.value;
          }

          if (widget.unlocked && _pulseController.isAnimating) {
            scale *= _pulseAnimation.value;
          }

          return Transform.scale(
            scale: scale,
            child: Opacity(
              opacity: opacity,
              child: Container(
                width: widget.size,
                height: widget.size,
                child: Stack(
                  children: [
                    // Main badge container
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.unlocked 
                            ? rarityColor.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.1),
                        border: Border.all(
                          color: widget.unlocked 
                              ? rarityColor
                              : Colors.grey,
                          width: 3,
                        ),
                        boxShadow: _getShadows(),
                      ),
                      child: Center(
                        child: Icon(
                          categoryIcon,
                          size: widget.size * 0.4,
                          color: widget.unlocked 
                              ? rarityColor
                              : Colors.grey,
                        ),
                      ),
                    ),

                    // Progress ring
                    _buildProgressRing(),

                    // Shine effect
                    _buildShineEffect(),

                    // Lock overlay for locked achievements
                    if (!widget.unlocked)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black.withValues(alpha: 0.3),
                          ),
                          child: Icon(
                            Icons.lock,
                            size: widget.size * 0.3,
                            color: Colors.white,
                          ),
                        ),
                      ),

                    // Rarity indicator
                    if (widget.unlocked && 
                        (widget.achievement.rarity == AchievementRarity.epic || 
                         widget.achievement.rarity == AchievementRarity.legendary))
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: widget.size * 0.25,
                          height: widget.size * 0.25,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: rarityColor,
                            border: Border.all(
                              color: Colors.white,
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            widget.achievement.rarity == AchievementRarity.legendary
                                ? Icons.auto_awesome
                                : Icons.star,
                            size: widget.size * 0.12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Achievement unlock celebration widget
class AchievementUnlockCelebration extends StatefulWidget {
  final Achievement achievement;
  final VoidCallback? onAnimationComplete;

  const AchievementUnlockCelebration({
    super.key,
    required this.achievement,
    this.onAnimationComplete,
  });

  @override
  State<AchievementUnlockCelebration> createState() => _AchievementUnlockCelebrationState();
}

class _AchievementUnlockCelebrationState extends State<AchievementUnlockCelebration>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -100.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.3, curve: Curves.elasticOut),
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    _controller.forward().then((_) {
      widget.onAnimationComplete?.call();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Row(
                children: [
                  AchievementBadge(
                    achievement: widget.achievement,
                    unlocked: true,
                    showUnlockAnimation: true,
                    size: 60,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Achievement Unlocked!',
                          style: theme.textTheme.labelMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.achievement.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.achievement.description,
                          style: theme.textTheme.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '+${widget.achievement.pointsAwarded} points',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
