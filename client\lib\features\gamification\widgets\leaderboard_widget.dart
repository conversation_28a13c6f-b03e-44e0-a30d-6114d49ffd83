import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget that displays the leaderboard with ranking and user stats
class LeaderboardWidget extends StatelessWidget {
  final List<LeaderboardEntry> entries;
  final String? currentUserId;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final Function(String userId)? onUserTap;
  final int maxEntries;
  final bool showCurrentUserHighlight;

  const LeaderboardWidget({
    super.key,
    required this.entries,
    this.currentUserId,
    this.isLoading = false,
    this.onRefresh,
    this.onUserTap,
    this.maxEntries = 10,
    this.showCurrentUserHighlight = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayEntries = entries.take(maxEntries).toList();
    final currentUserEntry = currentUserId != null 
        ? entries.where((entry) => entry.userId == currentUserId).firstOrNull
        : null;
    final currentUserRank = currentUserEntry?.rank;

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.leaderboard,
                  color: theme.colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Leaderboard',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onRefresh != null)
                  IconButton(
                    onPressed: isLoading ? null : onRefresh,
                    icon: AnimatedRotation(
                      turns: isLoading ? 1 : 0,
                      duration: const Duration(seconds: 1),
                      child: const Icon(Icons.refresh),
                    ),
                  ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Loading state
          if (isLoading && entries.isEmpty)
            const Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Empty state
          if (!isLoading && entries.isEmpty)
            Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.emoji_events_outlined,
                      size: 64,
                      color: theme.colorScheme.outline,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No rankings available',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Complete quests to appear on the leaderboard!',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Leaderboard entries
          if (displayEntries.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: displayEntries.length,
              itemBuilder: (context, index) {
                final entry = displayEntries[index];
                final isCurrentUser = entry.userId == currentUserId;
                
                return _LeaderboardEntryTile(
                  entry: entry,
                  isCurrentUser: isCurrentUser && showCurrentUserHighlight,
                  onTap: onUserTap != null ? () => onUserTap!(entry.userId) : null,
                );
              },
            ),

          // Current user position (if not in top entries)
          if (currentUserEntry != null && 
              currentUserRank != null && 
              currentUserRank > maxEntries &&
              showCurrentUserHighlight)
            Column(
              children: [
                const Divider(height: 1),
                Container(
                  color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.more_horiz,
                          color: theme.colorScheme.outline,
                        ),
                        const Spacer(),
                        Text(
                          'Your position',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.more_horiz,
                          color: theme.colorScheme.outline,
                        ),
                      ],
                    ),
                  ),
                ),
                _LeaderboardEntryTile(
                  entry: currentUserEntry,
                  isCurrentUser: true,
                  onTap: onUserTap != null ? () => onUserTap!(currentUserEntry.userId) : null,
                ),
              ],
            ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

/// Individual leaderboard entry tile
class _LeaderboardEntryTile extends StatelessWidget {
  final LeaderboardEntry entry;
  final bool isCurrentUser;
  final VoidCallback? onTap;

  const _LeaderboardEntryTile({
    required this.entry,
    required this.isCurrentUser,
    this.onTap,
  });

  Color _getRankColor(int rank, ColorScheme colorScheme) {
    switch (rank) {
      case 1:
        return Colors.amber; // Gold
      case 2:
        return Colors.grey[400]!; // Silver
      case 3:
        return Colors.brown[300]!; // Bronze
      default:
        return colorScheme.outline;
    }
  }

  Widget _getRankIcon(int rank) {
    switch (rank) {
      case 1:
        return const Icon(Icons.workspace_premium, color: Colors.amber, size: 32);
      case 2:
        return const Icon(Icons.workspace_premium, color: Colors.grey, size: 28);
      case 3:
        return const Icon(Icons.workspace_premium, color: Colors.brown, size: 24);
      default:
        return Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[300],
          ),
          child: Center(
            child: Text(
              rank.toString(),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        );
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role.toLowerCase()) {
      case 'novice':
        return 'Novice';
      case 'apprentice':
        return 'Apprentice';
      case 'journeyman':
        return 'Journeyman';
      case 'expert':
        return 'Expert';
      case 'master':
        return 'Master';
      case 'grandmaster':
        return 'Grand Master';
      default:
        return role;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rankColor = _getRankColor(entry.rank, theme.colorScheme);

    return Material(
      color: isCurrentUser 
          ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
          : Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Rank indicator
              SizedBox(
                width: 40,
                child: _getRankIcon(entry.rank),
              ),

              const SizedBox(width: 16),

              // User avatar
              CircleAvatar(
                radius: 20,
                backgroundColor: theme.colorScheme.primaryContainer,
                child: Text(
                  entry.displayName.isNotEmpty 
                      ? entry.displayName[0].toUpperCase()
                      : '?',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            entry.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: isCurrentUser ? FontWeight.bold : FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isCurrentUser)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'YOU',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.shield,
                          size: 16,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getRoleDisplayName(entry.role),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.local_fire_department,
                          size: 16,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${entry.stats?['currentStreak'] ?? 0} days',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Points
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${entry.score.toInt()}',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: rankColor,
                    ),
                  ),
                  Text(
                    'points',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Compact leaderboard widget for smaller spaces
class CompactLeaderboard extends StatelessWidget {
  final List<LeaderboardEntry> topEntries;
  final VoidCallback? onViewAll;

  const CompactLeaderboard({
    super.key,
    required this.topEntries,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final top3 = topEntries.take(3).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Top Players',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (top3.isEmpty)
              Center(
                child: Text(
                  'No rankings yet',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              )
            else
              ...top3.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: entry.rank == 1 
                            ? Colors.amber
                            : entry.rank == 2 
                                ? Colors.grey[400]
                                : Colors.brown[300],
                      ),
                      child: Center(
                        child: Text(
                          entry.rank.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.displayName,
                        style: theme.textTheme.bodyMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      '${entry.score.toInt()}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }
}
