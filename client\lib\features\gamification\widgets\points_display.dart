import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget that displays user points with animated counters and role progression
class PointsDisplay extends StatefulWidget {
  final UserPoints? userPoints;
  final Map<String, dynamic>? userStats; // Contains role and streak info
  final bool showLevel;
  final bool animated;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final TextStyle? pointsTextStyle;
  final TextStyle? roleTextStyle;

  const PointsDisplay({
    super.key,
    this.userPoints,
    this.userStats,
    this.showLevel = true,
    this.animated = true,
    this.onTap,
    this.padding,
    this.pointsTextStyle,
    this.roleTextStyle,
  });

  @override
  State<PointsDisplay> createState() => _PointsDisplayState();
}

class _PointsDisplayState extends State<PointsDisplay>
    with TickerProviderStateMixin {
  late AnimationController _pointsController;
  late AnimationController _levelUpController;
  late Animation<int> _pointsAnimation;
  late Animation<double> _scaleAnimation;
  
  int _previousPoints = 0;
  int _currentPoints = 0;

  @override
  void initState() {
    super.initState();
    
    _pointsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _levelUpController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _levelUpController,
      curve: Curves.elasticOut,
    ));

    _updatePoints();
  }

  @override
  void didUpdateWidget(PointsDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.userPoints?.totalPoints != widget.userPoints?.totalPoints) {
      _updatePoints();
    }
    
    // Check for level up
    if (oldWidget.userPoints?.currentLevel != widget.userPoints?.currentLevel &&
        widget.userPoints?.currentLevel != null) {
      _triggerLevelUpAnimation();
    }
  }

  void _updatePoints() {
    if (widget.userPoints != null) {
      _previousPoints = _currentPoints;
      _currentPoints = widget.userPoints!.totalPoints;
      
      if (widget.animated && _previousPoints != _currentPoints) {
        _pointsAnimation = IntTween(
          begin: _previousPoints,
          end: _currentPoints,
        ).animate(CurvedAnimation(
          parent: _pointsController,
          curve: Curves.easeOutCubic,
        ));
        
        _pointsController.reset();
        _pointsController.forward();
      } else {
        _pointsAnimation = AlwaysStoppedAnimation(_currentPoints);
      }
    } else {
      _pointsAnimation = const AlwaysStoppedAnimation(0);
    }
  }

  void _triggerLevelUpAnimation() {
    _levelUpController.reset();
    _levelUpController.forward();
  }

  @override
  void dispose() {
    _pointsController.dispose();
    _levelUpController.dispose();
    super.dispose();
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'novice':
        return Colors.grey;
      case 'explorer':
        return Colors.green;
      case 'adventurer':
        return Colors.blue;
      case 'hero':
        return Colors.purple;
      case 'legend':
        return Colors.orange;
      case 'mythic':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role.toLowerCase()) {
      case 'novice':
        return Icons.person_outline;
      case 'explorer':
        return Icons.explore;
      case 'adventurer':
        return Icons.hiking;
      case 'hero':
        return Icons.shield;
      case 'legend':
        return Icons.stars;
      case 'mythic':
        return Icons.auto_awesome;
      default:
        return Icons.person;
    }
  }

  String _formatPoints(int points) {
    if (points >= 1000000) {
      return '${(points / 1000000).toStringAsFixed(1)}M';
    } else if (points >= 1000) {
      return '${(points / 1000).toStringAsFixed(1)}K';
    }
    return points.toString();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final userPoints = widget.userPoints;
    final userStats = widget.userStats;
    
    if (userPoints == null) {
      return Card(
        child: Padding(
          padding: widget.padding ?? const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.hourglass_empty,
                size: 32,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 8),
              Text(
                'Loading points...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final String role = userStats?['role'] ?? 'novice';
    final int currentStreak = userStats?['current_streak'] ?? 0;
    final roleColor = _getRoleColor(role);
    final roleIcon = _getRoleIcon(role);

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pointsController, _levelUpController]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Card(
              elevation: 4,
              child: Container(
                padding: widget.padding ?? const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [
                      roleColor.withValues(alpha: 0.1),
                      roleColor.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Role badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: roleColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            roleIcon,
                            size: 16,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            role.toUpperCase(),
                            style: widget.roleTextStyle ??
                                theme.textTheme.labelSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Points display
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        AnimatedBuilder(
                          animation: _pointsAnimation,
                          builder: (context, child) {
                            return Text(
                              _formatPoints(_pointsAnimation.value),
                              style: widget.pointsTextStyle ??
                                  theme.textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                            );
                          },
                        ),
                      ],
                    ),
                    
                    if (widget.showLevel && userPoints.currentLevel > 0) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Level ${userPoints.currentLevel}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    
                    // Progress to next level (if available)
                    if (userPoints.pointsToNextLevel > 0) ...[
                      const SizedBox(height: 12),
                      Column(
                        children: [
                          Text(
                            '${userPoints.pointsToNextLevel} points to next level',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: userPoints.pointsToNextLevel > 0
                                ? 1.0 - (userPoints.pointsToNextLevel / 
                                    (userPoints.pointsToNextLevel + 100)) // Approximate
                                : 1.0,
                            backgroundColor: theme.colorScheme.surfaceContainerHighest,
                            valueColor: AlwaysStoppedAnimation(roleColor),
                          ),
                        ],
                      ),
                    ],
                    
                    // Current streak (if available)
                    if (currentStreak > 0) ...[
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.local_fire_department,
                            color: Colors.orange,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '$currentStreak day streak',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
