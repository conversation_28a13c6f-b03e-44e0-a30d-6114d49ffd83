import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Animated progress bar widget for gamification features
class GamificationProgressBar extends StatefulWidget {
  final double progress; // 0.0 to 1.0
  final String? label;
  final String? subLabel;
  final Color? progressColor;
  final Color? backgroundColor;
  final double height;
  final double borderRadius;
  final bool showPercentage;
  final bool showAnimation;
  final Duration animationDuration;
  final Widget? leadingIcon;
  final Widget? trailingWidget;
  final bool showGlow;

  const GamificationProgressBar({
    super.key,
    required this.progress,
    this.label,
    this.subLabel,
    this.progressColor,
    this.backgroundColor,
    this.height = 8.0,
    this.borderRadius = 4.0,
    this.showPercentage = false,
    this.showAnimation = true,
    this.animationDuration = const Duration(milliseconds: 800),
    this.leadingIcon,
    this.trailingWidget,
    this.showGlow = false,
  });

  @override
  State<GamificationProgressBar> createState() => _GamificationProgressBarState();
}

class _GamificationProgressBarState extends State<GamificationProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress.clamp(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    if (widget.showAnimation) {
      _controller.forward();
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(GamificationProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.progress.clamp(0.0, 1.0),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      
      if (widget.showAnimation) {
        _controller.reset();
        _controller.forward();
      } else {
        _controller.value = 1.0;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveProgressColor = widget.progressColor ?? theme.colorScheme.primary;
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Label and percentage row
        if (widget.label != null || widget.showPercentage || widget.trailingWidget != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                if (widget.leadingIcon != null) ...[
                  widget.leadingIcon!,
                  const SizedBox(width: 8),
                ],
                if (widget.label != null)
                  Expanded(
                    child: Text(
                      widget.label!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                if (widget.showPercentage)
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Text(
                        '${(_animation.value * 100).round()}%',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.outline,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    },
                  ),
                if (widget.trailingWidget != null) ...[
                  const SizedBox(width: 8),
                  widget.trailingWidget!,
                ],
              ],
            ),
          ),

        // Progress bar
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Container(
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                color: effectiveBackgroundColor,
                boxShadow: widget.showGlow && _animation.value > 0
                    ? [
                        BoxShadow(
                          color: effectiveProgressColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ]
                    : null,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Stack(
                  children: [
                    // Progress fill
                    FractionallySizedBox(
                      widthFactor: _animation.value,
                      child: Container(
                        height: widget.height,
                        decoration: BoxDecoration(
                          color: effectiveProgressColor,
                          borderRadius: BorderRadius.circular(widget.borderRadius),
                        ),
                      ),
                    ),
                    
                    // Shine effect for completed progress
                    if (_animation.value > 0)
                      FractionallySizedBox(
                        widthFactor: _animation.value,
                        child: Container(
                          height: widget.height,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(widget.borderRadius),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.white.withValues(alpha: 0.3),
                                Colors.transparent,
                                Colors.white.withValues(alpha: 0.1),
                              ],
                              stops: const [0.0, 0.5, 1.0],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),

        // Sub-label
        if (widget.subLabel != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              widget.subLabel!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
          ),
      ],
    );
  }
}

/// Circular progress indicator for level/experience progression
class CircularGamificationProgress extends StatefulWidget {
  final double progress; // 0.0 to 1.0
  final double size;
  final double strokeWidth;
  final Color? progressColor;
  final Color? backgroundColor;
  final Widget? centerWidget;
  final String? label;
  final bool showAnimation;
  final Duration animationDuration;
  final bool showGlow;

  const CircularGamificationProgress({
    super.key,
    required this.progress,
    this.size = 100.0,
    this.strokeWidth = 8.0,
    this.progressColor,
    this.backgroundColor,
    this.centerWidget,
    this.label,
    this.showAnimation = true,
    this.animationDuration = const Duration(milliseconds: 1000),
    this.showGlow = false,
  });

  @override
  State<CircularGamificationProgress> createState() => _CircularGamificationProgressState();
}

class _CircularGamificationProgressState extends State<CircularGamificationProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress.clamp(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    if (widget.showAnimation) {
      _controller.forward();
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(CircularGamificationProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.progress.clamp(0.0, 1.0),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      
      if (widget.showAnimation) {
        _controller.reset();
        _controller.forward();
      } else {
        _controller.value = 1.0;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveProgressColor = widget.progressColor ?? theme.colorScheme.primary;
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: widget.size,
          height: widget.size,
          decoration: widget.showGlow ? BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: effectiveProgressColor.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ) : null,
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: Size(widget.size, widget.size),
                painter: _CircularProgressPainter(
                  progress: _animation.value,
                  progressColor: effectiveProgressColor,
                  backgroundColor: effectiveBackgroundColor,
                  strokeWidth: widget.strokeWidth,
                ),
                child: widget.centerWidget != null
                    ? Center(child: widget.centerWidget!)
                    : null,
              );
            },
          ),
        ),
        if (widget.label != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              widget.label!,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }
}

/// Custom painter for circular progress
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;

  _CircularProgressPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    if (progress > 0) {
      final progressPaint = Paint()
        ..color = progressColor
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      final sweepAngle = 2 * math.pi * progress;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2, // Start from top
        sweepAngle,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(_CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.progressColor != progressColor ||
           oldDelegate.backgroundColor != backgroundColor ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}

/// Multi-segment progress bar for complex progress tracking
class SegmentedProgressBar extends StatefulWidget {
  final List<ProgressSegment> segments;
  final double height;
  final double borderRadius;
  final bool showAnimation;
  final Duration animationDuration;
  final bool showLabels;

  const SegmentedProgressBar({
    super.key,
    required this.segments,
    this.height = 12.0,
    this.borderRadius = 6.0,
    this.showAnimation = true,
    this.animationDuration = const Duration(milliseconds: 1000),
    this.showLabels = true,
  });

  @override
  State<SegmentedProgressBar> createState() => _SegmentedProgressBarState();
}

class _SegmentedProgressBarState extends State<SegmentedProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    if (widget.showAnimation) {
      _controller.forward();
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(SegmentedProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.showAnimation) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Labels
        if (widget.showLabels && widget.segments.any((s) => s.label != null))
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: widget.segments
                  .where((segment) => segment.label != null)
                  .map((segment) => Expanded(
                        flex: (segment.maxValue * 100).toInt(),
                        child: Text(
                          segment.label!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: segment.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),

        // Progress bar
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Container(
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Row(
                  children: widget.segments.map((segment) {
                    final segmentProgress = (segment.currentValue / segment.maxValue)
                        .clamp(0.0, 1.0) * _controller.value;
                    
                    return Expanded(
                      flex: (segment.maxValue * 100).toInt(),
                      child: FractionallySizedBox(
                        widthFactor: segmentProgress,
                        child: Container(
                          height: widget.height,
                          color: segment.color,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// Data class for progress segments
class ProgressSegment {
  final String? label;
  final double currentValue;
  final double maxValue;
  final Color color;

  const ProgressSegment({
    this.label,
    required this.currentValue,
    required this.maxValue,
    required this.color,
  });
}
