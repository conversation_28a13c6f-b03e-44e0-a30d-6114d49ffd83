import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Widget that displays the reward shop with available rewards
class RewardShop extends StatelessWidget {
  final List<Reward> rewards;
  final int currentPoints;
  final bool isLoading;
  final Function(String rewardId)? onPurchaseReward;
  final VoidCallback? onRefresh;
  final Function(Reward reward)? onRewardTap;

  const RewardShop({
    super.key,
    required this.rewards,
    required this.currentPoints,
    this.isLoading = false,
    this.onPurchaseReward,
    this.onRefresh,
    this.onRewardTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final availableRewards = rewards.where((r) => r.isCurrentlyAvailable).toList();
    final purchasableRewards = availableRewards
        .where((r) => r.pointsCost <= currentPoints)
        .toList();

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.redeem,
                  color: theme.colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Reward Shop',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.star,
                        size: 16,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$currentPoints',
                        style: theme.textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
                if (onRefresh != null)
                  IconButton(
                    onPressed: isLoading ? null : onRefresh,
                    icon: AnimatedRotation(
                      turns: isLoading ? 1 : 0,
                      duration: const Duration(seconds: 1),
                      child: const Icon(Icons.refresh),
                    ),
                  ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Summary stats
          if (availableRewards.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  _StatChip(
                    icon: Icons.shopping_bag,
                    label: '${availableRewards.length} Available',
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(width: 8),
                  _StatChip(
                    icon: Icons.check_circle,
                    label: '${purchasableRewards.length} Affordable',
                    color: Colors.green,
                  ),
                ],
              ),
            ),

          // Loading state
          if (isLoading && rewards.isEmpty)
            const Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Empty state
          if (!isLoading && availableRewards.isEmpty)
            Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.store_outlined,
                      size: 64,
                      color: theme.colorScheme.outline,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No rewards available',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Check back later for new rewards!',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Rewards grid
          if (availableRewards.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 0.85,
                ),
                itemCount: availableRewards.length,
                itemBuilder: (context, index) {
                  final reward = availableRewards[index];
                  final canAfford = reward.pointsCost <= currentPoints;
                  
                  return RewardCard(
                    reward: reward,
                    canAfford: canAfford,
                    onPurchase: onPurchaseReward != null 
                        ? () => onPurchaseReward!(reward.id)
                        : null,
                    onTap: onRewardTap != null 
                        ? () => onRewardTap!(reward)
                        : null,
                  );
                },
              ),
            ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

/// Individual reward card widget
class RewardCard extends StatelessWidget {
  final Reward reward;
  final bool canAfford;
  final VoidCallback? onPurchase;
  final VoidCallback? onTap;

  const RewardCard({
    super.key,
    required this.reward,
    required this.canAfford,
    this.onPurchase,
    this.onTap,
  });

  Color _getRarityColor() {
    switch (reward.rarity) {
      case RewardRarity.common:
        return Colors.grey;
      case RewardRarity.uncommon:
        return Colors.green;
      case RewardRarity.rare:
        return Colors.blue;
      case RewardRarity.epic:
        return Colors.purple;
      case RewardRarity.legendary:
        return Colors.amber;
    }
  }

  IconData _getCategoryIcon() {
    switch (reward.type) {
      case RewardType.cosmetic:
        return Icons.palette;
      case RewardType.functionality:
        return Icons.build;
      case RewardType.recognition:
        return Icons.star;
      case RewardType.privilege:
        return Icons.group;
      case RewardType.virtual:
        return Icons.diamond;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rarityColor = _getRarityColor();
    final categoryIcon = _getCategoryIcon();

    return Material(
      elevation: 2,
      borderRadius: BorderRadius.circular(12),
      color: theme.colorScheme.surface,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: rarityColor.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with rarity indicator
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: rarityColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      categoryIcon,
                      color: rarityColor,
                      size: 20,
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: rarityColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        reward.rarity.toString().split('.').last.toUpperCase(),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        reward.title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Description
                      Expanded(
                        child: Text(
                          reward.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Stock indicator
                      if (reward.maxQuantity != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Icon(
                                Icons.inventory,
                                size: 14,
                                color: theme.colorScheme.outline,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Limited quantity available',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: theme.colorScheme.outline,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Price and purchase button
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  size: 16,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${reward.pointsCost}',
                                  style: theme.textTheme.labelLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: canAfford 
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (onPurchase != null)
                            SizedBox(
                              height: 32,
                              child: ElevatedButton(
                                onPressed: canAfford ? onPurchase : null,
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 12),
                                  textStyle: theme.textTheme.labelSmall,
                                ),
                                child: Text(
                                  canAfford ? 'Buy' : 'Need ${reward.pointsCost - (canAfford ? 0 : reward.pointsCost)}',
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Compact reward showcase for dashboard
class CompactRewardShowcase extends StatelessWidget {
  final List<Reward> featuredRewards;
  final int currentPoints;
  final VoidCallback? onViewAll;
  final Function(String rewardId)? onPurchaseReward;

  const CompactRewardShowcase({
    super.key,
    required this.featuredRewards,
    required this.currentPoints,
    this.onViewAll,
    this.onPurchaseReward,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayRewards = featuredRewards.take(3).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.card_giftcard,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Featured Rewards',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (displayRewards.isEmpty)
              Center(
                child: Text(
                  'No featured rewards',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              )
            else
              ...displayRewards.map((reward) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getRewardRarityColor(reward.rarity).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getRewardCategoryIcon(reward.type),
                        color: _getRewardRarityColor(reward.rarity),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            reward.title,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Colors.amber,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${reward.pointsCost}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: reward.pointsCost <= currentPoints
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.error,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    if (onPurchaseReward != null && reward.pointsCost <= currentPoints)
                      SizedBox(
                        height: 28,
                        child: ElevatedButton(
                          onPressed: () => onPurchaseReward!(reward.id),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            textStyle: theme.textTheme.labelSmall,
                          ),
                          child: const Text('Buy'),
                        ),
                      ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Color _getRewardRarityColor(RewardRarity rarity) {
    switch (rarity) {
      case RewardRarity.common:
        return Colors.grey;
      case RewardRarity.uncommon:
        return Colors.green;
      case RewardRarity.rare:
        return Colors.blue;
      case RewardRarity.epic:
        return Colors.purple;
      case RewardRarity.legendary:
        return Colors.amber;
    }
  }

  IconData _getRewardCategoryIcon(RewardType type) {
    switch (type) {
      case RewardType.cosmetic:
        return Icons.palette;
      case RewardType.functionality:
        return Icons.build;
      case RewardType.recognition:
        return Icons.star;
      case RewardType.privilege:
        return Icons.group;
      case RewardType.virtual:
        return Icons.diamond;
    }
  }
}

/// Small stat chip widget
class _StatChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _StatChip({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
