import 'package:flutter/material.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/responsive_helper.dart';
import '../../core/enums/device_type.dart';
import 'widgets/course_discovery_tab.dart';
import 'widgets/my_courses_tab.dart';
import 'widgets/progress_tab.dart';
import 'widgets/certificates_tab.dart';

/// Main learning management system screen with tabbed interface
/// Features comprehensive educational platform functionality
class LearningScreen extends StatefulWidget {
  const LearningScreen({super.key});

  @override
  State<LearningScreen> createState() => _LearningScreenState();
}

class _LearningScreenState extends State<LearningScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  final List<LearningTab> _tabs = [
    LearningTab(
      title: 'Discover',
      icon: Icons.explore_outlined,
      description: 'Find courses to enhance your skills',
    ),
    LearningTab(
      title: 'My Courses',
      icon: Icons.school_outlined,
      description: 'Access your enrolled courses',
    ),
    LearningTab(
      title: 'Progress',
      icon: Icons.trending_up_rounded,
      description: 'Track your learning journey',
    ),
    LearningTab(
      title: 'Certificates',
      icon: Icons.card_membership_rounded,
      description: 'View earned certificates',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) return;
    setState(() {
      _selectedIndex = _tabController.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          _buildHeader(context, deviceType),
          _buildTabBar(context, deviceType),
          Expanded(
            child: _buildTabViews(context),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, DeviceType deviceType) {
    return Container(
      padding: ResponsiveHelper.responsivePadding(context),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            offset: Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: AppColors.secondaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.school_rounded,
                  color: AppColors.onPrimary,
                  size: ResponsiveHelper.responsiveIconSize(context, 24),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Learning Management',
                      style: AppTextStyles.headlineMedium.copyWith(
                        fontSize: ResponsiveHelper.responsiveFontSize(context, 20),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Enhance your skills with personalized learning paths',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
              if (deviceType.isDesktop) ...[
                const SizedBox(width: 16),
                _buildQuickStats(context),
              ],
            ],
          ),
          if (!deviceType.isDesktop) ...[
            const SizedBox(height: 16),
            _buildQuickStats(context),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Row(
      children: [
        _buildStatCard(context, 'Enrolled', '6', Icons.school_outlined),
        const SizedBox(width: 12),
        _buildStatCard(context, 'Completed', '4', Icons.check_circle_outline),
        const SizedBox(width: 12),
        _buildStatCard(context, 'Certificates', '3', Icons.card_membership_rounded),
      ],
    );
  }

  Widget _buildStatCard(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: AppColors.secondary),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                value,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.grey600,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, DeviceType deviceType) {
    if (deviceType.isMobile) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = index == _selectedIndex;
              
              return Padding(
                padding: EdgeInsets.only(right: index < _tabs.length - 1 ? 12 : 0),
                child: _buildMobileTabItem(context, tab, index, isSelected),
              );
            }).toList(),
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: _tabs.map((tab) => _buildDesktopTab(context, tab)).toList(),
        labelColor: AppColors.secondary,
        unselectedLabelColor: AppColors.grey600,
        indicatorColor: AppColors.secondary,
        indicatorWeight: 3,
        labelStyle: AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTextStyles.titleSmall,
        padding: const EdgeInsets.symmetric(horizontal: 16),
      ),
    );
  }

  Widget _buildMobileTabItem(BuildContext context, LearningTab tab, int index, bool isSelected) {
    return GestureDetector(
      onTap: () => _tabController.animateTo(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary.withValues(alpha: 0.1) : AppColors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.secondary : AppColors.border,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              tab.icon,
              size: 16,
              color: isSelected ? AppColors.secondary : AppColors.grey600,
            ),
            const SizedBox(width: 6),
            Text(
              tab.title,
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected ? AppColors.secondary : AppColors.grey600,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopTab(BuildContext context, LearningTab tab) {
    return Tab(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(tab.icon, size: 18),
            const SizedBox(width: 8),
            Text(tab.title),
          ],
        ),
      ),
    );
  }

  Widget _buildTabViews(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: const [
        CourseDiscoveryTab(),
        MyCoursesTab(),
        ProgressTab(),
        CertificatesTab(),
      ],
    );
  }
}

/// Model class for learning tabs
class LearningTab {
  final String title;
  final IconData icon;
  final String description;

  const LearningTab({
    required this.title,
    required this.icon,
    required this.description,
  });
}