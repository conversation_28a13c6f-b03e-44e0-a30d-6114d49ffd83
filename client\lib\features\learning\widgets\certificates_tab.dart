import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';

/// Certificates tab for managing earned certificates
class CertificatesTab extends StatelessWidget {
  const CertificatesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        children: [
          Icon(Icons.card_membership_rounded, size: 64, color: AppColors.grey400),
          SizedBox(height: 16),
          Text(
            'My Certificates',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'View and share your earned certificates',
            style: TextStyle(color: AppColors.grey600),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          Text(
            'Features coming soon:',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: 8),
          Text('• Certificate gallery'),
          Text('• Digital verification'),
          Text('• LinkedIn integration'),
          Text('• PDF downloads'),
        ],
      ),
    );
  }
}