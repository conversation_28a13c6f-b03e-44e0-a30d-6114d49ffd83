import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';

/// Course discovery tab for browsing available courses
class CourseDiscoveryTab extends StatefulWidget {
  const CourseDiscoveryTab({super.key});

  @override
  State<CourseDiscoveryTab> createState() => _CourseDiscoveryTabState();
}

class _CourseDiscoveryTabState extends State<CourseDiscoveryTab> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All';
  String _selectedLevel = 'All';

  final List<String> _categories = [
    'All',
    'Programming',
    'Design',
    'Business',
    'Data Science',
    'Marketing',
    'Personal Development',
  ];

  final List<String> _levels = [
    'All',
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return Column(
      children: [
        _buildSearchAndFilters(context, deviceType),
        Expanded(
          child: _buildCoursesList(context),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters(BuildContext context, deviceType) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search courses...',
              prefixIcon: const Icon(Icons.search_rounded),
              suffixIcon: IconButton(
                icon: const Icon(Icons.tune_rounded),
                onPressed: () => _showFilterDialog(context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: AppColors.grey50,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Quick Filters
          if (deviceType.isDesktop) ...[
            Row(
              children: [
                Expanded(
                  child: _buildDropdownFilter(
                    'Category',
                    _selectedCategory,
                    _categories,
                    (value) => setState(() => _selectedCategory = value!),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDropdownFilter(
                    'Level',
                    _selectedLevel,
                    _levels,
                    (value) => setState(() => _selectedLevel = value!),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.favorite_border_rounded, size: 18),
                  label: const Text('Wishlist'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.secondary,
                    foregroundColor: AppColors.onPrimary,
                  ),
                ),
              ],
            ),
          ] else ...[
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _categories.map((category) {
                  final isSelected = category == _selectedCategory;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() => _selectedCategory = category);
                      },
                      backgroundColor: AppColors.grey100,
                      selectedColor: AppColors.secondary.withValues(alpha: 0.2),
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.secondary : AppColors.grey700,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDropdownFilter(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: AppColors.grey50,
      ),
      items: options.map((option) {
        return DropdownMenuItem(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildCoursesList(BuildContext context) {
    // Mock course data
    final courses = List.generate(8, (index) => _createMockCourse(index));
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: ResponsiveHelper.isMobile(context) ? 1 : 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: ResponsiveHelper.isMobile(context) ? 1.2 : 1.4,
      ),
      itemCount: courses.length,
      itemBuilder: (context, index) {
        return _buildCourseCard(context, courses[index]);
      },
    );
  }

  Widget _buildCourseCard(BuildContext context, Map<String, dynamic> course) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Thumbnail
          Container(
            height: 120,
            decoration: BoxDecoration(
              gradient: _getCourseGradient(course['category']),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Center(
              child: Icon(
                _getCourseIcon(course['category']),
                size: 48,
                color: AppColors.onPrimary,
              ),
            ),
          ),
          
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course Title
                  Text(
                    course['title'],
                    style: AppTextStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Instructor
                  Text(
                    'by ${course['instructor']}',
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Course Stats
                  Row(
                    children: [
                      Icon(Icons.star_rounded, size: 14, color: AppColors.warning),
                      const SizedBox(width: 2),
                      Text(
                        course['rating'].toString(),
                        style: AppTextStyles.labelSmall,
                      ),
                      const SizedBox(width: 8),
                      Icon(Icons.people_outline_rounded, size: 14, color: AppColors.grey500),
                      const SizedBox(width: 2),
                      Text(
                        course['students'],
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.grey500,
                        ),
                      ),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // Price and Enroll Button
                  Row(
                    children: [
                      Text(
                        course['price'],
                        style: AppTextStyles.titleSmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: AppColors.onPrimary,
                          minimumSize: const Size(70, 32),
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                        ),
                        child: const Text('Enroll'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Gradient _getCourseGradient(String category) {
    switch (category) {
      case 'Programming':
        return AppColors.primaryGradient;
      case 'Design':
        return AppColors.secondaryGradient;
      case 'Business':
        return const LinearGradient(
          colors: [AppColors.warning, AppColors.accentLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppColors.primaryGradient;
    }
  }

  IconData _getCourseIcon(String category) {
    switch (category) {
      case 'Programming':
        return Icons.code_rounded;
      case 'Design':
        return Icons.palette_rounded;
      case 'Business':
        return Icons.business_center_rounded;
      case 'Data Science':
        return Icons.analytics_rounded;
      case 'Marketing':
        return Icons.campaign_rounded;
      default:
        return Icons.school_rounded;
    }
  }

  Map<String, dynamic> _createMockCourse(int index) {
    final titles = [
      'Complete Flutter Development',
      'UI/UX Design Masterclass',
      'Business Strategy Fundamentals',
      'Data Science with Python',
      'Digital Marketing Essentials',
      'React Native Development',
      'Graphic Design Principles',
      'Leadership and Management',
    ];
    
    final instructors = [
      'John Doe',
      'Sarah Wilson',
      'Mike Johnson',
      'Emily Chen',
      'David Brown',
      'Lisa Garcia',
      'Tom Anderson',
      'Anna Taylor',
    ];
    
    final categories = [
      'Programming',
      'Design',
      'Business',
      'Data Science',
      'Marketing',
      'Programming',
      'Design',
      'Business',
    ];
    
    final ratings = [4.8, 4.6, 4.9, 4.5, 4.7, 4.8, 4.4, 4.6];
    final students = ['2.1k', '1.8k', '3.2k', '1.5k', '2.7k', '1.9k', '1.3k', '2.4k'];
    final prices = ['Free', '\$49', '\$79', '\$129', '\$39', '\$99', 'Free', '\$69'];
    
    return {
      'title': titles[index],
      'instructor': instructors[index],
      'category': categories[index],
      'rating': ratings[index],
      'students': students[index],
      'price': prices[index],
    };
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Courses'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Advanced filtering options will be available here.'),
            SizedBox(height: 16),
            Text('Coming soon: Duration, Price range, Language filters'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}