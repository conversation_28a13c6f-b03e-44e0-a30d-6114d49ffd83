import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

/// My Courses tab for managing enrolled courses
class MyCoursesTab extends StatefulWidget {
  const MyCoursesTab({super.key});

  @override
  State<MyCoursesTab> createState() => _MyCoursesTabState();
}

class _MyCoursesTabState extends State<MyCoursesTab> with TickerProviderStateMixin {
  late TabController _tabController;

  // Mock data for courses
  final List<Map<String, dynamic>> _activeCourses = [
    {
      'id': '1',
      'title': 'Advanced Flutter Development',
      'instructor': 'Dr. <PERSON>',
      'progress': 0.65,
      'totalLessons': 24,
      'completedLessons': 16,
      'duration': '8 weeks',
      'category': 'Mobile Development',
      'nextLesson': 'State Management with Bloc',
      'enrolledAt': DateTime.now().subtract(const Duration(days: 30)),
    },
    {
      'id': '2',
      'title': 'UI/UX Design Fundamentals',
      'instructor': '<PERSON>',
      'progress': 0.30,
      'totalLessons': 18,
      'completedLessons': 5,
      'duration': '6 weeks',
      'category': 'Design',
      'nextLesson': 'Color Theory and Psychology',
      'enrolledAt': DateTime.now().subtract(const Duration(days: 15)),
    },
  ];

  final List<Map<String, dynamic>> _completedCourses = [
    {
      'id': '3',
      'title': 'Dart Programming Basics',
      'instructor': 'Alex Rodriguez',
      'progress': 1.0,
      'totalLessons': 12,
      'completedLessons': 12,
      'duration': '4 weeks',
      'category': 'Programming',
      'completedAt': DateTime.now().subtract(const Duration(days: 60)),
      'certificateId': 'CERT-001',
      'rating': 5,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildActiveCourses(),
              _buildCompletedCourses(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.school_outlined,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'My Courses',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Track your learning progress and achievements',
                  style: TextStyle(
                    color: AppColors.grey600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildLearningStats(),
        ],
      ),
    );
  }

  Widget _buildLearningStats() {
    final totalHours = _activeCourses.length * 20 + _completedCourses.length * 25;
    final completedCourses = _completedCourses.length;

    return Row(
      children: [
        _buildStatCard('Hours', totalHours.toString(), Colors.blue),
        const SizedBox(width: 8),
        _buildStatCard('Completed', completedCourses.toString(), Colors.green),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(6),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.grey600,
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.play_circle_outline, size: 16),
                const SizedBox(width: 8),
                Text('Active (${_activeCourses.length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.check_circle_outline, size: 16),
                const SizedBox(width: 8),
                Text('Completed (${_completedCourses.length})'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveCourses() {
    if (_activeCourses.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            SizedBox(height: 16),
            Text(
              'No Active Courses',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.grey600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Enroll in courses to start your learning journey',
              style: TextStyle(color: AppColors.grey500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activeCourses.length,
      itemBuilder: (context, index) {
        final course = _activeCourses[index];
        return _buildCourseCard(course, isActive: true);
      },
    );
  }

  Widget _buildCompletedCourses() {
    if (_completedCourses.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            SizedBox(height: 16),
            Text(
              'No Completed Courses',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.grey600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Complete courses to earn certificates',
              style: TextStyle(color: AppColors.grey500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _completedCourses.length,
      itemBuilder: (context, index) {
        final course = _completedCourses[index];
        return _buildCourseCard(course, isActive: false);
      },
    );
  }

  Widget _buildCourseCard(Map<String, dynamic> course, {required bool isActive}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled,
                    color: AppColors.primary,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        course['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'by ${course['instructor']}',
                        style: const TextStyle(
                          color: AppColors.grey600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          course['category'],
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (isActive) ...[
              LinearProgressIndicator(
                value: course['progress'],
                backgroundColor: AppColors.grey200,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    '${course['completedLessons']}/${course['totalLessons']} lessons',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.grey600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${(course['progress'] * 100).round()}% complete',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Next: ${course['nextLesson']}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
              ),
            ] else ...[
              Row(
                children: [
                  ...List.generate(5, (index) => Icon(
                    index < course['rating'] ? Icons.star : Icons.star_border,
                    size: 16,
                    color: Colors.amber,
                  )),
                  const SizedBox(width: 8),
                  Text(
                    'Completed ${_formatDate(course['completedAt'])}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.grey600,
                    ),
                  ),
                  const Spacer(),
                  if (course['certificateId'] != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.verified, size: 12, color: Colors.green),
                          SizedBox(width: 4),
                          Text(
                            'Certified',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).round()} weeks ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}