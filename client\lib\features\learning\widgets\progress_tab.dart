import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';

/// Progress tab for tracking learning progress
class ProgressTab extends StatelessWidget {
  const ProgressTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: <PERSON>umn(
        children: [
          Icon(Icons.trending_up_rounded, size: 64, color: AppColors.grey400),
          SizedBox(height: 16),
          Text(
            'Learning Progress',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Track your learning journey and achievements',
            style: TextStyle(color: AppColors.grey600),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          Text(
            'Features coming soon:',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: 8),
          Text('• Learning analytics dashboard'),
          Text('• Skill progression tracking'),
          Text('• Weekly/monthly reports'),
          Text('• Goal setting and milestones'),
        ],
      ),
    );
  }
}