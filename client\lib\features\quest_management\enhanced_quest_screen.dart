import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/quests/bloc/quest_bloc.dart';
import '../../core/bloc/realtime/realtime_bloc.dart';
import '../../features/realtime/widgets/live_notifications.dart';
import '../../features/realtime/widgets/user_presence_indicator.dart';
import '../../features/quests/widgets/create_quest_dialog.dart';
import '../../features/analytics/screens/analytics_dashboard_screen.dart';
import 'package:shared/shared.dart';

class EnhancedQuestManagementScreen extends StatefulWidget {
  const EnhancedQuestManagementScreen({super.key});

  @override
  State<EnhancedQuestManagementScreen> createState() => _EnhancedQuestManagementScreenState();
}

class _EnhancedQuestManagementScreenState extends State<EnhancedQuestManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fabAnimationController;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _showNotifications = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Initialize real-time connection
    context.read<RealtimeBloc>().add(const ConnectWebSocket('ws://localhost:8080/ws'));
    
    // Load initial quests
    context.read<QuestBloc>().add(const LoadQuests());
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: Column(
        children: [
          _buildRealTimeHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllQuestsTab(),
                _buildActiveQuestsTab(),
                _buildCompletedQuestsTab(),
                _buildCollaborativeQuestsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
      bottomNavigationBar: _buildBottomAppBar(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text('Quest Management'),
      elevation: 0,
      actions: [
        // Real-time presence indicator
        BlocBuilder<RealtimeBloc, RealtimeState>(
          builder: (context, state) {
            if (state is RealtimeConnected) {
              return Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: UserPresenceIndicator(
                  userId: 'current-user',
                  showActivity: true,
                  child: const CircleAvatar(
                    radius: 16,
                    child: Icon(Icons.person, size: 16),
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        
        // Notifications toggle
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.notifications),
              BlocBuilder<RealtimeBloc, RealtimeState>(
                builder: (context, state) {
                  if (state is RealtimeConnected) {
                    // For now, show no notifications indicator
                    // In real implementation, this would check for actual unread notifications
                    return const SizedBox.shrink();
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
          onPressed: () {
            setState(() {
              _showNotifications = !_showNotifications;
            });
          },
        ),
        
        // Menu for additional options
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'sync',
              child: ListTile(
                leading: Icon(Icons.sync),
                title: Text('Sync All'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Data'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('Settings'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRealTimeHeader() {
    if (!_showNotifications) return const SizedBox.shrink();
    
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: const LiveNotificationsPanel(),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabs: const [
          Tab(icon: Icon(Icons.all_inclusive), text: 'All'),
          Tab(icon: Icon(Icons.play_arrow), text: 'Active'),
          Tab(icon: Icon(Icons.check_circle), text: 'Completed'),
          Tab(icon: Icon(Icons.group), text: 'Collaborative'),
          Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
        ],
      ),
    );
  }

  Widget _buildAllQuestsTab() {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is QuestError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: ${state.message}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<QuestBloc>().add(const LoadQuests());
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        } else if (state is QuestLoaded) {
          return _buildQuestList(state.quests);
        }
        return const Center(child: Text('No quests available'));
      },
    );
  }

  Widget _buildActiveQuestsTab() {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestLoaded) {
          final activeQuests = state.quests.where((quest) => 
            quest.status == QuestStatus.inProgress || 
            quest.status == QuestStatus.active
          ).toList();
          return _buildQuestList(activeQuests);
        }
        return const Center(child: Text('No active quests'));
      },
    );
  }

  Widget _buildCompletedQuestsTab() {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestLoaded) {
          final completedQuests = state.quests.where((quest) => 
            quest.status == QuestStatus.completed
          ).toList();
          return _buildQuestList(completedQuests);
        }
        return const Center(child: Text('No completed quests'));
      },
    );
  }

  Widget _buildCollaborativeQuestsTab() {
    return BlocBuilder<QuestBloc, QuestState>(
      builder: (context, state) {
        if (state is QuestLoaded) {
          final collaborativeQuests = state.quests.where((quest) => 
            quest.participantIds.length > 1
          ).toList();
          
          if (collaborativeQuests.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.group, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text('No collaborative quests yet'),
                  Text('Invite others to join your quests!'),
                ],
              ),
            );
          }
          
          return Column(
            children: [
              // Real-time collaborators header
              BlocBuilder<RealtimeBloc, RealtimeState>(
                builder: (context, realtimeState) {
                  if (realtimeState is RealtimeConnected) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.people, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Collaborative Quests',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          const Text('Online collaborators coming soon'),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
              Expanded(child: _buildQuestList(collaborativeQuests)),
            ],
          );
        }
        return const Center(child: Text('No collaborative quests'));
      },
    );
  }

  Widget _buildAnalyticsTab() {
    return const AdvancedAnalyticsDashboard();
  }

  Widget _buildQuestList(List<Quest> quests) {
    if (quests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.assignment, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('No quests found'),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () => _showCreateQuestDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Create Your First Quest'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: quests.length,
      itemBuilder: (context, index) {
        final quest = quests[index];
        return _buildEnhancedQuestCard(quest);
      },
    );
  }

  Widget _buildEnhancedQuestCard(Quest quest) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToQuestDetails(quest),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and real-time indicators
              Row(
                children: [
                  Expanded(
                    child: Text(
                      quest.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Real-time activity indicator
                  BlocBuilder<RealtimeBloc, RealtimeState>(
                    builder: (context, state) {
                      if (state is RealtimeConnected) {
                        // For now, no activity indicator since we don't have activeCollaborationSessions
                        // In real implementation, this would check for active collaboration sessions
                        return const SizedBox.shrink();
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                  _buildQuestStatusChip(quest.status),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Description
              if (quest.description.isNotEmpty)
                Text(
                  quest.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              
              const SizedBox(height: 12),
              
              // Progress and participants
              Row(
                children: [
                  // Progress indicator
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Progress',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: _calculateProgress(quest),
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(_calculateProgress(quest) * 100).toInt()}% Complete',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Participants
                  if (quest.participantIds.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Participants',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.people,
                              size: 16,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${quest.participantIds.length}',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                ],
              ),
              
              // Action buttons for mobile
              const SizedBox(height: 12),
              Row(
                children: [
                  // Priority indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPriorityColor(quest.priority).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      quest.priority.toString().split('.').last.toUpperCase(),
                      style: TextStyle(
                        color: _getPriorityColor(quest.priority),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Quick actions
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _editQuest(quest),
                    tooltip: 'Edit Quest',
                  ),
                  IconButton(
                    icon: const Icon(Icons.share, size: 20),
                    onPressed: () => _shareQuest(quest),
                    tooltip: 'Share Quest',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuestStatusChip(QuestStatus status) {
    Color color;
    String label;
    
    switch (status) {
      case QuestStatus.draft:
        color = Colors.grey;
        label = 'Draft';
        break;
      case QuestStatus.active:
        color = Colors.orange;
        label = 'Active';
        break;
      case QuestStatus.inProgress:
        color = Colors.blue;
        label = 'In Progress';
        break;
      case QuestStatus.completed:
        color = Colors.green;
        label = 'Completed';
        break;
      case QuestStatus.archived:
        color = Colors.grey.shade600;
        label = 'Archived';
        break;
      case QuestStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getPriorityColor(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Colors.green;
      case QuestPriority.medium:
        return Colors.orange;
      case QuestPriority.high:
        return Colors.red;
      case QuestPriority.urgent:
        return Colors.purple;
    }
  }

  double _calculateProgress(Quest quest) {
    if (quest.taskIds.isEmpty) return 0.0;
    // For now, use progressPercentage from quest model
    // In a real implementation, this would fetch task completion status
    return quest.progressPercentage / 100.0;
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "create_quest",
          onPressed: () => _showCreateQuestDialog(context),
          tooltip: 'Create Quest',
          child: const Icon(Icons.add),
        ),
        const SizedBox(height: 8),
        FloatingActionButton.small(
          heroTag: "quick_task",
          onPressed: () => _showQuickTaskDialog(context),
          tooltip: 'Quick Task',
          child: const Icon(Icons.task_alt),
        ),
      ],
    );
  }

  Widget _buildBottomAppBar() {
    return BottomAppBar(
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => _scaffoldKey.currentState?.openDrawer(),
          ),
          const Spacer(),
          // Real-time connection status
          BlocBuilder<RealtimeBloc, RealtimeState>(
            builder: (context, state) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    state is RealtimeConnected ? Icons.wifi : Icons.wifi_off,
                    size: 16,
                    color: state is RealtimeConnected ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    state is RealtimeConnected ? 'Online' : 'Offline',
                    style: TextStyle(
                      color: state is RealtimeConnected ? Colors.green : Colors.red,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Quest Management',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                BlocBuilder<RealtimeBloc, RealtimeState>(
                  builder: (context, state) {
                    if (state is RealtimeConnected) {
                      return const Text(
                        'Connected to real-time services',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      );
                    }
                    return const Text(
                      'Offline mode',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard'),
            onTap: () {
              Navigator.pop(context);
              _tabController.animateTo(4); // Analytics tab
            },
          ),
          ListTile(
            leading: const Icon(Icons.group),
            title: const Text('Collaboration'),
            onTap: () {
              Navigator.pop(context);
              _tabController.animateTo(3); // Collaborative tab
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications),
            title: const Text('Notifications'),
            onTap: () {
              Navigator.pop(context);
              setState(() {
                _showNotifications = !_showNotifications;
              });
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              _handleMenuSelection('settings');
            },
          ),
          ListTile(
            leading: const Icon(Icons.help),
            title: const Text('Help & Support'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to help screen
            },
          ),
        ],
      ),
    );
  }

  void _showCreateQuestDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateQuestDialog(
        onQuestCreated: (questData) {
          // Create quest using the data
          context.read<QuestBloc>().add(CreateQuest(questData));
        },
      ),
    );
  }

  void _showQuickTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Task'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Enter task description...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Create quick task
              Navigator.pop(context);
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'sync':
        context.read<QuestBloc>().add(const LoadQuests());
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Syncing data...')),
        );
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export feature coming soon')),
        );
        break;
      case 'settings':
        // Navigate to settings
        break;
    }
  }

  void _navigateToQuestDetails(Quest quest) {
    // Navigate to quest details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening quest: ${quest.title}')),
    );
  }

  void _editQuest(Quest quest) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing quest: ${quest.title}')),
    );
  }

  void _shareQuest(Quest quest) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing quest: ${quest.title}')),
    );
  }
}
