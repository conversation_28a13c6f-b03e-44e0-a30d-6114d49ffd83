import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/services/api_service.dart';
import 'package:shared/shared.dart';

/// Events for Quest BLoC
abstract class QuestEvent extends Equatable {
  const QuestEvent();

  @override
  List<Object?> get props => [];
}

class LoadQuests extends QuestEvent {
  final String? userId;
  final String? status;

  const LoadQuests({this.userId, this.status});

  @override
  List<Object?> get props => [userId, status];
}

class CreateQuest extends QuestEvent {
  final CreateQuestDTO questData;

  const CreateQuest(this.questData);

  @override
  List<Object?> get props => [questData];
}

class UpdateQuest extends QuestEvent {
  final String questId;
  final UpdateQuestDTO questData;

  const UpdateQuest(this.questId, this.questData);

  @override
  List<Object?> get props => [questId, questData];
}

class DeleteQuest extends QuestEvent {
  final String questId;

  const DeleteQuest(this.questId);

  @override
  List<Object?> get props => [questId];
}

class CompleteQuest extends QuestEvent {
  final String questId;
  final String userId;

  const CompleteQuest(this.questId, this.userId);

  @override
  List<Object?> get props => [questId, userId];
}

/// States for Quest BLoC
abstract class QuestState extends Equatable {
  const QuestState();

  @override
  List<Object?> get props => [];
}

class QuestInitial extends QuestState {}

class QuestLoading extends QuestState {}

class QuestLoaded extends QuestState {
  final List<Quest> quests;
  final Map<String, QuestProgress> questProgress;

  const QuestLoaded({
    required this.quests,
    this.questProgress = const {},
  });

  @override
  List<Object?> get props => [quests, questProgress];

  QuestLoaded copyWith({
    List<Quest>? quests,
    Map<String, QuestProgress>? questProgress,
  }) {
    return QuestLoaded(
      quests: quests ?? this.quests,
      questProgress: questProgress ?? this.questProgress,
    );
  }
}

class QuestError extends QuestState {
  final String message;

  const QuestError(this.message);

  @override
  List<Object?> get props => [message];
}

class QuestSuccess extends QuestState {
  final String message;

  const QuestSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Quest BLoC for managing quest operations
class QuestBloc extends Bloc<QuestEvent, QuestState> {
  final ApiService _apiService;

  QuestBloc({required ApiService apiService}) 
    : _apiService = apiService,
      super(QuestInitial()) {
    
    on<LoadQuests>(_onLoadQuests);
    on<CreateQuest>(_onCreateQuest);
    on<UpdateQuest>(_onUpdateQuest);
    on<DeleteQuest>(_onDeleteQuest);
    on<CompleteQuest>(_onCompleteQuest);
  }

  Future<void> _onLoadQuests(
    LoadQuests event,
    Emitter<QuestState> emit,
  ) async {
    emit(QuestLoading());
    
    try {
      // Try to load quests from API, fall back to mock data if not available
      List<Quest> quests;
      try {
        final response = await _apiService.get('/api/v1/quests');
        if (response.statusCode == 200) {
          final data = response.data as Map<String, dynamic>;
          final questsData = data['quests'] as List<dynamic>;
          quests = questsData.map((json) => Quest.fromJson(json)).toList();
        } else {
          throw Exception('API returned ${response.statusCode}');
        }
      } catch (apiError) {
        // Fall back to mock data if API is not available
        quests = _createMockQuests();
      }
      final questProgress = _createMockQuestProgress();
      
      emit(QuestLoaded(
        quests: quests,
        questProgress: questProgress,
      ));
    } catch (e) {
      emit(QuestError('Failed to load quests: $e'));
    }
  }

  Future<void> _onCreateQuest(
    CreateQuest event,
    Emitter<QuestState> emit,
  ) async {
    try {
      // Try to create quest via API, fall back to mock if not available
      try {
        final result = await _apiService.createQuest(event.questData.toJson());
        if (result == null) {
          throw Exception('Quest creation failed');
        }
      } catch (apiError) {
        // Fall back to mock quest creation for now
        await Future.delayed(const Duration(milliseconds: 500));
      }
      
      emit(const QuestSuccess('Quest created successfully'));
      
      // Reload quests
      add(const LoadQuests());
    } catch (e) {
      emit(QuestError('Failed to create quest: $e'));
    }
  }

  Future<void> _onUpdateQuest(
    UpdateQuest event,
    Emitter<QuestState> emit,
  ) async {
    try {
      // Mock quest update
      await Future.delayed(const Duration(milliseconds: 500));
      
      emit(const QuestSuccess('Quest updated successfully'));
      
      // Reload quests
      add(const LoadQuests());
    } catch (e) {
      emit(QuestError('Failed to update quest: $e'));
    }
  }

  Future<void> _onDeleteQuest(
    DeleteQuest event,
    Emitter<QuestState> emit,
  ) async {
    try {
      // Mock quest deletion
      await Future.delayed(const Duration(milliseconds: 500));
      
      emit(const QuestSuccess('Quest deleted successfully'));
      
      // Reload quests
      add(const LoadQuests());
    } catch (e) {
      emit(QuestError('Failed to delete quest: $e'));
    }
  }

  Future<void> _onCompleteQuest(
    CompleteQuest event,
    Emitter<QuestState> emit,
  ) async {
    try {
      // Mock quest completion
      await Future.delayed(const Duration(milliseconds: 500));
      
      emit(const QuestSuccess('Quest completed! Points awarded.'));
      
      // Reload quests
      add(const LoadQuests());
    } catch (e) {
      emit(QuestError('Failed to complete quest: $e'));
    }
  }

  List<Quest> _createMockQuests() {
    final now = DateTime.now();
    return [
      Quest(
        id: 'quest_001',
        title: 'Complete Daily Workout',
        description: 'Complete a 30-minute workout session every day for a week',
        createdById: 'user_001',
        status: QuestStatus.active,
        priority: QuestPriority.medium,
        difficulty: QuestDifficulty.intermediate,
        category: QuestCategory.health,
        basePoints: 100,
        bonusPoints: 50,
        totalPoints: 150,
        earnedPoints: 75,
        progressPercentage: 50.0,
        taskIds: ['task_001', 'task_002'],
        participantIds: [],
        tags: ['health', 'fitness', 'daily'],
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 6)),
        deadline: now.add(const Duration(days: 5)),
        estimatedHours: 7,
        actualHours: 3,
        startedAt: now.subtract(const Duration(days: 2)),
      ),
      Quest(
        id: 'quest_002',
        title: 'Learn Flutter Development',
        description: 'Complete a comprehensive Flutter development course with practical projects',
        createdById: 'user_001',
        status: QuestStatus.inProgress,
        priority: QuestPriority.high,
        difficulty: QuestDifficulty.advanced,
        category: QuestCategory.learning,
        basePoints: 250,
        bonusPoints: 100,
        totalPoints: 350,
        earnedPoints: 125,
        progressPercentage: 35.7,
        taskIds: ['task_003', 'task_004', 'task_005'],
        participantIds: ['user_002'],
        tags: ['learning', 'flutter', 'development'],
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        deadline: now.add(const Duration(days: 30)),
        estimatedHours: 40,
        actualHours: 15,
        startedAt: now.subtract(const Duration(days: 6)),
      ),
      Quest(
        id: 'quest_003',
        title: 'Organize Home Office',
        description: 'Create an efficient and comfortable workspace at home',
        createdById: 'user_001',
        status: QuestStatus.completed,
        priority: QuestPriority.low,
        difficulty: QuestDifficulty.beginner,
        category: QuestCategory.productivity,
        basePoints: 75,
        bonusPoints: 25,
        totalPoints: 100,
        earnedPoints: 100,
        progressPercentage: 100.0,
        taskIds: ['task_006'],
        participantIds: [],
        tags: ['productivity', 'organization', 'workspace'],
        createdAt: now.subtract(const Duration(days: 14)),
        updatedAt: now.subtract(const Duration(days: 1)),
        completedAt: now.subtract(const Duration(days: 1)),
        startedAt: now.subtract(const Duration(days: 14)),
      ),
      Quest(
        id: 'quest_004',
        title: 'Team Project: Website Redesign',
        description: 'Collaborate with team to redesign the company website with modern UI/UX',
        createdById: 'user_003',
        assignedToId: 'user_001',
        status: QuestStatus.active,
        priority: QuestPriority.urgent,
        difficulty: QuestDifficulty.expert,
        category: QuestCategory.work,
        basePoints: 400,
        bonusPoints: 200,
        totalPoints: 600,
        earnedPoints: 180,
        progressPercentage: 30.0,
        taskIds: ['task_007', 'task_008', 'task_009', 'task_010'],
        participantIds: ['user_002', 'user_004', 'user_005'],
        tags: ['work', 'team', 'design', 'website'],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        deadline: now.add(const Duration(days: 21)),
        estimatedHours: 60,
        actualHours: 18,
        startedAt: now.subtract(const Duration(days: 5)),
      ),
      Quest(
        id: 'quest_005',
        title: 'Creative Writing Challenge',
        description: 'Write a short story every day for two weeks to improve creative writing skills',
        createdById: 'user_001',
        status: QuestStatus.draft,
        priority: QuestPriority.medium,
        difficulty: QuestDifficulty.intermediate,
        category: QuestCategory.creative,
        basePoints: 200,
        bonusPoints: 75,
        totalPoints: 275,
        earnedPoints: 0,
        progressPercentage: 0.0,
        taskIds: [],
        participantIds: [],
        tags: ['creative', 'writing', 'challenge'],
        createdAt: now.subtract(const Duration(hours: 3)),
        updatedAt: now.subtract(const Duration(hours: 3)),
      ),
    ];
  }

  Map<String, QuestProgress> _createMockQuestProgress() {
    final now = DateTime.now();
    return {
      'quest_001': QuestProgress(
        id: 'progress_001',
        questId: 'quest_001',
        userId: 'current_user',
        progressPercentage: 0.5,
        pointsEarned: 75,
        totalPointsAvailable: 150,
        tasksCompleted: 1,
        totalTasks: 2,
        timeSpentMinutes: 180,
        milestoneTimestamps: {
          'started': now.subtract(const Duration(days: 2)),
          'quarter': now.subtract(const Duration(days: 1)),
        },
        dailyProgress: {
          _formatDate(now.subtract(const Duration(days: 2))): 0.25,
          _formatDate(now.subtract(const Duration(days: 1))): 0.25,
        },
        currentStreak: 2,
        longestStreak: 2,
        isActive: true,
        startedAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 6)),
      ),
      'quest_002': QuestProgress(
        id: 'progress_002',
        questId: 'quest_002',
        userId: 'current_user',
        progressPercentage: 0.357,
        pointsEarned: 125,
        totalPointsAvailable: 350,
        tasksCompleted: 1,
        totalTasks: 3,
        timeSpentMinutes: 900,
        milestoneTimestamps: {
          'started': now.subtract(const Duration(days: 6)),
          'quarter': now.subtract(const Duration(days: 4)),
        },
        dailyProgress: {
          _formatDate(now.subtract(const Duration(days: 6))): 0.1,
          _formatDate(now.subtract(const Duration(days: 5))): 0.12,
          _formatDate(now.subtract(const Duration(days: 4))): 0.137,
        },
        currentStreak: 1,
        longestStreak: 3,
        isActive: true,
        startedAt: now.subtract(const Duration(days: 6)),
        updatedAt: now.subtract(const Duration(hours: 2)),
      ),
      'quest_003': QuestProgress(
        id: 'progress_003',
        questId: 'quest_003',
        userId: 'current_user',
        progressPercentage: 1.0,
        pointsEarned: 100,
        totalPointsAvailable: 100,
        tasksCompleted: 1,
        totalTasks: 1,
        timeSpentMinutes: 480,
        milestoneTimestamps: {
          'started': now.subtract(const Duration(days: 14)),
          'quarter': now.subtract(const Duration(days: 12)),
          'half': now.subtract(const Duration(days: 8)),
          'three_quarters': now.subtract(const Duration(days: 4)),
          'completed': now.subtract(const Duration(days: 1)),
        },
        dailyProgress: {
          _formatDate(now.subtract(const Duration(days: 14))): 0.15,
          _formatDate(now.subtract(const Duration(days: 12))): 0.1,
          _formatDate(now.subtract(const Duration(days: 8))): 0.25,
          _formatDate(now.subtract(const Duration(days: 4))): 0.25,
          _formatDate(now.subtract(const Duration(days: 1))): 0.25,
        },
        currentStreak: 0,
        longestStreak: 5,
        isActive: false,
        startedAt: now.subtract(const Duration(days: 14)),
        updatedAt: now.subtract(const Duration(days: 1)),
        completedAt: now.subtract(const Duration(days: 1)),
      ),
    };
  }

  /// Format date for daily progress keys (YYYY-MM-DD)
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}