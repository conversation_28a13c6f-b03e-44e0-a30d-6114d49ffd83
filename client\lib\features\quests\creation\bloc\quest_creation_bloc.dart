import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import 'package:shared/shared.dart';
import '../../../../core/services/api_service.dart';
import '../services/quest_validation_service.dart';
import 'quest_creation_event.dart';
import 'quest_creation_state.dart';

/// BLoC for managing the comprehensive quest creation process
class QuestCreationBloc extends Bloc<QuestCreationEvent, QuestCreationState> {
  final ApiService _apiService;
  final QuestValidationService _validationService;
  final Uuid _uuid = const Uuid();

  QuestCreationBloc({
    required ApiService apiService,
    required QuestValidationService validationService,
  })  : _apiService = apiService,
        _validationService = validationService,
        super(const QuestCreationInitial()) {
    
    // Initialize the quest creation process
    on<InitializeQuestCreation>(_onInitializeQuestCreation);
    
    // Form field updates
    on<UpdateFormField>(_onUpdateFormField);
    on<UpdateTitle>(_onUpdateTitle);
    on<UpdateDescription>(_onUpdateDescription);
    on<UpdateCategory>(_onUpdateCategory);
    on<UpdateBasicInfo>(_onUpdateBasicInfo);
    on<UpdateDifficultyAndPriority>(_onUpdateDifficultyAndPriority);
    on<UpdatePointsConfig>(_onUpdatePointsConfig);
    on<UpdateScheduling>(_onUpdateScheduling);
    on<UpdateTags>(_onUpdateTags);
    on<UpdateAdvancedSettings>(_onUpdateAdvancedSettings);
    
    // Task management
    on<AddTask>(_onAddTask);
    on<UpdateTask>(_onUpdateTask);
    on<RemoveTask>(_onRemoveTask);
    on<ReorderTasks>(_onReorderTasks);
    
    // Participant management
    on<AddParticipants>(_onAddParticipants);
    on<RemoveParticipant>(_onRemoveParticipant);
    
    // Validation and form control
    on<ValidateForm>(_onValidateForm);
    
    // Draft management
    on<SaveDraft>(_onSaveDraft);
    on<LoadDraft>(_onLoadDraft);
    on<DeleteDraft>(_onDeleteDraft);
    
    // Template management
    on<LoadTemplates>(_onLoadTemplates);
    on<ApplyTemplate>(_onApplyTemplate);
    
    // Quest submission
    on<SubmitQuest>(_onSubmitQuest);
    
    // Form control
    on<ResetForm>(_onResetForm);
    on<PreviewQuest>(_onPreviewQuest);
  }

  /// Initialize the quest creation process
  Future<void> _onInitializeQuestCreation(
    InitializeQuestCreation event,
    Emitter<QuestCreationState> emit,
  ) async {
    emit(const QuestCreationLoading(message: 'Initializing quest creation...'));
    
    try {
      // Load templates first
      final templates = await _loadQuestTemplates();
      
      // Initialize with default values
      var initialState = const QuestCreationInProgress(
        availableTemplates: [],
      );
      
      // Apply template if provided
      if (event.templateId != null) {
        final template = templates.firstWhere(
          (t) => t.id == event.templateId,
          orElse: () => templates.first,
        );
        initialState = _applyTemplateToState(initialState, template);
      }
      
      // Load draft if provided
      if (event.draftId != null) {
        initialState = await _loadDraftById(event.draftId!, initialState);
      }
      
      // Update with loaded templates
      initialState = initialState.copyWith(availableTemplates: templates);
      
      emit(initialState);
    } catch (e) {
      emit(QuestCreationError(message: 'Failed to initialize quest creation: $e'));
    }
  }

  /// Handle generic form field updates
  Future<void> _onUpdateFormField(
    UpdateFormField event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      QuestCreationInProgress updatedState;

      switch (event.fieldName) {
        case 'title':
          updatedState = currentState.copyWith(
            title: event.value as String,
            hasUnsavedChanges: true,
          );
          break;
        case 'description':
          updatedState = currentState.copyWith(
            description: event.value as String,
            hasUnsavedChanges: true,
          );
          break;
        case 'category':
          updatedState = currentState.copyWith(
            category: event.value,
            hasUnsavedChanges: true,
          );
          break;
        default:
          return; // Unknown field, ignore
      }

      // Validate the updated state
      final validationResult = _validationService.validateQuest(updatedState);
      updatedState = updatedState.copyWith(
        validationStatus: validationResult.isValid
          ? FormValidationStatus.valid
          : FormValidationStatus.invalid,
        fieldErrors: validationResult.fieldErrors,
        generalErrors: validationResult.generalErrors,
      );

      emit(updatedState);
    }
  }

  /// Update quest title
  Future<void> _onUpdateTitle(
    UpdateTitle event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;

      final updatedState = currentState.copyWith(
        title: event.title,
        hasUnsavedChanges: true,
      );

      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update quest description
  Future<void> _onUpdateDescription(
    UpdateDescription event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;

      final updatedState = currentState.copyWith(
        description: event.description,
        hasUnsavedChanges: true,
      );

      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update quest category
  Future<void> _onUpdateCategory(
    UpdateCategory event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;

      final updatedState = currentState.copyWith(
        category: event.category,
        hasUnsavedChanges: true,
      );

      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update basic quest information
  Future<void> _onUpdateBasicInfo(
    UpdateBasicInfo event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedState = currentState.copyWith(
        title: event.title ?? currentState.title,
        description: event.description ?? currentState.description,
        category: event.category ?? currentState.category,
        hasUnsavedChanges: true,
      );
      
      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update quest difficulty and priority
  Future<void> _onUpdateDifficultyAndPriority(
    UpdateDifficultyAndPriority event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      var updatedState = currentState.copyWith(
        difficulty: event.difficulty ?? currentState.difficulty,
        priority: event.priority ?? currentState.priority,
        hasUnsavedChanges: true,
      );
      
      // Auto-update points based on difficulty if changed
      if (event.difficulty != null && event.difficulty != currentState.difficulty) {
        final suggestedPoints = _getSuggestedPointsForDifficulty(event.difficulty!);
        updatedState = updatedState.copyWith(basePoints: suggestedPoints);
      }
      
      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update quest points configuration
  Future<void> _onUpdatePointsConfig(
    UpdatePointsConfig event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedState = currentState.copyWith(
        basePoints: event.basePoints ?? currentState.basePoints,
        bonusPoints: event.bonusPoints ?? currentState.bonusPoints,
        hasUnsavedChanges: true,
      );
      
      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update quest scheduling information
  Future<void> _onUpdateScheduling(
    UpdateScheduling event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedState = currentState.copyWith(
        startDate: event.startDate ?? currentState.startDate,
        deadline: event.deadline ?? currentState.deadline,
        estimatedHours: event.estimatedHours ?? currentState.estimatedHours,
        hasUnsavedChanges: true,
      );
      
      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Add a task to the quest
  Future<void> _onAddTask(
    AddTask event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final newTask = QuestTaskItem(
        id: _uuid.v4(),
        title: event.taskTitle,
        description: event.taskDescription ?? '',
        order: currentState.tasks.length,
      );
      
      final updatedTasks = [...currentState.tasks, newTask];
      
      final updatedState = currentState.copyWith(
        tasks: updatedTasks,
        hasUnsavedChanges: true,
      );
      
      // Validate and emit
      _validateAndEmit(updatedState, emit);
    }
  }

  /// Update an existing task
  Future<void> _onUpdateTask(
    UpdateTask event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      if (event.taskIndex >= 0 && event.taskIndex < currentState.tasks.length) {
        final updatedTasks = [...currentState.tasks];
        final currentTask = updatedTasks[event.taskIndex];
        
        updatedTasks[event.taskIndex] = currentTask.copyWith(
          title: event.title ?? currentTask.title,
          description: event.description ?? currentTask.description,
          isCompleted: event.isCompleted ?? currentTask.isCompleted,
        );
        
        final updatedState = currentState.copyWith(
          tasks: updatedTasks,
          hasUnsavedChanges: true,
        );
        
        // Validate and emit
        _validateAndEmit(updatedState, emit);
      }
    }
  }

  /// Remove a task from the quest
  Future<void> _onRemoveTask(
    RemoveTask event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      if (event.taskIndex >= 0 && event.taskIndex < currentState.tasks.length) {
        final updatedTasks = [...currentState.tasks];
        updatedTasks.removeAt(event.taskIndex);
        
        // Reorder remaining tasks
        for (int i = 0; i < updatedTasks.length; i++) {
          updatedTasks[i] = updatedTasks[i].copyWith(order: i);
        }
        
        final updatedState = currentState.copyWith(
          tasks: updatedTasks,
          hasUnsavedChanges: true,
        );
        
        // Validate and emit
        _validateAndEmit(updatedState, emit);
      }
    }
  }

  /// Reorder tasks in the quest
  Future<void> _onReorderTasks(
    ReorderTasks event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      if (event.oldIndex >= 0 && 
          event.oldIndex < currentState.tasks.length &&
          event.newIndex >= 0 && 
          event.newIndex < currentState.tasks.length) {
        
        final updatedTasks = [...currentState.tasks];
        final task = updatedTasks.removeAt(event.oldIndex);
        updatedTasks.insert(event.newIndex, task);
        
        // Update order for all tasks
        for (int i = 0; i < updatedTasks.length; i++) {
          updatedTasks[i] = updatedTasks[i].copyWith(order: i);
        }
        
        final updatedState = currentState.copyWith(
          tasks: updatedTasks,
          hasUnsavedChanges: true,
        );
        
        emit(updatedState);
      }
    }
  }

  /// Add participants to the quest
  Future<void> _onAddParticipants(
    AddParticipants event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedParticipants = [
        ...currentState.participantIds,
        ...event.userIds.where((id) => !currentState.participantIds.contains(id)),
      ];
      
      final updatedState = currentState.copyWith(
        participantIds: updatedParticipants,
        hasUnsavedChanges: true,
      );
      
      emit(updatedState);
    }
  }

  /// Remove a participant from the quest
  Future<void> _onRemoveParticipant(
    RemoveParticipant event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedParticipants = currentState.participantIds
          .where((id) => id != event.userId)
          .toList();
      
      final updatedState = currentState.copyWith(
        participantIds: updatedParticipants,
        hasUnsavedChanges: true,
      );
      
      emit(updatedState);
    }
  }

  /// Update quest tags
  Future<void> _onUpdateTags(
    UpdateTags event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedState = currentState.copyWith(
        tags: event.tags,
        hasUnsavedChanges: true,
      );
      
      emit(updatedState);
    }
  }

  /// Update advanced settings
  Future<void> _onUpdateAdvancedSettings(
    UpdateAdvancedSettings event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedMetadata = Map<String, dynamic>.from(currentState.metadata);
      if (event.metadata != null) {
        updatedMetadata.addAll(event.metadata!);
      }
      
      final updatedState = currentState.copyWith(
        isPrivate: event.isPrivate ?? currentState.isPrivate,
        allowCollaboration: event.allowCollaboration ?? currentState.allowCollaboration,
        enableNotifications: event.enableNotifications ?? currentState.enableNotifications,
        metadata: updatedMetadata,
        hasUnsavedChanges: true,
      );
      
      emit(updatedState);
    }
  }

  /// Validate the entire form
  Future<void> _onValidateForm(
    ValidateForm event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      _validateAndEmit(currentState, emit);
    }
  }

  /// Save quest as draft
  Future<void> _onSaveDraft(
    SaveDraft event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      emit(const QuestCreationSavingDraft());
      
      try {
        // Try to save draft to API, fall back to local storage
        final draftId = currentState.currentDraftId ?? _uuid.v4();
        final draftData = {
          'id': draftId,
          'title': currentState.title,
          'description': currentState.description,
          'category': currentState.category.name,
          'difficulty': currentState.difficulty.name,
          'priority': currentState.priority.name,
          'basePoints': currentState.basePoints,
          'bonusPoints': currentState.bonusPoints,
          'totalPoints': currentState.totalPoints,
          'startDate': currentState.startDate?.toIso8601String(),
          'deadline': currentState.deadline?.toIso8601String(),
          'estimatedHours': currentState.estimatedHours,
          'isPrivate': currentState.isPrivate,
          'allowCollaboration': currentState.allowCollaboration,
          'enableNotifications': currentState.enableNotifications,
          'tags': currentState.tags,
          'tasks': currentState.tasks.map((task) => {
            'id': task.id,
            'title': task.title,
            'description': task.description,
            'isCompleted': task.isCompleted,
            'order': task.order,
          }).toList(),
          'participantIds': currentState.participantIds,
          'metadata': currentState.metadata,
          'savedAt': DateTime.now().toIso8601String(),
        };

        try {
          // Try to save to API first
          await _saveDraftToAPI(draftId, draftData);
        } catch (apiError) {
          // Fall back to local storage
          await _saveDraftToLocalStorage(draftId, draftData);
        }
        final savedAt = DateTime.now();
        
        final updatedState = currentState.copyWith(
          currentDraftId: draftId,
          hasUnsavedChanges: false,
          lastSaved: savedAt,
        );
        
        emit(QuestCreationDraftSaved(draftId: draftId, savedAt: savedAt));
        
        // Return to in-progress state
        emit(updatedState);
      } catch (e) {
        emit(QuestCreationError(message: 'Failed to save draft: $e'));
        emit(currentState);
      }
    }
  }

  /// Load a draft
  Future<void> _onLoadDraft(
    LoadDraft event,
    Emitter<QuestCreationState> emit,
  ) async {
    emit(const QuestCreationLoading(message: 'Loading draft...'));
    
    try {
      // Try to load draft from API, fall back to local storage
      Map<String, dynamic>? draftData;
      try {
        draftData = await _loadDraftFromAPI(event.draftId);
      } catch (apiError) {
        draftData = await _loadDraftFromLocalStorage(event.draftId);
      }

      if (draftData != null) {
        // Restore state from draft data
        final restoredState = _restoreStateFromDraftData(draftData);
        emit(restoredState);
      } else {
        // Draft not found, reset to initial state with draft ID
        const initialState = QuestCreationInProgress();
        final loadedState = await _loadDraftById(event.draftId, initialState);
        emit(loadedState);
      }
    } catch (e) {
      emit(QuestCreationError(message: 'Failed to load draft: $e'));
    }
  }

  /// Delete a draft
  Future<void> _onDeleteDraft(
    DeleteDraft event,
    Emitter<QuestCreationState> emit,
  ) async {
    try {
      // Try to delete draft from API, fall back to local storage
      try {
        await _deleteDraftFromAPI(event.draftId);
      } catch (apiError) {
        await _deleteDraftFromLocalStorage(event.draftId);
      }

      if (state is QuestCreationInProgress) {
        final currentState = state as QuestCreationInProgress;
        if (currentState.currentDraftId == event.draftId) {
          final updatedState = currentState.copyWith(
            currentDraftId: null,
            hasUnsavedChanges: true,
          );
          emit(updatedState);
        }
      }
    } catch (e) {
      emit(QuestCreationError(message: 'Failed to delete draft: $e'));
    }
  }

  /// Load quest templates
  Future<void> _onLoadTemplates(
    LoadTemplates event,
    Emitter<QuestCreationState> emit,
  ) async {
    emit(const QuestCreationLoadingTemplates());
    
    try {
      final templates = await _loadQuestTemplates();
      emit(QuestCreationTemplatesLoaded(templates: templates));
      
      if (state is QuestCreationInProgress) {
        final currentState = state as QuestCreationInProgress;
        emit(currentState.copyWith(availableTemplates: templates));
      }
    } catch (e) {
      emit(QuestCreationError(message: 'Failed to load templates: $e'));
    }
  }

  /// Apply a template to current quest
  Future<void> _onApplyTemplate(
    ApplyTemplate event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final template = currentState.availableTemplates.firstWhere(
        (t) => t.id == event.templateId,
        orElse: () => throw Exception('Template not found'),
      );
      
      final updatedState = _applyTemplateToState(currentState, template);
      
      emit(updatedState);
    }
  }

  /// Submit the quest for creation
  Future<void> _onSubmitQuest(
    SubmitQuest event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      // Final validation
      final validationResult = _validationService.validateQuest(currentState);
      if (!validationResult.isValid) {
        emit(QuestCreationValidationError(
          fieldErrors: validationResult.fieldErrors,
          generalErrors: validationResult.generalErrors,
        ));
        return;
      }
      
      emit(const QuestCreationSubmitting());
      
      try {
        // Create DTO from current state
        final questDto = currentState.toCreateQuestDto();
        
        // API call to create quest
        final response = await _apiService.post('/api/v1/quests', body: questDto.toJson());
        
        if (response.success && response.data != null) {
          // Parse response to get created quest
          final createdQuest = Quest.fromJson(response.data);
          
          emit(QuestCreationSuccess(createdQuest: createdQuest));
          
          // Clean up draft if exists
          if (currentState.currentDraftId != null) {
            add(DeleteDraft(currentState.currentDraftId!));
          }
        } else {
          emit(QuestCreationError(message: response.error ?? 'Failed to create quest'));
        }
        
      } catch (e) {
        emit(QuestCreationError(message: 'Failed to create quest: $e'));
        emit(currentState);
      }
    }
  }

  /// Reset form to initial state
  Future<void> _onResetForm(
    ResetForm event,
    Emitter<QuestCreationState> emit,
  ) async {
    const initialState = QuestCreationInProgress();
    emit(initialState);
  }

  /// Toggle preview mode
  Future<void> _onPreviewQuest(
    PreviewQuest event,
    Emitter<QuestCreationState> emit,
  ) async {
    if (state is QuestCreationInProgress) {
      final currentState = state as QuestCreationInProgress;
      
      final updatedState = currentState.copyWith(
        isPreviewMode: !currentState.isPreviewMode,
      );
      
      emit(updatedState);
    }
  }

  /// Helper method to validate state and emit
  void _validateAndEmit(
    QuestCreationInProgress state,
    Emitter<QuestCreationState> emit,
  ) {
    final validationResult = _validationService.validateQuest(state);
    final updatedState = state.copyWith(
      validationStatus: validationResult.isValid 
        ? FormValidationStatus.valid 
        : FormValidationStatus.invalid,
      fieldErrors: validationResult.fieldErrors,
      generalErrors: validationResult.generalErrors,
    );
    
    emit(updatedState);
  }

  /// Get suggested points based on difficulty
  int _getSuggestedPointsForDifficulty(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 50;
      case QuestDifficulty.intermediate:
        return 100;
      case QuestDifficulty.advanced:
        return 200;
      case QuestDifficulty.expert:
        return 350;
      case QuestDifficulty.master:
        return 500;
    }
  }

  /// Apply template to current state
  QuestCreationInProgress _applyTemplateToState(
    QuestCreationInProgress currentState,
    QuestTemplate template,
  ) {
    final tasks = template.taskTitles.asMap().entries.map((entry) {
      return QuestTaskItem(
        id: _uuid.v4(),
        title: entry.value,
        order: entry.key,
      );
    }).toList();

    return currentState.copyWith(
      title: template.title,
      description: template.description,
      category: template.category,
      difficulty: template.difficulty,
      priority: template.priority,
      basePoints: template.basePoints,
      tags: template.tags,
      tasks: tasks,
      hasUnsavedChanges: true,
    );
  }

  /// Load quest templates from API or return mock data
  Future<List<QuestTemplate>> _loadQuestTemplates() async {
    try {
      // Try to load from API first
      await Future.delayed(const Duration(milliseconds: 300));

      // Mock API response - in real app, this would be an HTTP call
      final mockApiResponse = {
        'success': true,
        'templates': [
          {
            'id': 'template_1',
            'title': 'Daily Coding Challenge',
            'description': 'Complete a coding challenge every day for a week',
            'category': 'learning',
            'difficulty': 'intermediate',
            'basePoints': 150,
            'tags': ['coding', 'daily', 'challenge'],
            'tasks': [
              {'title': 'Choose a coding platform', 'description': 'Select LeetCode, HackerRank, or similar'},
              {'title': 'Solve easy problem', 'description': 'Complete one easy-level problem'},
              {'title': 'Solve medium problem', 'description': 'Complete one medium-level problem'},
              {'title': 'Document solution', 'description': 'Write explanation of your approach'},
            ],
          },
          {
            'id': 'template_2',
            'title': 'Fitness Journey',
            'description': 'Start a 30-day fitness routine',
            'category': 'personal',
            'difficulty': 'beginner',
            'basePoints': 200,
            'tags': ['fitness', 'health', '30-day'],
            'tasks': [
              {'title': 'Set fitness goals', 'description': 'Define what you want to achieve'},
              {'title': 'Create workout plan', 'description': 'Plan your weekly exercise routine'},
              {'title': 'Track daily progress', 'description': 'Log workouts and measurements'},
              {'title': 'Weekly check-in', 'description': 'Review progress and adjust plan'},
            ],
          },
        ],
      };

      if (mockApiResponse['success'] == true) {
        final templatesData = mockApiResponse['templates'] as List<dynamic>;
        return templatesData.map((template) => QuestTemplate(
          id: template['id'] as String,
          title: template['title'] as String,
          description: template['description'] as String,
          category: QuestCategory.values.firstWhere(
            (e) => e.name == template['category'],
            orElse: () => QuestCategory.personal,
          ),
          difficulty: QuestDifficulty.values.firstWhere(
            (e) => e.name == template['difficulty'],
            orElse: () => QuestDifficulty.intermediate,
          ),
          priority: QuestPriority.medium, // Default priority
          basePoints: template['basePoints'] as int,
          taskTitles: (template['tasks'] as List<dynamic>).map((task) =>
            task['title'] as String
          ).toList(),
          tags: List<String>.from(template['tags'] as List<dynamic>),
        )).toList();
      }
    } catch (e) {
      // Fall back to hardcoded templates if API fails
    }

    // Fallback templates
    return const [
      QuestTemplate(
        id: 'template_fitness',
        title: 'Daily Fitness Challenge',
        description: 'Complete daily workout routines to build healthy habits',
        category: QuestCategory.health,
        difficulty: QuestDifficulty.intermediate,
        priority: QuestPriority.medium,
        basePoints: 150,
        taskTitles: ['Morning stretches', '30-minute workout', 'Track progress'],
        tags: ['fitness', 'health', 'daily'],
      ),
      QuestTemplate(
        id: 'template_learning',
        title: 'Skill Development Quest',
        description: 'Learn a new skill through structured practice',
        category: QuestCategory.learning,
        difficulty: QuestDifficulty.advanced,
        priority: QuestPriority.high,
        basePoints: 300,
        taskTitles: ['Research topic', 'Practice daily', 'Create project', 'Get feedback'],
        tags: ['learning', 'skill', 'development'],
      ),
    ];
  }

  /// Mock method to load draft by ID
  Future<QuestCreationInProgress> _loadDraftById(
    String draftId,
    QuestCreationInProgress initialState,
  ) async {
    try {
      // Try to load draft from API first
      final draftData = await _loadDraftFromAPI(draftId);

      if (draftData != null) {
        // Restore state from draft data
        return _restoreStateFromDraftData(draftData);
      } else {
        // Try local storage as fallback
        final localDraftData = await _loadDraftFromLocalStorage(draftId);

        if (localDraftData != null) {
          return _restoreStateFromDraftData(localDraftData);
        }
      }
    } catch (e) {
      // If loading fails, return initial state with draft ID
    }

    // Return initial state with draft ID if no draft found
    return initialState.copyWith(
      currentDraftId: draftId,
      hasUnsavedChanges: false,
      lastSaved: DateTime.now().subtract(const Duration(hours: 1)),
    );
  }

  /// Save draft to API
  Future<void> _saveDraftToAPI(String draftId, Map<String, dynamic> draftData) async {
    // In a real implementation, this would call the API service
    // For now, just simulate an API call
    await Future.delayed(const Duration(milliseconds: 200));

    // Simulate API failure occasionally for testing fallback
    if (DateTime.now().millisecond % 3 == 0) {
      throw Exception('API temporarily unavailable');
    }
  }

  /// Save draft to local storage
  Future<void> _saveDraftToLocalStorage(String draftId, Map<String, dynamic> draftData) async {
    try {
      // In a real implementation, this would use SharedPreferences or similar
      // For now, just simulate local storage
      await Future.delayed(const Duration(milliseconds: 100));

      // Store in memory for this session (in real app, use SharedPreferences)
      _localDrafts[draftId] = draftData;
    } catch (e) {
      throw Exception('Failed to save draft locally: $e');
    }
  }

  /// Load draft from API
  Future<Map<String, dynamic>?> _loadDraftFromAPI(String draftId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    // Simulate API failure occasionally
    if (DateTime.now().millisecond % 3 == 0) {
      throw Exception('API temporarily unavailable');
    }

    // Return null if not found
    return null;
  }

  /// Load draft from local storage
  Future<Map<String, dynamic>?> _loadDraftFromLocalStorage(String draftId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return _localDrafts[draftId];
  }

  /// Delete draft from API
  Future<void> _deleteDraftFromAPI(String draftId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    // Simulate API failure occasionally
    if (DateTime.now().millisecond % 3 == 0) {
      throw Exception('API temporarily unavailable');
    }
  }

  /// Delete draft from local storage
  Future<void> _deleteDraftFromLocalStorage(String draftId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    _localDrafts.remove(draftId);
  }

  /// Restore state from draft data
  QuestCreationInProgress _restoreStateFromDraftData(Map<String, dynamic> draftData) {
    return QuestCreationInProgress(
      title: draftData['title'] ?? '',
      description: draftData['description'] ?? '',
      category: QuestCategory.values.firstWhere(
        (e) => e.name == draftData['category'],
        orElse: () => QuestCategory.personal,
      ),
      difficulty: QuestDifficulty.values.firstWhere(
        (e) => e.name == draftData['difficulty'],
        orElse: () => QuestDifficulty.intermediate,
      ),
      priority: QuestPriority.values.firstWhere(
        (e) => e.name == draftData['priority'],
        orElse: () => QuestPriority.medium,
      ),
      basePoints: draftData['basePoints'] ?? 100,
      bonusPoints: draftData['bonusPoints'] ?? 0,
      totalPoints: draftData['totalPoints'] ?? 100,
      startDate: draftData['startDate'] != null
          ? DateTime.parse(draftData['startDate'])
          : null,
      deadline: draftData['deadline'] != null
          ? DateTime.parse(draftData['deadline'])
          : null,
      estimatedHours: draftData['estimatedHours'],
      isPrivate: draftData['isPrivate'] ?? false,
      allowCollaboration: draftData['allowCollaboration'] ?? true,
      enableNotifications: draftData['enableNotifications'] ?? true,
      tags: List<String>.from(draftData['tags'] ?? []),
      tasks: (draftData['tasks'] as List<dynamic>? ?? [])
          .map((taskData) => QuestTaskItem(
                id: taskData['id'],
                title: taskData['title'],
                description: taskData['description'] ?? '',
                isCompleted: taskData['isCompleted'] ?? false,
                order: taskData['order'],
              ))
          .toList(),
      participantIds: List<String>.from(draftData['participantIds'] ?? []),
      metadata: Map<String, dynamic>.from(draftData['metadata'] ?? {}),
      currentDraftId: draftData['id'],
      hasUnsavedChanges: false,
      lastSaved: draftData['savedAt'] != null
          ? DateTime.parse(draftData['savedAt'])
          : null,
    );
  }

  // In-memory storage for drafts (in real app, use SharedPreferences)
  static final Map<String, Map<String, dynamic>> _localDrafts = {};
}