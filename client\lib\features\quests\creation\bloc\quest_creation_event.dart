import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Base class for all Quest Creation events
abstract class Quest<PERSON>reation<PERSON>vent extends Equatable {
  const QuestCreationEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize the quest creation process
class InitializeQuestCreation extends QuestCreationEvent {
  final String? templateId;
  final String? draftId;

  const InitializeQuestCreation({
    this.templateId,
    this.draftId,
  });

  @override
  List<Object?> get props => [templateId, draftId];
}

/// Update a specific form field
class UpdateFormField extends QuestCreationEvent {
  final String fieldName;
  final dynamic value;

  const UpdateFormField({
    required this.fieldName,
    required this.value,
  });

  @override
  List<Object?> get props => [fieldName, value];
}

/// Update quest title
class UpdateTitle extends QuestCreationEvent {
  final String title;

  const UpdateTitle(this.title);

  @override
  List<Object?> get props => [title];
}

/// Update quest description
class UpdateDescription extends QuestCreationEvent {
  final String description;

  const UpdateDescription(this.description);

  @override
  List<Object?> get props => [description];
}

/// Update quest category
class UpdateCategory extends QuestCreationEvent {
  final QuestCategory category;

  const UpdateCategory(this.category);

  @override
  List<Object?> get props => [category];
}

/// Update quest basic information
class UpdateBasicInfo extends QuestCreationEvent {
  final String? title;
  final String? description;
  final QuestCategory? category;

  const UpdateBasicInfo({
    this.title,
    this.description,
    this.category,
  });

  @override
  List<Object?> get props => [title, description, category];
}

/// Update quest difficulty and priority
class UpdateDifficultyAndPriority extends QuestCreationEvent {
  final QuestDifficulty? difficulty;
  final QuestPriority? priority;

  const UpdateDifficultyAndPriority({
    this.difficulty,
    this.priority,
  });

  @override
  List<Object?> get props => [difficulty, priority];
}

/// Update quest points configuration
class UpdatePointsConfig extends QuestCreationEvent {
  final int? basePoints;
  final int? bonusPoints;

  const UpdatePointsConfig({
    this.basePoints,
    this.bonusPoints,
  });

  @override
  List<Object?> get props => [basePoints, bonusPoints];
}

/// Update quest scheduling information
class UpdateScheduling extends QuestCreationEvent {
  final DateTime? startDate;
  final DateTime? deadline;
  final int? estimatedHours;

  const UpdateScheduling({
    this.startDate,
    this.deadline,
    this.estimatedHours,
  });

  @override
  List<Object?> get props => [startDate, deadline, estimatedHours];
}

/// Add a task to the quest
class AddTask extends QuestCreationEvent {
  final String taskTitle;
  final String? taskDescription;

  const AddTask({
    required this.taskTitle,
    this.taskDescription,
  });

  @override
  List<Object?> get props => [taskTitle, taskDescription];
}

/// Update an existing task
class UpdateTask extends QuestCreationEvent {
  final int taskIndex;
  final String? title;
  final String? description;
  final bool? isCompleted;

  const UpdateTask({
    required this.taskIndex,
    this.title,
    this.description,
    this.isCompleted,
  });

  @override
  List<Object?> get props => [taskIndex, title, description, isCompleted];
}

/// Remove a task from the quest
class RemoveTask extends QuestCreationEvent {
  final int taskIndex;

  const RemoveTask(this.taskIndex);

  @override
  List<Object?> get props => [taskIndex];
}

/// Reorder tasks in the quest
class ReorderTasks extends QuestCreationEvent {
  final int oldIndex;
  final int newIndex;

  const ReorderTasks({
    required this.oldIndex,
    required this.newIndex,
  });

  @override
  List<Object?> get props => [oldIndex, newIndex];
}

/// Add participants to the quest
class AddParticipants extends QuestCreationEvent {
  final List<String> userIds;

  const AddParticipants(this.userIds);

  @override
  List<Object?> get props => [userIds];
}

/// Remove a participant from the quest
class RemoveParticipant extends QuestCreationEvent {
  final String userId;

  const RemoveParticipant(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Add tags to the quest
class UpdateTags extends QuestCreationEvent {
  final List<String> tags;

  const UpdateTags(this.tags);

  @override
  List<Object?> get props => [tags];
}

/// Update advanced settings
class UpdateAdvancedSettings extends QuestCreationEvent {
  final bool? isPrivate;
  final bool? allowCollaboration;
  final bool? enableNotifications;
  final Map<String, dynamic>? metadata;

  const UpdateAdvancedSettings({
    this.isPrivate,
    this.allowCollaboration,
    this.enableNotifications,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        isPrivate,
        allowCollaboration,
        enableNotifications,
        metadata,
      ];
}

/// Validate the entire form
class ValidateForm extends QuestCreationEvent {
  const ValidateForm();
}

/// Save the quest as a draft
class SaveDraft extends QuestCreationEvent {
  const SaveDraft();
}

/// Load a previously saved draft
class LoadDraft extends QuestCreationEvent {
  final String draftId;

  const LoadDraft(this.draftId);

  @override
  List<Object?> get props => [draftId];
}

/// Delete a saved draft
class DeleteDraft extends QuestCreationEvent {
  final String draftId;

  const DeleteDraft(this.draftId);

  @override
  List<Object?> get props => [draftId];
}

/// Submit the quest for creation
class SubmitQuest extends QuestCreationEvent {
  const SubmitQuest();
}

/// Reset the form to initial state
class ResetForm extends QuestCreationEvent {
  const ResetForm();
}

/// Load quest templates
class LoadTemplates extends QuestCreationEvent {
  const LoadTemplates();
}

/// Apply a template to the current quest
class ApplyTemplate extends QuestCreationEvent {
  final String templateId;

  const ApplyTemplate(this.templateId);

  @override
  List<Object?> get props => [templateId];
}

/// Preview the quest
class PreviewQuest extends QuestCreationEvent {
  const PreviewQuest();
}