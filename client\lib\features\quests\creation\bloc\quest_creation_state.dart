import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';

/// Form validation status
enum FormValidationStatus {
  initial,
  valid,
  invalid,
}

/// Quest creation progress step
enum QuestCreationStep {
  basicInfo,
  difficulty,
  tasks,
  scheduling,
  advanced,
  preview,
}

/// Task item for the quest creation form
class QuestTaskItem extends Equatable {
  final String id;
  final String title;
  final String description;
  final bool isCompleted;
  final int order;

  const QuestTaskItem({
    required this.id,
    required this.title,
    this.description = '',
    this.isCompleted = false,
    required this.order,
  });

  QuestTaskItem copyWith({
    String? id,
    String? title,
    String? description,
    bool? isCompleted,
    int? order,
  }) {
    return QuestTaskItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      order: order ?? this.order,
    );
  }

  @override
  List<Object?> get props => [id, title, description, isCompleted, order];
}

/// Quest template item
class QuestTemplate extends Equatable {
  final String id;
  final String title;
  final String description;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final QuestPriority priority;
  final int basePoints;
  final List<String> taskTitles;
  final List<String> tags;

  const QuestTemplate({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.priority,
    required this.basePoints,
    required this.taskTitles,
    required this.tags,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        category,
        difficulty,
        priority,
        basePoints,
        taskTitles,
        tags,
      ];
}

/// Base class for all Quest Creation states
abstract class QuestCreationState extends Equatable {
  const QuestCreationState();

  @override
  List<Object?> get props => [];
}

/// Initial state when quest creation is first loaded
class QuestCreationInitial extends QuestCreationState {
  const QuestCreationInitial();
}

/// State when the form is being loaded (draft or template)
class QuestCreationLoading extends QuestCreationState {
  final String message;

  const QuestCreationLoading({
    this.message = 'Loading...',
  });

  @override
  List<Object?> get props => [message];
}

/// Main state when user is actively creating/editing a quest
class QuestCreationInProgress extends QuestCreationState {
  // Basic Information
  final String title;
  final String description;
  final QuestCategory category;

  // Difficulty and Priority
  final QuestDifficulty difficulty;
  final QuestPriority priority;

  // Points Configuration
  final int basePoints;
  final int bonusPoints;
  final int totalPoints;

  // Scheduling
  final DateTime? startDate;
  final DateTime? deadline;
  final int? estimatedHours;

  // Tasks
  final List<QuestTaskItem> tasks;

  // Participants
  final List<String> participantIds;

  // Tags and Metadata
  final List<String> tags;
  final Map<String, dynamic> metadata;

  // Advanced Settings
  final bool isPrivate;
  final bool allowCollaboration;
  final bool enableNotifications;

  // Form State
  final FormValidationStatus validationStatus;
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;
  final QuestCreationStep currentStep;

  // Draft and Template State
  final String? currentDraftId;
  final List<QuestTemplate> availableTemplates;
  final bool hasUnsavedChanges;
  final DateTime? lastSaved;

  // UI State
  final bool isPreviewMode;
  final bool isSubmitting;

  const QuestCreationInProgress({
    // Basic Information
    this.title = '',
    this.description = '',
    this.category = QuestCategory.personal,

    // Difficulty and Priority
    this.difficulty = QuestDifficulty.intermediate,
    this.priority = QuestPriority.medium,

    // Points Configuration
    this.basePoints = 100,
    this.bonusPoints = 0,
    this.totalPoints = 100,

    // Scheduling
    this.startDate,
    this.deadline,
    this.estimatedHours,

    // Tasks
    this.tasks = const [],

    // Participants
    this.participantIds = const [],

    // Tags and Metadata
    this.tags = const [],
    this.metadata = const {},

    // Advanced Settings
    this.isPrivate = false,
    this.allowCollaboration = true,
    this.enableNotifications = true,

    // Form State
    this.validationStatus = FormValidationStatus.initial,
    this.fieldErrors = const {},
    this.generalErrors = const [],
    this.currentStep = QuestCreationStep.basicInfo,

    // Draft and Template State
    this.currentDraftId,
    this.availableTemplates = const [],
    this.hasUnsavedChanges = false,
    this.lastSaved,

    // UI State
    this.isPreviewMode = false,
    this.isSubmitting = false,
  });

  /// Check if the form has all required fields filled
  bool get isFormComplete {
    return title.trim().isNotEmpty &&
        description.trim().isNotEmpty &&
        basePoints > 0 &&
        validationStatus == FormValidationStatus.valid;
  }

  /// Get the total number of tasks
  int get taskCount => tasks.length;

  /// Get the number of completed tasks
  int get completedTaskCount =>
      tasks.where((task) => task.isCompleted).length;

  /// Calculate total points including difficulty multiplier
  int get calculatedTotalPoints {
    final difficultyMultiplier = _getDifficultyMultiplier(difficulty);
    return ((basePoints + bonusPoints) * difficultyMultiplier).round();
  }

  /// Get difficulty multiplier for points calculation
  double _getDifficultyMultiplier(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 1.0;
      case QuestDifficulty.intermediate:
        return 1.5;
      case QuestDifficulty.advanced:
        return 2.0;
      case QuestDifficulty.expert:
        return 2.5;
      case QuestDifficulty.master:
        return 3.0;
    }
  }

  /// Create a CreateQuestDto from current state
  CreateQuestDto toCreateQuestDto() {
    return CreateQuestDto(
      title: title,
      description: description,
      priority: priority,
      difficulty: difficulty,
      category: category,
      estimatedHours: estimatedHours,
      deadline: deadline,
      participantIds: participantIds,
      tags: tags,
      metadata: metadata.isNotEmpty ? metadata : null,
    );
  }

  QuestCreationInProgress copyWith({
    // Basic Information
    String? title,
    String? description,
    QuestCategory? category,

    // Difficulty and Priority
    QuestDifficulty? difficulty,
    QuestPriority? priority,

    // Points Configuration
    int? basePoints,
    int? bonusPoints,
    int? totalPoints,

    // Scheduling
    DateTime? startDate,
    DateTime? deadline,
    int? estimatedHours,

    // Tasks
    List<QuestTaskItem>? tasks,

    // Participants
    List<String>? participantIds,

    // Tags and Metadata
    List<String>? tags,
    Map<String, dynamic>? metadata,

    // Advanced Settings
    bool? isPrivate,
    bool? allowCollaboration,
    bool? enableNotifications,

    // Form State
    FormValidationStatus? validationStatus,
    Map<String, String>? fieldErrors,
    List<String>? generalErrors,
    QuestCreationStep? currentStep,

    // Draft and Template State
    String? currentDraftId,
    List<QuestTemplate>? availableTemplates,
    bool? hasUnsavedChanges,
    DateTime? lastSaved,

    // UI State
    bool? isPreviewMode,
    bool? isSubmitting,
  }) {
    return QuestCreationInProgress(
      // Basic Information
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,

      // Difficulty and Priority
      difficulty: difficulty ?? this.difficulty,
      priority: priority ?? this.priority,

      // Points Configuration
      basePoints: basePoints ?? this.basePoints,
      bonusPoints: bonusPoints ?? this.bonusPoints,
      totalPoints: totalPoints ?? this.totalPoints,

      // Scheduling
      startDate: startDate ?? this.startDate,
      deadline: deadline ?? this.deadline,
      estimatedHours: estimatedHours ?? this.estimatedHours,

      // Tasks
      tasks: tasks ?? this.tasks,

      // Participants
      participantIds: participantIds ?? this.participantIds,

      // Tags and Metadata
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,

      // Advanced Settings
      isPrivate: isPrivate ?? this.isPrivate,
      allowCollaboration: allowCollaboration ?? this.allowCollaboration,
      enableNotifications: enableNotifications ?? this.enableNotifications,

      // Form State
      validationStatus: validationStatus ?? this.validationStatus,
      fieldErrors: fieldErrors ?? this.fieldErrors,
      generalErrors: generalErrors ?? this.generalErrors,
      currentStep: currentStep ?? this.currentStep,

      // Draft and Template State
      currentDraftId: currentDraftId ?? this.currentDraftId,
      availableTemplates: availableTemplates ?? this.availableTemplates,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
      lastSaved: lastSaved ?? this.lastSaved,

      // UI State
      isPreviewMode: isPreviewMode ?? this.isPreviewMode,
      isSubmitting: isSubmitting ?? this.isSubmitting,
    );
  }

  @override
  List<Object?> get props => [
        // Basic Information
        title,
        description,
        category,

        // Difficulty and Priority
        difficulty,
        priority,

        // Points Configuration
        basePoints,
        bonusPoints,
        totalPoints,

        // Scheduling
        startDate,
        deadline,
        estimatedHours,

        // Tasks
        tasks,

        // Participants
        participantIds,

        // Tags and Metadata
        tags,
        metadata,

        // Advanced Settings
        isPrivate,
        allowCollaboration,
        enableNotifications,

        // Form State
        validationStatus,
        fieldErrors,
        generalErrors,
        currentStep,

        // Draft and Template State
        currentDraftId,
        availableTemplates,
        hasUnsavedChanges,
        lastSaved,

        // UI State
        isPreviewMode,
        isSubmitting,
      ];
}

/// State when validation fails
class QuestCreationValidationError extends QuestCreationState {
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;

  const QuestCreationValidationError({
    required this.fieldErrors,
    required this.generalErrors,
  });

  @override
  List<Object?> get props => [fieldErrors, generalErrors];
}

/// State when draft is being saved
class QuestCreationSavingDraft extends QuestCreationState {
  const QuestCreationSavingDraft();
}

/// State when draft has been saved successfully
class QuestCreationDraftSaved extends QuestCreationState {
  final String draftId;
  final DateTime savedAt;

  const QuestCreationDraftSaved({
    required this.draftId,
    required this.savedAt,
  });

  @override
  List<Object?> get props => [draftId, savedAt];
}

/// State when quest is being submitted
class QuestCreationSubmitting extends QuestCreationState {
  const QuestCreationSubmitting();
}

/// State when quest has been created successfully
class QuestCreationSuccess extends QuestCreationState {
  final Quest createdQuest;
  final String message;

  const QuestCreationSuccess({
    required this.createdQuest,
    this.message = 'Quest created successfully!',
  });

  @override
  List<Object?> get props => [createdQuest, message];
}

/// State when an error occurs during quest creation
class QuestCreationError extends QuestCreationState {
  final String message;
  final Exception? exception;

  const QuestCreationError({
    required this.message,
    this.exception,
  });

  @override
  List<Object?> get props => [message, exception];
}

/// State when templates are being loaded
class QuestCreationLoadingTemplates extends QuestCreationState {
  const QuestCreationLoadingTemplates();
}

/// State when templates have been loaded
class QuestCreationTemplatesLoaded extends QuestCreationState {
  final List<QuestTemplate> templates;

  const QuestCreationTemplatesLoaded({
    required this.templates,
  });

  @override
  List<Object?> get props => [templates];
}