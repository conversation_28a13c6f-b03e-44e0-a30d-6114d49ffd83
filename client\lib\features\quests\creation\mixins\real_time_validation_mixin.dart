import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../services/quest_validation_service.dart';

/// Mixin that provides real-time validation capabilities to form widgets
mixin RealTimeValidationMixin<T extends StatefulWidget> on State<T> {
  /// Timer for debouncing validation requests
  Timer? _validationTimer;
  
  /// Duration to wait before triggering validation
  Duration get validationDelay => const Duration(milliseconds: 500);
  
  /// Whether validation is enabled
  bool get validationEnabled => true;
  
  /// Current validation result
  QuestValidationResult? _currentValidationResult;
  
  /// Get the current validation result
  QuestValidationResult? get currentValidationResult => _currentValidationResult;
  
  /// Validation service instance
  QuestValidationService? _validationService;
  
  /// Get or create validation service
  QuestValidationService get validationService {
    _validationService ??= QuestValidationService();
    return _validationService!;
  }

  @override
  void dispose() {
    _validationTimer?.cancel();
    super.dispose();
  }

  /// Trigger real-time validation for the entire form
  void triggerValidation() {
    if (!validationEnabled || !mounted) return;
    
    _validationTimer?.cancel();
    _validationTimer = Timer(validationDelay, () {
      if (!mounted) return;
      
      final bloc = context.read<QuestCreationBloc>();
      final currentState = bloc.state;
      
      if (currentState is QuestCreationInProgress) {
        final result = validationService.validateQuest(currentState);
        setState(() {
          _currentValidationResult = result;
        });
        
        // Optionally trigger validation event in BLoC
        if (shouldTriggerBlocValidation(result)) {
          bloc.add(const ValidateForm());
        }
      }
    });
  }

  /// Trigger validation for a specific field
  void triggerFieldValidation(String fieldName) {
    if (!validationEnabled || !mounted) return;
    
    _validationTimer?.cancel();
    _validationTimer = Timer(validationDelay, () {
      if (!mounted) return;
      
      final bloc = context.read<QuestCreationBloc>();
      final currentState = bloc.state;
      
      if (currentState is QuestCreationInProgress) {
        final result = validationService.validateField(currentState, fieldName);
        setState(() {
          _currentValidationResult = result;
        });
        
        // Update the field error in the current validation result
        if (_currentValidationResult != null) {
          final updatedFieldErrors = Map<String, String>.from(_currentValidationResult!.fieldErrors);
          if (result.fieldErrors.containsKey(fieldName)) {
            updatedFieldErrors[fieldName] = result.fieldErrors[fieldName]!;
          } else {
            updatedFieldErrors.remove(fieldName);
          }
          
          setState(() {
            _currentValidationResult = QuestValidationResult(
              isValid: updatedFieldErrors.isEmpty && _currentValidationResult!.generalErrors.isEmpty,
              fieldErrors: updatedFieldErrors,
              generalErrors: _currentValidationResult!.generalErrors,
              warnings: _currentValidationResult!.warnings,
              severity: updatedFieldErrors.isNotEmpty ? ValidationSeverity.error : _currentValidationResult!.severity,
            );
          });
        }
      }
    });
  }

  /// Check if BLoC validation should be triggered
  bool shouldTriggerBlocValidation(QuestValidationResult result) {
    // Only trigger BLoC validation for critical errors or when form becomes valid
    return result.severity == ValidationSeverity.critical || result.isValid;
  }

  /// Get error for a specific field
  String? getFieldError(String fieldName) {
    return _currentValidationResult?.fieldErrors[fieldName];
  }

  /// Check if a field has an error
  bool hasFieldError(String fieldName) {
    return _currentValidationResult?.fieldErrors.containsKey(fieldName) ?? false;
  }

  /// Get all warnings
  List<String> getWarnings() {
    return _currentValidationResult?.warnings ?? [];
  }

  /// Check if form is valid
  bool get isFormValid {
    return _currentValidationResult?.isValid ?? false;
  }

  /// Get validation status for UI
  ValidationSeverity get validationSeverity {
    return _currentValidationResult?.severity ?? ValidationSeverity.none;
  }

  /// Create a text field with real-time validation
  Widget buildValidatedTextField({
    required TextEditingController controller,
    required String fieldName,
    required String labelText,
    FocusNode? focusNode,
    String? hintText,
    IconData? prefixIcon,
    bool obscureText = false,
    TextInputType? keyboardType,
    int? maxLines = 1,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
    ValueChanged<String>? onChanged,
    VoidCallback? onEditingComplete,
    String? Function(String?)? validator,
    bool enabled = true,
    bool showValidationIcon = true,
    bool showInlineError = true,
  }) {
    final hasError = hasFieldError(fieldName);
    final errorText = getFieldError(fieldName);
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          obscureText: obscureText,
          keyboardType: keyboardType,
          maxLines: maxLines,
          maxLength: maxLength,
          inputFormatters: inputFormatters,
          enabled: enabled,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
            suffixIcon: showValidationIcon ? _buildValidationIcon(fieldName, theme) : null,
            errorText: showInlineError ? errorText : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.0),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.0),
              borderSide: BorderSide(color: theme.colorScheme.error, width: 2.0),
            ),
            filled: true,
            fillColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          ),
          onChanged: (value) {
            onChanged?.call(value);
            triggerFieldValidation(fieldName);
          },
          onEditingComplete: () {
            onEditingComplete?.call();
            triggerFieldValidation(fieldName);
          },
          validator: validator,
        ),
        if (!showInlineError && hasError)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              errorText!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Create a dropdown field with real-time validation
  Widget buildValidatedDropdownField<V>({
    required V? value,
    required String fieldName,
    required String labelText,
    required List<DropdownMenuItem<V>> items,
    String? hintText,
    IconData? prefixIcon,
    ValueChanged<V?>? onChanged,
    bool enabled = true,
    bool showValidationIcon = true,
  }) {
    final errorText = getFieldError(fieldName);
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<V>(
          value: value,
          items: items,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
            suffixIcon: showValidationIcon ? _buildValidationIcon(fieldName, theme) : null,
            errorText: errorText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.0),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            filled: true,
            fillColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          ),
          onChanged: enabled ? (value) {
            onChanged?.call(value);
            triggerFieldValidation(fieldName);
          } : null,
        ),
      ],
    );
  }

  /// Build validation icon for form fields
  Widget? _buildValidationIcon(String fieldName, ThemeData theme) {
    final hasError = hasFieldError(fieldName);
    
    if (hasError) {
      return Icon(
        Icons.error_outline_rounded,
        color: theme.colorScheme.error,
        size: 20,
      );
    }
    
    // Only show success icon if the field has been validated and is valid
    if (_currentValidationResult != null && !_currentValidationResult!.fieldErrors.containsKey(fieldName)) {
      return Icon(
        Icons.check_circle_outline_rounded,
        color: theme.colorScheme.primary,
        size: 20,
      );
    }
    
    return null;
  }

  /// Create a validation listener widget that responds to BLoC state changes
  Widget buildValidationListener({required Widget child}) {
    return BlocListener<QuestCreationBloc, QuestCreationState>(
      listener: (context, state) {
        if (state is QuestCreationInProgress) {
          // Update validation result when BLoC state changes
          final result = validationService.validateQuest(state);
          if (mounted) {
            setState(() {
              _currentValidationResult = result;
            });
          }
        }
      },
      child: child,
    );
  }

  /// Show validation snackbar for immediate feedback
  void showValidationSnackbar(BuildContext context, {
    String? message,
    bool isError = true,
    Duration duration = const Duration(seconds: 3),
  }) {
    final theme = Theme.of(context);
    final effectiveMessage = message ?? 
        (isError ? 'Please fix the errors above' : 'Form validated successfully');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline_rounded : Icons.check_circle_outline_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                effectiveMessage,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? theme.colorScheme.error : theme.colorScheme.primary,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  /// Scroll to first error field
  void scrollToFirstError(GlobalKey<FormState> formKey) {
    if (_currentValidationResult?.fieldErrors.isNotEmpty ?? false) {
      final firstErrorField = _currentValidationResult!.fieldErrors.keys.first;
      // This would require additional setup to scroll to specific fields
      // Implementation depends on your form structure
      debugPrint('Should scroll to field: $firstErrorField');
    }
  }
}

/// Extension methods for easy validation integration
extension ValidationExtensions on BuildContext {
  /// Trigger form validation
  void triggerFormValidation() {
    final state = findAncestorStateOfType<State<StatefulWidget>>();
    if (state is State && state is RealTimeValidationMixin) {
      (state).triggerValidation();
    }
  }
  
  /// Show validation feedback
  void showValidationFeedback({String? message, bool isError = true}) {
    final state = findAncestorStateOfType<State<StatefulWidget>>();
    if (state is State && state is RealTimeValidationMixin) {
      (state).showValidationSnackbar(
        this,
        message: message,
        isError: isError,
      );
    }
  }
}