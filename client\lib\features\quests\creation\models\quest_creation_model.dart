import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_state.dart';
import 'quest_creation_validation.dart';

part 'quest_creation_model.g.dart';

/// Quest creation draft for persistence
@JsonSerializable()
class QuestCreationDraft extends Equatable {
  final String id;
  final String title;
  final String description;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final QuestPriority priority;
  final int basePoints;
  final int bonusPoints;
  final DateTime? startDate;
  final DateTime? deadline;
  final int? estimatedHours;
  final List<QuestTaskItemData> tasks;
  final List<String> participantIds;
  final List<String> tags;
  final Map<String, dynamic> metadata;
  final bool isPrivate;
  final bool allowCollaboration;
  final bool enableNotifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  const QuestCreationDraft({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.priority,
    required this.basePoints,
    required this.bonusPoints,
    this.startDate,
    this.deadline,
    this.estimatedHours,
    required this.tasks,
    required this.participantIds,
    required this.tags,
    required this.metadata,
    required this.isPrivate,
    required this.allowCollaboration,
    required this.enableNotifications,
    required this.createdAt,
    required this.updatedAt,
  });

  factory QuestCreationDraft.fromJson(Map<String, dynamic> json) =>
      _$QuestCreationDraftFromJson(json);

  Map<String, dynamic> toJson() => _$QuestCreationDraftToJson(this);

  /// Create from BLoC state
  factory QuestCreationDraft.fromState(
    QuestCreationInProgress state, {
    required String id,
  }) {
    final now = DateTime.now();
    
    return QuestCreationDraft(
      id: id,
      title: state.title,
      description: state.description,
      category: state.category,
      difficulty: state.difficulty,
      priority: state.priority,
      basePoints: state.basePoints,
      bonusPoints: state.bonusPoints,
      startDate: state.startDate,
      deadline: state.deadline,
      estimatedHours: state.estimatedHours,
      tasks: state.tasks.map((task) => QuestTaskItemData.fromTaskItem(task)).toList(),
      participantIds: state.participantIds,
      tags: state.tags,
      metadata: state.metadata,
      isPrivate: state.isPrivate,
      allowCollaboration: state.allowCollaboration,
      enableNotifications: state.enableNotifications,
      createdAt: state.lastSaved ?? now,
      updatedAt: now,
    );
  }

  /// Convert to BLoC state
  QuestCreationInProgress toState() {
    return QuestCreationInProgress(
      title: title,
      description: description,
      category: category,
      difficulty: difficulty,
      priority: priority,
      basePoints: basePoints,
      bonusPoints: bonusPoints,
      startDate: startDate,
      deadline: deadline,
      estimatedHours: estimatedHours,
      tasks: tasks.map((task) => task.toTaskItem()).toList(),
      participantIds: participantIds,
      tags: tags,
      metadata: metadata,
      isPrivate: isPrivate,
      allowCollaboration: allowCollaboration,
      enableNotifications: enableNotifications,
      currentDraftId: id,
      hasUnsavedChanges: false,
      lastSaved: updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        category,
        difficulty,
        priority,
        basePoints,
        bonusPoints,
        startDate,
        deadline,
        estimatedHours,
        tasks,
        participantIds,
        tags,
        metadata,
        isPrivate,
        allowCollaboration,
        enableNotifications,
        createdAt,
        updatedAt,
      ];
}

/// Serializable task item for drafts
@JsonSerializable()
class QuestTaskItemData extends Equatable {
  final String id;
  final String title;
  final String description;
  final bool isCompleted;
  final int order;

  const QuestTaskItemData({
    required this.id,
    required this.title,
    required this.description,
    required this.isCompleted,
    required this.order,
  });

  factory QuestTaskItemData.fromJson(Map<String, dynamic> json) =>
      _$QuestTaskItemDataFromJson(json);

  Map<String, dynamic> toJson() => _$QuestTaskItemDataToJson(this);

  /// Create from BLoC task item
  factory QuestTaskItemData.fromTaskItem(QuestTaskItem task) {
    return QuestTaskItemData(
      id: task.id,
      title: task.title,
      description: task.description,
      isCompleted: task.isCompleted,
      order: task.order,
    );
  }

  /// Convert to BLoC task item
  QuestTaskItem toTaskItem() {
    return QuestTaskItem(
      id: id,
      title: title,
      description: description,
      isCompleted: isCompleted,
      order: order,
    );
  }

  @override
  List<Object?> get props => [id, title, description, isCompleted, order];
}

/// Quest creation form model with validation
class QuestCreationFormModel extends Equatable {
  final QuestFormValidation validation;
  final QuestCategory selectedCategory;
  final QuestDifficulty selectedDifficulty;
  final QuestPriority selectedPriority;
  final List<QuestTaskItem> tasks;
  final List<String> participants;
  final bool isSubmitting;
  final String? submitError;

  const QuestCreationFormModel({
    required this.validation,
    this.selectedCategory = QuestCategory.personal,
    this.selectedDifficulty = QuestDifficulty.intermediate,
    this.selectedPriority = QuestPriority.medium,
    this.tasks = const [],
    this.participants = const [],
    this.isSubmitting = false,
    this.submitError,
  });

  /// Check if form can be submitted
  bool get canSubmit {
    return validation.isValid && 
           !isSubmitting && 
           validation.title.value.isNotEmpty &&
           validation.description.value.isNotEmpty;
  }

  /// Get suggested points based on difficulty
  int get suggestedPoints {
    switch (selectedDifficulty) {
      case QuestDifficulty.beginner:
        return 50;
      case QuestDifficulty.intermediate:
        return 100;
      case QuestDifficulty.advanced:
        return 200;
      case QuestDifficulty.expert:
        return 350;
      case QuestDifficulty.master:
        return 500;
    }
  }

  /// Get difficulty color for UI
  String get difficultyColor {
    switch (selectedDifficulty) {
      case QuestDifficulty.beginner:
        return '#4CAF50'; // Green
      case QuestDifficulty.intermediate:
        return '#FF9800'; // Orange
      case QuestDifficulty.advanced:
        return '#F44336'; // Red
      case QuestDifficulty.expert:
        return '#9C27B0'; // Purple
      case QuestDifficulty.master:
        return '#212121'; // Black
    }
  }

  /// Get category icon for UI
  String get categoryIcon {
    switch (selectedCategory) {
      case QuestCategory.health:
        return 'fitness_center';
      case QuestCategory.learning:
        return 'school';
      case QuestCategory.productivity:
        return 'work';
      case QuestCategory.creative:
        return 'palette';
      case QuestCategory.social:
        return 'people';
      case QuestCategory.work:
        return 'business';
      case QuestCategory.personal:
        return 'person';
      case QuestCategory.other:
        return 'more_horiz';
    }
  }

  /// Get completion percentage
  double get taskCompletionPercentage {
    if (tasks.isEmpty) return 0.0;
    final completedTasks = tasks.where((task) => task.isCompleted).length;
    return completedTasks / tasks.length;
  }

  /// Create from BLoC state
  factory QuestCreationFormModel.fromState(QuestCreationInProgress state) {
    return QuestCreationFormModel(
      validation: QuestFormValidation(
        title: QuestTitle.dirty(state.title),
        description: QuestDescription.dirty(state.description),
        basePoints: QuestPoints.dirty(state.basePoints),
        bonusPoints: QuestPoints.dirty(state.bonusPoints),
        estimatedHours: EstimatedHours.dirty(state.estimatedHours),
        tags: QuestTags.dirty(state.tags),
        deadline: QuestDeadline.dirty(state.deadline),
      ),
      selectedCategory: state.category,
      selectedDifficulty: state.difficulty,
      selectedPriority: state.priority,
      tasks: state.tasks,
      participants: state.participantIds,
      isSubmitting: state.isSubmitting,
    );
  }

  QuestCreationFormModel copyWith({
    QuestFormValidation? validation,
    QuestCategory? selectedCategory,
    QuestDifficulty? selectedDifficulty,
    QuestPriority? selectedPriority,
    List<QuestTaskItem>? tasks,
    List<String>? participants,
    bool? isSubmitting,
    String? submitError,
  }) {
    return QuestCreationFormModel(
      validation: validation ?? this.validation,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedDifficulty: selectedDifficulty ?? this.selectedDifficulty,
      selectedPriority: selectedPriority ?? this.selectedPriority,
      tasks: tasks ?? this.tasks,
      participants: participants ?? this.participants,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      submitError: submitError ?? this.submitError,
    );
  }

  @override
  List<Object?> get props => [
        validation,
        selectedCategory,
        selectedDifficulty,
        selectedPriority,
        tasks,
        participants,
        isSubmitting,
        submitError,
      ];
}

/// Quest template model
@JsonSerializable()
class QuestTemplateModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final QuestPriority priority;
  final int basePoints;
  final List<String> taskTitles;
  final List<String> tags;
  final String? iconUrl;
  final bool isPopular;
  final int usageCount;

  const QuestTemplateModel({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.priority,
    required this.basePoints,
    required this.taskTitles,
    required this.tags,
    this.iconUrl,
    this.isPopular = false,
    this.usageCount = 0,
  });

  factory QuestTemplateModel.fromJson(Map<String, dynamic> json) =>
      _$QuestTemplateModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestTemplateModelToJson(this);

  /// Convert to BLoC template
  QuestTemplate toTemplate() {
    return QuestTemplate(
      id: id,
      title: name,
      description: description,
      category: category,
      difficulty: difficulty,
      priority: priority,
      basePoints: basePoints,
      taskTitles: taskTitles,
      tags: tags,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        difficulty,
        priority,
        basePoints,
        taskTitles,
        tags,
        iconUrl,
        isPopular,
        usageCount,
      ];
}

/// User preferences for quest creation
@JsonSerializable()
class QuestCreationPreferences extends Equatable {
  final QuestCategory defaultCategory;
  final QuestDifficulty defaultDifficulty;
  final QuestPriority defaultPriority;
  final int defaultPoints;
  final bool enableAutoSave;
  final int autoSaveIntervalSeconds;
  final bool showAdvancedOptions;
  final List<String> favoriteTemplates;
  final List<String> recentTags;

  const QuestCreationPreferences({
    this.defaultCategory = QuestCategory.personal,
    this.defaultDifficulty = QuestDifficulty.intermediate,
    this.defaultPriority = QuestPriority.medium,
    this.defaultPoints = 100,
    this.enableAutoSave = true,
    this.autoSaveIntervalSeconds = 30,
    this.showAdvancedOptions = false,
    this.favoriteTemplates = const [],
    this.recentTags = const [],
  });

  factory QuestCreationPreferences.fromJson(Map<String, dynamic> json) =>
      _$QuestCreationPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$QuestCreationPreferencesToJson(this);

  QuestCreationPreferences copyWith({
    QuestCategory? defaultCategory,
    QuestDifficulty? defaultDifficulty,
    QuestPriority? defaultPriority,
    int? defaultPoints,
    bool? enableAutoSave,
    int? autoSaveIntervalSeconds,
    bool? showAdvancedOptions,
    List<String>? favoriteTemplates,
    List<String>? recentTags,
  }) {
    return QuestCreationPreferences(
      defaultCategory: defaultCategory ?? this.defaultCategory,
      defaultDifficulty: defaultDifficulty ?? this.defaultDifficulty,
      defaultPriority: defaultPriority ?? this.defaultPriority,
      defaultPoints: defaultPoints ?? this.defaultPoints,
      enableAutoSave: enableAutoSave ?? this.enableAutoSave,
      autoSaveIntervalSeconds: autoSaveIntervalSeconds ?? this.autoSaveIntervalSeconds,
      showAdvancedOptions: showAdvancedOptions ?? this.showAdvancedOptions,
      favoriteTemplates: favoriteTemplates ?? this.favoriteTemplates,
      recentTags: recentTags ?? this.recentTags,
    );
  }

  @override
  List<Object?> get props => [
        defaultCategory,
        defaultDifficulty,
        defaultPriority,
        defaultPoints,
        enableAutoSave,
        autoSaveIntervalSeconds,
        showAdvancedOptions,
        favoriteTemplates,
        recentTags,
      ];
}