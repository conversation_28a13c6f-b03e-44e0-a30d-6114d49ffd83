// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quest_creation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestCreationDraft _$QuestCreationDraftFromJson(Map<String, dynamic> json) =>
    QuestCreationDraft(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      priority: $enumDecode(_$Quest<PERSON>riority<PERSON>numMap, json['priority']),
      basePoints: (json['basePoints'] as num).toInt(),
      bonusPoints: (json['bonusPoints'] as num).toInt(),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      estimatedHours: (json['estimatedHours'] as num?)?.toInt(),
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => QuestTaskItemData.fromJson(e as Map<String, dynamic>))
          .toList(),
      participantIds: (json['participantIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>,
      isPrivate: json['isPrivate'] as bool,
      allowCollaboration: json['allowCollaboration'] as bool,
      enableNotifications: json['enableNotifications'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$QuestCreationDraftToJson(QuestCreationDraft instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'priority': _$QuestPriorityEnumMap[instance.priority]!,
      'basePoints': instance.basePoints,
      'bonusPoints': instance.bonusPoints,
      'startDate': instance.startDate?.toIso8601String(),
      'deadline': instance.deadline?.toIso8601String(),
      'estimatedHours': instance.estimatedHours,
      'tasks': instance.tasks,
      'participantIds': instance.participantIds,
      'tags': instance.tags,
      'metadata': instance.metadata,
      'isPrivate': instance.isPrivate,
      'allowCollaboration': instance.allowCollaboration,
      'enableNotifications': instance.enableNotifications,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$QuestCategoryEnumMap = {
  QuestCategory.personal: 'personal',
  QuestCategory.work: 'work',
  QuestCategory.learning: 'learning',
  QuestCategory.health: 'health',
  QuestCategory.social: 'social',
  QuestCategory.creative: 'creative',
  QuestCategory.productivity: 'productivity',
  QuestCategory.other: 'other',
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.beginner: 'beginner',
  QuestDifficulty.intermediate: 'intermediate',
  QuestDifficulty.advanced: 'advanced',
  QuestDifficulty.expert: 'expert',
  QuestDifficulty.master: 'master',
};

const _$QuestPriorityEnumMap = {
  QuestPriority.low: 'low',
  QuestPriority.medium: 'medium',
  QuestPriority.high: 'high',
  QuestPriority.urgent: 'urgent',
};

QuestTaskItemData _$QuestTaskItemDataFromJson(Map<String, dynamic> json) =>
    QuestTaskItemData(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      isCompleted: json['isCompleted'] as bool,
      order: (json['order'] as num).toInt(),
    );

Map<String, dynamic> _$QuestTaskItemDataToJson(QuestTaskItemData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'isCompleted': instance.isCompleted,
      'order': instance.order,
    };

QuestTemplateModel _$QuestTemplateModelFromJson(Map<String, dynamic> json) =>
    QuestTemplateModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      priority: $enumDecode(_$QuestPriorityEnumMap, json['priority']),
      basePoints: (json['basePoints'] as num).toInt(),
      taskTitles: (json['taskTitles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      iconUrl: json['iconUrl'] as String?,
      isPopular: json['isPopular'] as bool? ?? false,
      usageCount: (json['usageCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$QuestTemplateModelToJson(QuestTemplateModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'priority': _$QuestPriorityEnumMap[instance.priority]!,
      'basePoints': instance.basePoints,
      'taskTitles': instance.taskTitles,
      'tags': instance.tags,
      'iconUrl': instance.iconUrl,
      'isPopular': instance.isPopular,
      'usageCount': instance.usageCount,
    };

QuestCreationPreferences _$QuestCreationPreferencesFromJson(
  Map<String, dynamic> json,
) => QuestCreationPreferences(
  defaultCategory:
      $enumDecodeNullable(_$QuestCategoryEnumMap, json['defaultCategory']) ??
      QuestCategory.personal,
  defaultDifficulty:
      $enumDecodeNullable(
        _$QuestDifficultyEnumMap,
        json['defaultDifficulty'],
      ) ??
      QuestDifficulty.intermediate,
  defaultPriority:
      $enumDecodeNullable(_$QuestPriorityEnumMap, json['defaultPriority']) ??
      QuestPriority.medium,
  defaultPoints: (json['defaultPoints'] as num?)?.toInt() ?? 100,
  enableAutoSave: json['enableAutoSave'] as bool? ?? true,
  autoSaveIntervalSeconds:
      (json['autoSaveIntervalSeconds'] as num?)?.toInt() ?? 30,
  showAdvancedOptions: json['showAdvancedOptions'] as bool? ?? false,
  favoriteTemplates:
      (json['favoriteTemplates'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  recentTags:
      (json['recentTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$QuestCreationPreferencesToJson(
  QuestCreationPreferences instance,
) => <String, dynamic>{
  'defaultCategory': _$QuestCategoryEnumMap[instance.defaultCategory]!,
  'defaultDifficulty': _$QuestDifficultyEnumMap[instance.defaultDifficulty]!,
  'defaultPriority': _$QuestPriorityEnumMap[instance.defaultPriority]!,
  'defaultPoints': instance.defaultPoints,
  'enableAutoSave': instance.enableAutoSave,
  'autoSaveIntervalSeconds': instance.autoSaveIntervalSeconds,
  'showAdvancedOptions': instance.showAdvancedOptions,
  'favoriteTemplates': instance.favoriteTemplates,
  'recentTags': instance.recentTags,
};
