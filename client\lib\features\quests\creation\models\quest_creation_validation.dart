import 'package:formz/formz.dart';

/// Quest title validation errors
enum QuestTitleValidationError {
  empty,
  tooShort,
  tooLong,
  invalidCharacters,
}

/// Quest title input with validation
class QuestTitle extends FormzInput<String, QuestTitleValidationError> {
  const QuestTitle.pure([super.value = '']) : super.pure();
  const QuestTitle.dirty([super.value = '']) : super.dirty();

  static const int minLength = 3;
  static const int maxLength = 100;
  static final RegExp validCharactersRegExp = RegExp(r'^[a-zA-Z0-9\s\-_.!?()]+$');

  @override
  QuestTitleValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return QuestTitleValidationError.empty;
    }
    
    if (trimmed.length < minLength) {
      return QuestTitleValidationError.tooShort;
    }
    
    if (trimmed.length > maxLength) {
      return QuestTitleValidationError.tooLong;
    }
    
    if (!validCharactersRegExp.hasMatch(trimmed)) {
      return QuestTitleValidationError.invalidCharacters;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case QuestTitleValidationError.empty:
        return 'Quest title is required';
      case QuestTitleValidationError.tooShort:
        return 'Title must be at least $minLength characters';
      case QuestTitleValidationError.tooLong:
        return 'Title must be less than $maxLength characters';
      case QuestTitleValidationError.invalidCharacters:
        return 'Title contains invalid characters';
      case null:
        return null;
    }
  }
}

/// Quest description validation errors
enum QuestDescriptionValidationError {
  empty,
  tooShort,
  tooLong,
}

/// Quest description input with validation
class QuestDescription extends FormzInput<String, QuestDescriptionValidationError> {
  const QuestDescription.pure([super.value = '']) : super.pure();
  const QuestDescription.dirty([super.value = '']) : super.dirty();

  static const int minLength = 10;
  static const int maxLength = 1000;

  @override
  QuestDescriptionValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return QuestDescriptionValidationError.empty;
    }
    
    if (trimmed.length < minLength) {
      return QuestDescriptionValidationError.tooShort;
    }
    
    if (trimmed.length > maxLength) {
      return QuestDescriptionValidationError.tooLong;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case QuestDescriptionValidationError.empty:
        return 'Quest description is required';
      case QuestDescriptionValidationError.tooShort:
        return 'Description must be at least $minLength characters';
      case QuestDescriptionValidationError.tooLong:
        return 'Description must be less than $maxLength characters';
      case null:
        return null;
    }
  }
}

/// Quest points validation errors
enum QuestPointsValidationError {
  empty,
  invalid,
  tooLow,
  tooHigh,
}

/// Extension to add message getter to QuestPointsValidationError
extension QuestPointsValidationErrorExtension on QuestPointsValidationError {
  String get message {
    switch (this) {
      case QuestPointsValidationError.empty:
        return 'Points cannot be empty';
      case QuestPointsValidationError.invalid:
        return 'Invalid points value';
      case QuestPointsValidationError.tooLow:
        return 'Points must be at least 1';
      case QuestPointsValidationError.tooHigh:
        return 'Points cannot exceed 10,000';
    }
  }
}

/// Quest base points input with validation
class QuestPoints extends FormzInput<int, QuestPointsValidationError> {
  const QuestPoints.pure([super.value = 0]) : super.pure();
  const QuestPoints.dirty([super.value = 0]) : super.dirty();

  static const int minPoints = 1;
  static const int maxPoints = 10000;

  @override
  QuestPointsValidationError? validator(int value) {
    if (value <= 0) {
      return QuestPointsValidationError.tooLow;
    }
    
    if (value > maxPoints) {
      return QuestPointsValidationError.tooHigh;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case QuestPointsValidationError.empty:
        return 'Points value is required';
      case QuestPointsValidationError.invalid:
        return 'Please enter a valid number';
      case QuestPointsValidationError.tooLow:
        return 'Points must be at least $minPoints';
      case QuestPointsValidationError.tooHigh:
        return 'Points cannot exceed $maxPoints';
      case null:
        return null;
    }
  }
}

/// Estimated hours validation errors
enum EstimatedHoursValidationError {
  invalid,
  tooLow,
  tooHigh,
}

/// Estimated hours input with validation
class EstimatedHours extends FormzInput<int?, EstimatedHoursValidationError> {
  const EstimatedHours.pure([super.value]) : super.pure();
  const EstimatedHours.dirty([super.value]) : super.dirty();

  static const int minHours = 1;
  static const int maxHours = 1000;

  @override
  EstimatedHoursValidationError? validator(int? value) {
    // Null/empty is allowed for optional field
    if (value == null) return null;
    
    if (value < minHours) {
      return EstimatedHoursValidationError.tooLow;
    }
    
    if (value > maxHours) {
      return EstimatedHoursValidationError.tooHigh;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case EstimatedHoursValidationError.invalid:
        return 'Please enter a valid number of hours';
      case EstimatedHoursValidationError.tooLow:
        return 'Estimated hours must be at least $minHours';
      case EstimatedHoursValidationError.tooHigh:
        return 'Estimated hours cannot exceed $maxHours';
      case null:
        return null;
    }
  }
}

/// Quest estimated hours validation errors for double values
enum QuestEstimatedHoursValidationError {
  invalid,
  tooLow,
  tooHigh,
}

/// Extension to add message getter to QuestEstimatedHoursValidationError
extension QuestEstimatedHoursValidationErrorExtension on QuestEstimatedHoursValidationError {
  String get message {
    switch (this) {
      case QuestEstimatedHoursValidationError.invalid:
        return 'Invalid time format';
      case QuestEstimatedHoursValidationError.tooLow:
        return 'Time must be at least 0.5 hours';
      case QuestEstimatedHoursValidationError.tooHigh:
        return 'Time cannot exceed 1000 hours';
    }
  }
}

/// Quest estimated hours input with validation for double values
class QuestEstimatedHours extends FormzInput<String, QuestEstimatedHoursValidationError> {
  const QuestEstimatedHours.pure([super.value = '']) : super.pure();
  const QuestEstimatedHours.dirty([super.value = '']) : super.dirty();

  static const double minHours = 0.5;
  static const double maxHours = 1000.0;

  @override
  QuestEstimatedHoursValidationError? validator(String value) {
    if (value.trim().isEmpty) return null; // Empty is allowed
    
    final doubleValue = double.tryParse(value);
    if (doubleValue == null) {
      return QuestEstimatedHoursValidationError.invalid;
    }
    
    if (doubleValue < minHours) {
      return QuestEstimatedHoursValidationError.tooLow;
    }
    
    if (doubleValue > maxHours) {
      return QuestEstimatedHoursValidationError.tooHigh;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case QuestEstimatedHoursValidationError.invalid:
        return 'Please enter a valid number of hours';
      case QuestEstimatedHoursValidationError.tooLow:
        return 'Estimated hours must be at least ${minHours.toString()} hours';
      case QuestEstimatedHoursValidationError.tooHigh:
        return 'Estimated hours cannot exceed ${maxHours.round()} hours';
      case null:
        return null;
    }
  }
}

/// Task title validation errors
enum TaskTitleValidationError {
  empty,
  tooShort,
  tooLong,
  invalidCharacters,
}

/// Task title input with validation
class TaskTitle extends FormzInput<String, TaskTitleValidationError> {
  const TaskTitle.pure([super.value = '']) : super.pure();
  const TaskTitle.dirty([super.value = '']) : super.dirty();

  static const int minLength = 2;
  static const int maxLength = 200;
  static final RegExp validCharactersRegExp = RegExp(r'^[a-zA-Z0-9\s\-_.!?()]+$');

  @override
  TaskTitleValidationError? validator(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      return TaskTitleValidationError.empty;
    }
    
    if (trimmed.length < minLength) {
      return TaskTitleValidationError.tooShort;
    }
    
    if (trimmed.length > maxLength) {
      return TaskTitleValidationError.tooLong;
    }
    
    if (!validCharactersRegExp.hasMatch(trimmed)) {
      return TaskTitleValidationError.invalidCharacters;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case TaskTitleValidationError.empty:
        return 'Task title is required';
      case TaskTitleValidationError.tooShort:
        return 'Task title must be at least $minLength characters';
      case TaskTitleValidationError.tooLong:
        return 'Task title must be less than $maxLength characters';
      case TaskTitleValidationError.invalidCharacters:
        return 'Task title contains invalid characters';
      case null:
        return null;
    }
  }
}

/// Tags validation errors
enum TagsValidationError {
  tooMany,
  invalidTag,
  duplicateTag,
}

/// Tags input with validation
class QuestTags extends FormzInput<List<String>, TagsValidationError> {
  const QuestTags.pure([super.value = const []]) : super.pure();
  const QuestTags.dirty([super.value = const []]) : super.dirty();

  static const int maxTags = 10;
  static const int maxTagLength = 30;
  static final RegExp validTagRegExp = RegExp(r'^[a-zA-Z0-9\-_]+$');

  @override
  TagsValidationError? validator(List<String> value) {
    if (value.length > maxTags) {
      return TagsValidationError.tooMany;
    }
    
    // Check for duplicates
    if (value.toSet().length != value.length) {
      return TagsValidationError.duplicateTag;
    }
    
    // Validate each tag
    for (final tag in value) {
      if (tag.length > maxTagLength || !validTagRegExp.hasMatch(tag)) {
        return TagsValidationError.invalidTag;
      }
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case TagsValidationError.tooMany:
        return 'Maximum $maxTags tags allowed';
      case TagsValidationError.invalidTag:
        return 'Tags can only contain letters, numbers, hyphens, and underscores (max $maxTagLength chars)';
      case TagsValidationError.duplicateTag:
        return 'Duplicate tags are not allowed';
      case null:
        return null;
    }
  }
}

/// Deadline validation errors
enum DeadlineValidationError {
  inPast,
  tooSoon,
  tooFar,
}

/// Deadline input with validation
class QuestDeadline extends FormzInput<DateTime?, DeadlineValidationError> {
  const QuestDeadline.pure([super.value]) : super.pure();
  const QuestDeadline.dirty([super.value]) : super.dirty();

  static const int minHoursFromNow = 1;
  static const int maxDaysFromNow = 365;

  @override
  DeadlineValidationError? validator(DateTime? value) {
    // Null/empty is allowed for optional field
    if (value == null) return null;
    
    final now = DateTime.now();
    
    if (value.isBefore(now)) {
      return DeadlineValidationError.inPast;
    }
    
    if (value.isBefore(now.add(Duration(hours: minHoursFromNow)))) {
      return DeadlineValidationError.tooSoon;
    }
    
    if (value.isAfter(now.add(Duration(days: maxDaysFromNow)))) {
      return DeadlineValidationError.tooFar;
    }
    
    return null;
  }

  /// Get user-friendly error message
  String? get errorMessage {
    switch (error) {
      case DeadlineValidationError.inPast:
        return 'Deadline cannot be in the past';
      case DeadlineValidationError.tooSoon:
        return 'Deadline must be at least $minHoursFromNow hour from now';
      case DeadlineValidationError.tooFar:
        return 'Deadline cannot be more than $maxDaysFromNow days from now';
      case null:
        return null;
    }
  }
}

/// Complete quest form validation combining all fields
class QuestFormValidation {
  final QuestTitle title;
  final QuestDescription description;
  final QuestPoints basePoints;
  final QuestPoints bonusPoints;
  final EstimatedHours estimatedHours;
  final QuestTags tags;
  final QuestDeadline deadline;

  const QuestFormValidation({
    this.title = const QuestTitle.pure(),
    this.description = const QuestDescription.pure(),
    this.basePoints = const QuestPoints.pure(),
    this.bonusPoints = const QuestPoints.pure(),
    this.estimatedHours = const EstimatedHours.pure(),
    this.tags = const QuestTags.pure(),
    this.deadline = const QuestDeadline.pure(),
  });

  /// Check if the entire form is valid
  bool get isValid {
    return Formz.validate([
      title,
      description,
      basePoints,
      bonusPoints,
      estimatedHours,
      tags,
      deadline,
    ]);
  }

  /// Get all form inputs for validation
  List<FormzInput> get inputs => [
    title,
    description,
    basePoints,
    bonusPoints,
    estimatedHours,
    tags,
    deadline,
  ];

  /// Get form status
  // FormzStatus get status {
  //   return Formz.validate(inputs);
  // }

  /// Copy with new values
  QuestFormValidation copyWith({
    QuestTitle? title,
    QuestDescription? description,
    QuestPoints? basePoints,
    QuestPoints? bonusPoints,
    EstimatedHours? estimatedHours,
    QuestTags? tags,
    QuestDeadline? deadline,
  }) {
    return QuestFormValidation(
      title: title ?? this.title,
      description: description ?? this.description,
      basePoints: basePoints ?? this.basePoints,
      bonusPoints: bonusPoints ?? this.bonusPoints,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      tags: tags ?? this.tags,
      deadline: deadline ?? this.deadline,
    );
  }

  /// Get field errors map for display
  Map<String, String> get fieldErrors {
    final errors = <String, String>{};
    
    if (title.errorMessage != null) {
      errors['title'] = title.errorMessage!;
    }
    
    if (description.errorMessage != null) {
      errors['description'] = description.errorMessage!;
    }
    
    if (basePoints.errorMessage != null) {
      errors['basePoints'] = basePoints.errorMessage!;
    }
    
    if (bonusPoints.errorMessage != null) {
      errors['bonusPoints'] = bonusPoints.errorMessage!;
    }
    
    if (estimatedHours.errorMessage != null) {
      errors['estimatedHours'] = estimatedHours.errorMessage!;
    }
    
    if (tags.errorMessage != null) {
      errors['tags'] = tags.errorMessage!;
    }
    
    if (deadline.errorMessage != null) {
      errors['deadline'] = deadline.errorMessage!;
    }
    
    return errors;
  }

  /// Get all error messages
  List<String> get allErrors {
    return [
      title.errorMessage,
      description.errorMessage,
      basePoints.errorMessage,
      bonusPoints.errorMessage,
      estimatedHours.errorMessage,
      tags.errorMessage,
      deadline.errorMessage,
    ].where((error) => error != null).cast<String>().toList();
  }
}