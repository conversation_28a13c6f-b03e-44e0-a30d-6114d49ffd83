import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../widgets/quest_basic_info_form.dart';
import '../widgets/quest_difficulty_selector.dart';
import '../widgets/quest_points_config.dart';
import '../widgets/quest_task_manager.dart';
import '../widgets/quest_scheduling_form.dart';
import '../widgets/quest_advanced_settings.dart';
import '../widgets/quest_preview.dart';

/// Comprehensive quest creation page with step-by-step interface
class QuestCreationPage extends StatefulWidget {
  /// Optional template ID to pre-populate the form
  final String? templateId;

  /// Optional draft ID to continue editing
  final String? draftId;

  /// Callback when quest is successfully created
  final Function(Quest)? onQuestCreated;

  const QuestCreationPage({
    super.key,
    this.templateId,
    this.draftId,
    this.onQuestCreated,
  });

  @override
  State<QuestCreationPage> createState() => _QuestCreationPageState();
}

class _QuestCreationPageState extends State<QuestCreationPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;

  // Step navigation
  final List<QuestCreationStep> _steps = [
    QuestCreationStep.basicInfo,
    QuestCreationStep.difficulty,
    QuestCreationStep.tasks,
    QuestCreationStep.scheduling,
    QuestCreationStep.advanced,
    QuestCreationStep.preview,
  ];

  int _currentStepIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _steps.length, vsync: this);
    _pageController = PageController();

    // Initialize quest creation
    context.read<QuestCreationBloc>().add(InitializeQuestCreation(
      templateId: widget.templateId,
      draftId: widget.draftId,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<QuestCreationBloc, QuestCreationState>(
      listener: _handleStateChanges,
      builder: (context, state) {
        return Scaffold(
          appBar: _buildAppBar(context, state),
          body: _buildBody(context, state),
          bottomNavigationBar: _buildBottomNavigation(context, state),
          floatingActionButton: _buildFloatingActionButton(context, state),
        );
      },
    );
  }

  void _handleStateChanges(BuildContext context, QuestCreationState state) {
    if (state is QuestCreationSuccess) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Quest created successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Call callback if provided
      widget.onQuestCreated?.call(state.createdQuest);

      // Navigate back
      Navigator.of(context).pop(state.createdQuest);
    } else if (state is QuestCreationError) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, QuestCreationState state) {
    return AppBar(
      title: const Text('Create Quest'),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        // Save draft button
        if (state is QuestCreationInProgress && state.hasUnsavedChanges)
          IconButton(
            icon: const Icon(Icons.save_outlined),
            onPressed: () => _saveDraft(context),
            tooltip: 'Save Draft',
          ),

        // Menu button
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'templates',
              child: ListTile(
                leading: Icon(Icons.description_outlined),
                title: Text('Browse Templates'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'drafts',
              child: ListTile(
                leading: Icon(Icons.drafts_outlined),
                title: Text('My Drafts'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'reset',
              child: ListTile(
                leading: Icon(Icons.refresh_outlined),
                title: Text('Reset Form'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
      bottom: _buildProgressIndicator(context, state),
    );
  }

  PreferredSizeWidget? _buildProgressIndicator(BuildContext context, QuestCreationState state) {
    if (state is! QuestCreationInProgress) return null;

    return PreferredSize(
      preferredSize: const Size.fromHeight(4.0),
      child: LinearProgressIndicator(
        value: (_currentStepIndex + 1) / _steps.length,
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context, QuestCreationState state) {
    if (state is QuestCreationLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading quest creation...'),
          ],
        ),
      );
    }

    if (state is QuestCreationError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading quest creation',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.read<QuestCreationBloc>().add(
                InitializeQuestCreation(
                  templateId: widget.templateId,
                  draftId: widget.draftId,
                ),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state is QuestCreationInProgress) {
      return PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentStepIndex = index;
          });
          _tabController.animateTo(index);
        },
        children: [
          _buildBasicInfoStep(context, state),
          _buildDifficultyStep(context, state),
          _buildTasksStep(context, state),
          _buildSchedulingStep(context, state),
          _buildAdvancedStep(context, state),
          _buildPreviewStep(context, state),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildBasicInfoStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Start by giving your quest a compelling title and description.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestBasicInfoForm(),
        ],
      ),
    );
  }

  Widget _buildDifficultyStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Difficulty & Priority',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Set the difficulty level and priority for your quest.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestDifficultySelector(),
          const SizedBox(height: 24),
          const QuestPointsConfig(),
        ],
      ),
    );
  }

  Widget _buildTasksStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tasks',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Break down your quest into manageable tasks.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestTaskManager(),
        ],
      ),
    );
  }

  Widget _buildSchedulingStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Scheduling',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Set deadlines and time estimates for your quest.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestSchedulingForm(),
        ],
      ),
    );
  }

  Widget _buildAdvancedStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Advanced Settings',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Configure privacy, collaboration, and notification settings.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestAdvancedSettings(),
        ],
      ),
    );
  }

  Widget _buildPreviewStep(BuildContext context, QuestCreationInProgress state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preview',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Review your quest before creating it.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          const QuestPreview(),
        ],
      ),
    );
  }

  Widget? _buildBottomNavigation(BuildContext context, QuestCreationState state) {
    if (state is! QuestCreationInProgress) return null;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Previous button
          if (_currentStepIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _goToPreviousStep,
                child: const Text('Previous'),
              ),
            ),

          if (_currentStepIndex > 0) const SizedBox(width: 16),

          // Next/Create button
          Expanded(
            child: _currentStepIndex < _steps.length - 1
                ? ElevatedButton(
                    onPressed: _canGoToNextStep(state) ? _goToNextStep : null,
                    child: const Text('Next'),
                  )
                : ElevatedButton(
                    onPressed: state.isFormComplete && !state.isSubmitting
                        ? () => _createQuest(context)
                        : null,
                    child: state.isSubmitting
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Create Quest'),
                  ),
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context, QuestCreationState state) {
    if (state is! QuestCreationInProgress) return null;
    if (_currentStepIndex != _steps.length - 1) return null;

    return FloatingActionButton.extended(
      onPressed: state.isFormComplete && !state.isSubmitting
          ? () => _createQuest(context)
          : null,
      icon: state.isSubmitting
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.rocket_launch),
      label: Text(state.isSubmitting ? 'Creating...' : 'Create Quest'),
    );
  }

  // Navigation methods
  void _goToPreviousStep() {
    if (_currentStepIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canGoToNextStep(QuestCreationInProgress state) {
    switch (_steps[_currentStepIndex]) {
      case QuestCreationStep.basicInfo:
        return state.title.trim().isNotEmpty && state.description.trim().isNotEmpty;
      case QuestCreationStep.difficulty:
        return true; // Always can proceed from difficulty step
      case QuestCreationStep.tasks:
        return state.tasks.isNotEmpty;
      case QuestCreationStep.scheduling:
        return true; // Optional step
      case QuestCreationStep.advanced:
        return true; // Optional step
      case QuestCreationStep.preview:
        return state.isFormComplete;
    }
  }

  // Action methods
  void _saveDraft(BuildContext context) {
    context.read<QuestCreationBloc>().add(const SaveDraft());
  }

  void _createQuest(BuildContext context) {
    context.read<QuestCreationBloc>().add(const SubmitQuest());
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'templates':
        _showTemplatesDialog(context);
        break;
      case 'drafts':
        _showDraftsDialog(context);
        break;
      case 'reset':
        _showResetConfirmation(context);
        break;
    }
  }

  void _showTemplatesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quest Templates'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: BlocBuilder<QuestCreationBloc, QuestCreationState>(
            builder: (context, state) {
              if (state is QuestCreationInProgress) {
                return ListView.builder(
                  itemCount: state.availableTemplates.length,
                  itemBuilder: (context, index) {
                    final template = state.availableTemplates[index];
                    return Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                          child: Icon(
                            Icons.description,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        title: Text(template.title),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(template.description),
                            const SizedBox(height: 4),
                            Wrap(
                              spacing: 4,
                              children: template.tags.take(3).map((tag) => Chip(
                                label: Text(tag),
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              )).toList(),
                            ),
                          ],
                        ),
                        trailing: Text('${template.basePoints} pts'),
                        onTap: () {
                          Navigator.of(context).pop();
                          context.read<QuestCreationBloc>().add(ApplyTemplate(template.id));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Applied template: ${template.title}')),
                          );
                        },
                      ),
                    );
                  },
                );
              }
              return const Center(child: CircularProgressIndicator());
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDraftsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Saved Drafts'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: FutureBuilder<List<Map<String, dynamic>>>(
            future: _loadDraftsList(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading drafts: ${snapshot.error}'),
                    ],
                  ),
                );
              }

              final drafts = snapshot.data ?? [];

              if (drafts.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.description_outlined, size: 48, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No saved drafts found'),
                      SizedBox(height: 8),
                      Text(
                        'Save your current quest as a draft to see it here',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: drafts.length,
                itemBuilder: (context, index) {
                  final draft = drafts[index];
                  final savedAt = DateTime.parse(draft['savedAt']);
                  final timeAgo = _formatTimeAgo(savedAt);

                  return Card(
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.description,
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                      title: Text(draft['title'].isEmpty ? 'Untitled Quest' : draft['title']),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (draft['description'].isNotEmpty)
                            Text(
                              draft['description'],
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 4),
                          Text(
                            'Saved $timeAgo',
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                      trailing: PopupMenuButton<String>(
                        onSelected: (action) {
                          switch (action) {
                            case 'load':
                              Navigator.of(context).pop();
                              context.read<QuestCreationBloc>().add(LoadDraft(draft['id']));
                              break;
                            case 'delete':
                              _confirmDeleteDraft(context, draft);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'load',
                            child: Row(
                              children: [
                                Icon(Icons.open_in_new),
                                SizedBox(width: 8),
                                Text('Load Draft'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red),
                                SizedBox(width: 8),
                                Text('Delete', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                        context.read<QuestCreationBloc>().add(LoadDraft(draft['id']));
                      },
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showResetConfirmation(BuildContext context) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Form'),
        content: const Text(
          'Are you sure you want to reset the form? All unsaved changes will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true && mounted) {
        if (context.mounted) {
          context.read<QuestCreationBloc>().add(const ResetForm());
        }
      }
    });
  }

  /// Load list of saved drafts
  Future<List<Map<String, dynamic>>> _loadDraftsList() async {
    // In a real implementation, this would load from API or local storage
    // For now, return mock data
    await Future.delayed(const Duration(milliseconds: 300));

    return [
      {
        'id': 'draft_1',
        'title': 'Complete Flutter Project',
        'description': 'Build a complete Flutter application with authentication and database integration',
        'savedAt': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
      },
      {
        'id': 'draft_2',
        'title': 'Learn Design Patterns',
        'description': 'Study and implement common software design patterns',
        'savedAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
      {
        'id': 'draft_3',
        'title': '',
        'description': 'Untitled quest draft',
        'savedAt': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
      },
    ];
  }

  /// Format time ago string
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  /// Confirm draft deletion
  void _confirmDeleteDraft(BuildContext context, Map<String, dynamic> draft) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Draft'),
        content: Text(
          'Are you sure you want to delete "${draft['title'].isEmpty ? 'Untitled Quest' : draft['title']}"?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close confirmation dialog
              Navigator.of(context).pop(); // Close drafts dialog
              context.read<QuestCreationBloc>().add(DeleteDraft(draft['id']));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Draft deleted successfully')),
              );
            },
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}