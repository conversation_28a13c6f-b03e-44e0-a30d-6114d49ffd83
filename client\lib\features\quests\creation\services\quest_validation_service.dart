import 'package:shared/shared.dart';
import '../bloc/quest_creation_state.dart';
import '../models/quest_creation_validation.dart';

/// Result of quest validation
class QuestValidationResult {
  final bool isValid;
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;
  final List<String> warnings;
  final ValidationSeverity severity;

  const QuestValidationResult({
    required this.isValid,
    required this.fieldErrors,
    required this.generalErrors,
    this.warnings = const [],
    this.severity = ValidationSeverity.none,
  });

  QuestValidationResult.valid() : this(
    isValid: true,
    fieldErrors: const {},
    generalErrors: const [],
    warnings: const [],
    severity: ValidationSeverity.none,
  );

  QuestValidationResult.invalid({
    required this.fieldErrors,
    required this.generalErrors,
    this.warnings = const [],
    this.severity = ValidationSeverity.error,
  }) : isValid = false;

  QuestValidationResult.warning({
    required this.fieldErrors,
    required this.generalErrors,
    required this.warnings,
  }) : isValid = true, severity = ValidationSeverity.warning;

  /// Create result with warnings that don't prevent submission
  QuestValidationResult copyWithWarnings(List<String> warnings) {
    return QuestValidationResult(
      isValid: isValid,
      fieldErrors: fieldErrors,
      generalErrors: generalErrors,
      warnings: [...this.warnings, ...warnings],
      severity: warnings.isNotEmpty ? ValidationSeverity.warning : severity,
    );
  }
}

/// Validation severity levels
enum ValidationSeverity {
  none,
  info,
  warning,
  error,
  critical,
}

/// Comprehensive service for validating quest creation forms
class QuestValidationService {
  
  /// Validation configuration constants
  static const int minTitleLength = 3;
  static const int maxTitleLength = 100;
  static const int minDescriptionLength = 10;
  static const int maxDescriptionLength = 1000;
  static const int minBasePoints = 1;
  static const int maxBasePoints = 10000;
  static const int maxBonusPoints = 5000;
  static const int maxTasks = 50;
  static const int maxParticipants = 100;
  static const int maxTags = 10;
  static const double maxEstimatedHours = 1000.0;
  static const int maxDaysInFuture = 365;
  static const int minHoursFromNow = 1;

  /// Validate a complete quest creation state
  QuestValidationResult validateQuest(QuestCreationInProgress state) {
    final fieldErrors = <String, String>{};
    final generalErrors = <String>[];
    final warnings = <String>[];

    // Validate individual fields
    _validateBasicInfo(state, fieldErrors, warnings);
    _validatePointsConfiguration(state, fieldErrors, warnings);
    _validateScheduling(state, fieldErrors, warnings);
    _validateTasks(state, fieldErrors, warnings);
    _validateParticipants(state, fieldErrors, warnings);
    _validateTags(state, fieldErrors, warnings);
    _validateAdvancedSettings(state, fieldErrors, warnings);

    // Cross-field validation
    _validateCrossFieldRules(state, generalErrors, warnings);
    
    // Business rule validation
    _validateBusinessRules(state, generalErrors, warnings);

    final isValid = fieldErrors.isEmpty && generalErrors.isEmpty;
    
    if (!isValid) {
      return QuestValidationResult.invalid(
        fieldErrors: fieldErrors,
        generalErrors: generalErrors,
        warnings: warnings,
      );
    }

    if (warnings.isNotEmpty) {
      return QuestValidationResult.warning(
        fieldErrors: fieldErrors,
        generalErrors: generalErrors,
        warnings: warnings,
      );
    }

    return QuestValidationResult.valid();
  }

  /// Validate specific field individually
  QuestValidationResult validateField(QuestCreationInProgress state, String fieldName) {
    final fieldErrors = <String, String>{};
    final warnings = <String>[];

    switch (fieldName) {
      case 'title':
        _validateTitle(state.title, fieldErrors, warnings);
        break;
      case 'description':
        _validateDescription(state.description, fieldErrors, warnings);
        break;
      case 'basePoints':
        _validateBasePoints(state.basePoints, fieldErrors, warnings);
        break;
      case 'bonusPoints':
        _validateBonusPoints(state.bonusPoints, fieldErrors, warnings);
        break;
      case 'estimatedHours':
        _validateEstimatedHours(state.estimatedHours, fieldErrors, warnings);
        break;
      case 'deadline':
        _validateDeadline(state.deadline, fieldErrors, warnings);
        break;
      case 'startDate':
        _validateStartDate(state.startDate, fieldErrors, warnings);
        break;
    }

    final isValid = fieldErrors.isEmpty;
    
    return QuestValidationResult(
      isValid: isValid,
      fieldErrors: fieldErrors,
      generalErrors: const [],
      warnings: warnings,
    );
  }

  /// Validate basic quest information
  void _validateBasicInfo(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    _validateTitle(state.title, fieldErrors, warnings);
    _validateDescription(state.description, fieldErrors, warnings);
    _validateCategory(state.category, fieldErrors, warnings);
    _validateDifficulty(state.difficulty, fieldErrors, warnings);
    _validatePriority(state.priority, fieldErrors, warnings);
  }

  /// Validate quest title
  void _validateTitle(String title, Map<String, String> fieldErrors, List<String> warnings) {
    final titleValidation = QuestTitle.dirty(title);
    
    if (titleValidation.isNotValid && titleValidation.error != null) {
      fieldErrors['title'] = titleValidation.errorMessage ?? 'Invalid title';
      return;
    }

    final trimmed = title.trim();
    
    // Additional business logic validation
    if (trimmed.toLowerCase().contains('test') || trimmed.toLowerCase().contains('placeholder')) {
      warnings.add('Title appears to be a placeholder - consider using a more descriptive title');
    }

    if (trimmed.split(' ').length < 2) {
      warnings.add('Consider adding more descriptive words to your quest title');
    }

    // Check for potentially offensive content patterns
    if (_containsOffensiveContent(trimmed)) {
      fieldErrors['title'] = 'Title contains inappropriate content';
    }
  }

  /// Validate quest description
  void _validateDescription(String description, Map<String, String> fieldErrors, List<String> warnings) {
    final descriptionValidation = QuestDescription.dirty(description);
    
    if (descriptionValidation.isNotValid && descriptionValidation.error != null) {
      fieldErrors['description'] = descriptionValidation.errorMessage ?? 'Invalid description';
      return;
    }

    final trimmed = description.trim();
    
    if (trimmed.isNotEmpty) {
      // Check description quality
      if (trimmed.split(' ').length < 5) {
        warnings.add('Consider adding more detail to your quest description');
      }

      if (trimmed == trimmed.toUpperCase() && trimmed.length > 20) {
        warnings.add('Avoid using ALL CAPS in descriptions');
      }

      // Check for potentially offensive content
      if (_containsOffensiveContent(trimmed)) {
        fieldErrors['description'] = 'Description contains inappropriate content';
      }
    }
  }

  /// Validate category selection
  void _validateCategory(QuestCategory? category, Map<String, String> fieldErrors, List<String> warnings) {
    if (category == null) {
      fieldErrors['category'] = 'Please select a quest category';
    }
  }

  /// Validate difficulty selection
  void _validateDifficulty(QuestDifficulty difficulty, Map<String, String> fieldErrors, List<String> warnings) {
    // Difficulty is always valid as it has a default value
    // But we can provide guidance
    if (difficulty == QuestDifficulty.master) {
      warnings.add('Master difficulty quests require significant time and effort - ensure your planning is thorough');
    }
  }

  /// Validate priority selection
  void _validatePriority(QuestPriority priority, Map<String, String> fieldErrors, List<String> warnings) {
    if (priority == QuestPriority.urgent) {
      warnings.add('Urgent priority should be reserved for time-sensitive quests');
    }
  }

  /// Validate points configuration
  void _validatePointsConfiguration(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    _validateBasePoints(state.basePoints, fieldErrors, warnings);
    _validateBonusPoints(state.bonusPoints, fieldErrors, warnings);
    _validatePointsBalance(state, fieldErrors, warnings);
  }

  /// Validate base points
  void _validateBasePoints(int basePoints, Map<String, String> fieldErrors, List<String> warnings) {
    if (basePoints < minBasePoints) {
      fieldErrors['basePoints'] = 'Base points must be at least $minBasePoints';
      return;
    }
    
    if (basePoints > maxBasePoints) {
      fieldErrors['basePoints'] = 'Base points cannot exceed $maxBasePoints';
      return;
    }

    // Provide guidance based on points
    if (basePoints > 1000) {
      warnings.add('High point values should be reserved for complex, long-term quests');
    } else if (basePoints < 25) {
      warnings.add('Consider increasing points for better motivation');
    }
  }

  /// Validate bonus points
  void _validateBonusPoints(int bonusPoints, Map<String, String> fieldErrors, List<String> warnings) {
    if (bonusPoints < 0) {
      fieldErrors['bonusPoints'] = 'Bonus points cannot be negative';
      return;
    }
    
    if (bonusPoints > maxBonusPoints) {
      fieldErrors['bonusPoints'] = 'Bonus points cannot exceed $maxBonusPoints';
      return;
    }
  }

  /// Validate points balance between base and bonus
  void _validatePointsBalance(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    final basePoints = state.basePoints;
    final bonusPoints = state.bonusPoints;
    
    if (bonusPoints > basePoints) {
      warnings.add('Bonus points are higher than base points - consider balancing the rewards');
    }
    
    if (bonusPoints > 0 && bonusPoints < (basePoints * 0.1)) {
      warnings.add('Bonus points might be too low to provide meaningful incentive');
    }

    // Validate difficulty-appropriate points
    final expectedRange = _getExpectedPointsRange(state.difficulty);
    final totalPoints = basePoints + bonusPoints;
    
    if (totalPoints < expectedRange.$1) {
      warnings.add('Points seem low for ${state.difficulty.name} difficulty (suggested: ${expectedRange.$1}-${expectedRange.$2})');
    } else if (totalPoints > expectedRange.$2) {
      warnings.add('Points seem high for ${state.difficulty.name} difficulty (suggested: ${expectedRange.$1}-${expectedRange.$2})');
    }
  }

  /// Validate scheduling information
  void _validateScheduling(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    _validateStartDate(state.startDate, fieldErrors, warnings);
    _validateDeadline(state.deadline, fieldErrors, warnings);
    _validateEstimatedHours(state.estimatedHours, fieldErrors, warnings);
    _validateScheduleConsistency(state, fieldErrors, warnings);
  }

  /// Validate start date
  void _validateStartDate(DateTime? startDate, Map<String, String> fieldErrors, List<String> warnings) {
    if (startDate != null) {
      final now = DateTime.now();
      
      if (startDate.isBefore(now.subtract(const Duration(hours: 1)))) {
        fieldErrors['startDate'] = 'Start date cannot be in the past';
      } else if (startDate.isAfter(now.add(Duration(days: maxDaysInFuture)))) {
        fieldErrors['startDate'] = 'Start date cannot be more than $maxDaysInFuture days in the future';
      }
    }
  }

  /// Validate deadline
  void _validateDeadline(DateTime? deadline, Map<String, String> fieldErrors, List<String> warnings) {
    if (deadline != null) {
      final deadlineValidation = QuestDeadline.dirty(deadline);
      
      if (deadlineValidation.isNotValid && deadlineValidation.error != null) {
        fieldErrors['deadline'] = deadlineValidation.errorMessage ?? 'Invalid deadline';
        return;
      }

      final now = DateTime.now();
      final daysUntilDeadline = deadline.difference(now).inDays;
      
      if (daysUntilDeadline <= 1) {
        warnings.add('Short deadline - ensure the quest can be completed in time');
      } else if (daysUntilDeadline > 90) {
        warnings.add('Long deadline - consider breaking into smaller quests for better engagement');
      }
    }
  }

  /// Validate estimated hours
  void _validateEstimatedHours(int? estimatedHours, Map<String, String> fieldErrors, List<String> warnings) {
    if (estimatedHours == null) {
      return; // Null is allowed for optional field
    }
    
    if (estimatedHours < 0) {
      fieldErrors['estimatedHours'] = 'Estimated hours cannot be negative';
      return;
    }
    
    if (estimatedHours > maxEstimatedHours) {
      fieldErrors['estimatedHours'] = 'Estimated hours cannot exceed ${maxEstimatedHours.round()}';
      return;
    }

    if (estimatedHours > 40) {
      warnings.add('Long quests (40+ hours) should be broken into smaller sub-quests');
    } else if (estimatedHours > 0 && estimatedHours < 1) {
      warnings.add('Very short tasks might be better managed outside the quest system');
    }
  }

  /// Validate schedule consistency
  void _validateScheduleConsistency(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    final startDate = state.startDate;
    final deadline = state.deadline;
    final estimatedHours = state.estimatedHours;
    
    if (startDate != null && deadline != null) {
      if (deadline.isBefore(startDate)) {
        fieldErrors['deadline'] = 'Deadline must be after start date';
        return;
      }
      
      if (estimatedHours != null && estimatedHours > 0) {
        final availableDays = deadline.difference(startDate).inDays;
        final estimatedDays = (estimatedHours / 8).ceil(); // Assuming 8 hours per day
        
        if (estimatedDays > availableDays) {
          warnings.add('Estimated time ($estimatedDays days) exceeds available time ($availableDays days)');
        }
      }
    }
  }

  /// Validate tasks
  void _validateTasks(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    final tasks = state.tasks;
    
    if (tasks.length > maxTasks) {
      fieldErrors['tasks'] = 'Cannot exceed $maxTasks tasks per quest';
      return;
    }

    // Validate individual tasks
    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];
      final taskValidation = TaskTitle.dirty(task.title);
      
      if (taskValidation.isNotValid) {
        fieldErrors['tasks'] = 'Task ${i + 1}: ${taskValidation.errorMessage ?? 'Invalid task title'}';
        break;
      }
    }

    // Check for duplicate task titles
    final taskTitles = tasks.map((t) => t.title.toLowerCase().trim()).toList();
    final duplicates = <String>[];
    for (int i = 0; i < taskTitles.length; i++) {
      for (int j = i + 1; j < taskTitles.length; j++) {
        if (taskTitles[i] == taskTitles[j] && !duplicates.contains(taskTitles[i])) {
          duplicates.add(taskTitles[i]);
        }
      }
    }
    
    if (duplicates.isNotEmpty) {
      warnings.add('Duplicate task titles found: ${duplicates.join(', ')}');
    }

    // Task quantity guidance
    if (tasks.isEmpty) {
      warnings.add('Consider adding tasks to break down your quest into manageable steps');
    } else if (tasks.length > 20) {
      warnings.add('Large number of tasks (${tasks.length}) - consider grouping into sub-quests');
    }

    // Check task completion ratio if any are marked complete
    final completedTasks = tasks.where((t) => t.isCompleted).length;
    if (completedTasks > 0) {
      warnings.add('$completedTasks tasks already marked complete - ensure this is intentional');
    }
  }

  /// Validate participants
  void _validateParticipants(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    final participants = state.participantIds;
    
    if (participants.length > maxParticipants) {
      fieldErrors['participants'] = 'Cannot exceed $maxParticipants participants per quest';
      return;
    }

    if (participants.length > 10) {
      warnings.add('Large groups (${participants.length} people) may be difficult to coordinate');
    }
  }

  /// Validate tags
  void _validateTags(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    final tags = state.tags;
    final tagsValidation = QuestTags.dirty(tags);
    
    if (tagsValidation.isNotValid && tagsValidation.error != null) {
      fieldErrors['tags'] = tagsValidation.errorMessage ?? 'Invalid tags';
      return;
    }

    if (tags.isEmpty) {
      warnings.add('Adding tags helps organize and discover your quest');
    }
  }

  /// Validate advanced settings
  void _validateAdvancedSettings(
    QuestCreationInProgress state,
    Map<String, String> fieldErrors,
    List<String> warnings,
  ) {
    // Validate privacy settings consistency
    if (state.isPrivate && state.participantIds.isNotEmpty) {
      warnings.add('Private quest with participants - ensure they have access');
    }
    
    if (!state.allowCollaboration && state.participantIds.length > 1) {
      warnings.add('Collaboration disabled with multiple participants may cause confusion');
    }
  }

  /// Cross-field validation rules
  void _validateCrossFieldRules(
    QuestCreationInProgress state,
    List<String> generalErrors,
    List<String> warnings,
  ) {
    // Validate consistency between difficulty and other fields
    _validateDifficultyConsistency(state, warnings);
    
    // Validate priority and deadline consistency
    _validatePriorityDeadlineConsistency(state, warnings);
    
    // Validate collaboration settings
    _validateCollaborationConsistency(state, warnings);
  }

  /// Validate difficulty consistency with other fields
  void _validateDifficultyConsistency(QuestCreationInProgress state, List<String> warnings) {
    final difficulty = state.difficulty;
    final estimatedHours = state.estimatedHours;
    final taskCount = state.tasks.length;
    
    switch (difficulty) {
      case QuestDifficulty.beginner:
        if (estimatedHours != null && estimatedHours > 4) {
          warnings.add('Beginner quests typically take less than 4 hours');
        }
        if (taskCount > 5) {
          warnings.add('Beginner quests usually have 5 or fewer tasks');
        }
        break;
      
      case QuestDifficulty.expert:
      case QuestDifficulty.master:
        if (estimatedHours != null && estimatedHours < 8) {
          warnings.add('${difficulty.name} quests typically require significant time investment');
        }
        if (taskCount < 3) {
          warnings.add('${difficulty.name} quests usually have multiple complex tasks');
        }
        break;
      
      default:
        break;
    }
  }

  /// Validate priority and deadline consistency
  void _validatePriorityDeadlineConsistency(QuestCreationInProgress state, List<String> warnings) {
    final priority = state.priority;
    final deadline = state.deadline;
    
    if (priority == QuestPriority.urgent && deadline != null) {
      final daysUntilDeadline = deadline.difference(DateTime.now()).inDays;
      if (daysUntilDeadline > 7) {
        warnings.add('Urgent priority with distant deadline may be confusing');
      }
    }
    
    if (priority == QuestPriority.low && deadline != null) {
      final daysUntilDeadline = deadline.difference(DateTime.now()).inDays;
      if (daysUntilDeadline <= 2) {
        warnings.add('Low priority with near deadline may be inconsistent');
      }
    }
  }

  /// Validate collaboration settings consistency
  void _validateCollaborationConsistency(QuestCreationInProgress state, List<String> warnings) {
    final hasParticipants = state.participantIds.isNotEmpty;
    final allowsCollaboration = state.allowCollaboration;
    
    if (hasParticipants && !allowsCollaboration) {
      warnings.add('Consider enabling collaboration for multi-participant quests');
    }
  }

  /// Business rule validation
  void _validateBusinessRules(
    QuestCreationInProgress state,
    List<String> generalErrors,
    List<String> warnings,
  ) {
    // Rule: Quest must have meaningful content
    if (state.title.trim().isEmpty || state.description.trim().isEmpty) {
      generalErrors.add('Quest must have both title and description');
    }
    
    // Rule: Points must be proportional to effort
    final totalPoints = state.basePoints + state.bonusPoints;
    if (state.estimatedHours != null && state.estimatedHours! > 0) {
      final pointsPerHour = totalPoints / state.estimatedHours!;
      if (pointsPerHour < 10) {
        warnings.add('Low points per hour ratio - consider increasing rewards');
      } else if (pointsPerHour > 200) {
        warnings.add('High points per hour ratio - ensure quest difficulty is appropriate');
      }
    }
    
    // Rule: Deadlines should be reasonable
    if (state.deadline != null) {
      final hoursUntilDeadline = state.deadline!.difference(DateTime.now()).inHours;
      if (hoursUntilDeadline < minHoursFromNow) {
        generalErrors.add('Deadline must be at least $minHoursFromNow hour in the future');
      }
    }
    
    // Rule: Private quests with participants need explicit access
    if (state.isPrivate && state.participantIds.isNotEmpty) {
      warnings.add('Ensure participants have been granted access to this private quest');
    }
  }

  /// Check for potentially offensive content (basic implementation)
  bool _containsOffensiveContent(String content) {
    // This is a simple implementation - in production, you'd want a more sophisticated approach
    final offensiveWords = ['spam', 'fake', 'scam'];
    final lowerContent = content.toLowerCase();
    
    return offensiveWords.any((word) => lowerContent.contains(word));
  }

  /// Get expected points range for difficulty
  (int, int) _getExpectedPointsRange(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return (25, 100);
      case QuestDifficulty.intermediate:
        return (75, 250);
      case QuestDifficulty.advanced:
        return (200, 500);
      case QuestDifficulty.expert:
        return (400, 800);
      case QuestDifficulty.master:
        return (700, 1500);
    }
  }

  /// Validate if quest is ready for submission
  bool isReadyForSubmission(QuestCreationInProgress state) {
    final result = validateQuest(state);
    return result.isValid;
  }

  /// Get validation summary for UI display
  ValidationSummary getValidationSummary(QuestCreationInProgress state) {
    final result = validateQuest(state);
    
    return ValidationSummary(
      isValid: result.isValid,
      errorCount: result.fieldErrors.length + result.generalErrors.length,
      warningCount: result.warnings.length,
      severity: result.severity,
      canSubmit: result.isValid,
      nextSteps: _getNextSteps(result),
    );
  }

  /// Get next steps for improving the quest
  List<String> _getNextSteps(QuestValidationResult result) {
    final steps = <String>[];
    
    if (result.fieldErrors.containsKey('title')) {
      steps.add('Fix the quest title');
    }
    
    if (result.fieldErrors.containsKey('description')) {
      steps.add('Provide a detailed description');
    }
    
    if (result.fieldErrors.containsKey('basePoints')) {
      steps.add('Set appropriate base points');
    }
    
    if (result.fieldErrors.containsKey('deadline')) {
      steps.add('Choose a valid deadline');
    }
    
    if (result.warnings.isNotEmpty && result.warnings.any((w) => w.contains('task'))) {
      steps.add('Consider adding tasks to organize your quest');
    }
    
    if (steps.isEmpty && result.isValid) {
      steps.add('Your quest is ready to submit!');
    } else if (steps.isEmpty) {
      steps.add('Review the errors above');
    }
    
    return steps;
  }
}

/// Summary of validation results for UI display
class ValidationSummary {
  final bool isValid;
  final int errorCount;
  final int warningCount;
  final ValidationSeverity severity;
  final bool canSubmit;
  final List<String> nextSteps;

  const ValidationSummary({
    required this.isValid,
    required this.errorCount,
    required this.warningCount,
    required this.severity,
    required this.canSubmit,
    required this.nextSteps,
  });

  /// Get display text for validation status
  String get statusText {
    if (isValid && warningCount == 0) {
      return 'Ready to submit';
    } else if (isValid && warningCount > 0) {
      return '$warningCount warning${warningCount == 1 ? '' : 's'}';
    } else {
      return '$errorCount error${errorCount == 1 ? '' : 's'}${warningCount > 0 ? ', $warningCount warning${warningCount == 1 ? '' : 's'}' : ''}';
    }
  }

  /// Get color for status display
  String get statusColor {
    switch (severity) {
      case ValidationSeverity.none:
        return 'success';
      case ValidationSeverity.info:
        return 'info';
      case ValidationSeverity.warning:
        return 'warning';
      case ValidationSeverity.error:
      case ValidationSeverity.critical:
        return 'error';
    }
  }
}