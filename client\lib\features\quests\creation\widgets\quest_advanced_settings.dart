import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';

/// Widget for configuring collaboration, privacy, and notification settings for quests
class QuestAdvancedSettings extends StatelessWidget {
  const QuestAdvancedSettings({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPrivacySettings(context, state),
            const SizedBox(height: 24),
            _buildCollaborationSettings(context, state),
            const SizedBox(height: 24),
            _buildNotificationSettings(context, state),
            const SizedBox(height: 24),
            _buildTagsSection(context, state),
          ],
        );
      },
    );
  }

  Widget _buildPrivacySettings(BuildContext context, QuestCreationInProgress state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.privacy_tip_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Privacy Settings',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Private Quest'),
              subtitle: const Text('Only you and invited participants can see this quest'),
              value: state.isPrivate,
              onChanged: (value) {
                context.read<QuestCreationBloc>().add(
                  UpdateAdvancedSettings(isPrivate: value),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollaborationSettings(BuildContext context, QuestCreationInProgress state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.group_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Collaboration',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Allow Collaboration'),
              subtitle: const Text('Let others contribute to this quest'),
              value: state.allowCollaboration,
              onChanged: (value) {
                context.read<QuestCreationBloc>().add(
                  UpdateAdvancedSettings(allowCollaboration: value),
                );
              },
            ),
            if (state.allowCollaboration) ...[
              const SizedBox(height: 16),
              _buildParticipantsSection(context, state),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings(BuildContext context, QuestCreationInProgress state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notifications',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Enable Notifications'),
              subtitle: const Text('Get notified about quest progress and updates'),
              value: state.enableNotifications,
              onChanged: (value) {
                context.read<QuestCreationBloc>().add(
                  UpdateAdvancedSettings(enableNotifications: value),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParticipantsSection(BuildContext context, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Participants',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        if (state.participantIds.isEmpty)
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
              ),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.person_add_outlined,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  'No participants added yet',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          )
        else
          ...state.participantIds.map((participantId) => ListTile(
                leading: CircleAvatar(
                  child: Text(participantId.substring(0, 1).toUpperCase()),
                ),
                title: Text('User $participantId'),
                trailing: IconButton(
                  icon: const Icon(Icons.remove_circle_outline),
                  onPressed: () {
                    context.read<QuestCreationBloc>().add(
                      RemoveParticipant(participantId),
                    );
                  },
                ),
              )),
        const SizedBox(height: 8),
        OutlinedButton.icon(
          onPressed: () => _showAddParticipantDialog(context),
          icon: const Icon(Icons.person_add),
          label: const Text('Add Participant'),
        ),
      ],
    );
  }

  Widget _buildTagsSection(BuildContext context, QuestCreationInProgress state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.label_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Tags',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (state.tags.isEmpty)
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.add_circle_outline,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'No tags added yet',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              )
            else
              Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children: state.tags.map((tag) => Chip(
                  label: Text(tag),
                  onDeleted: () {
                    final updatedTags = List<String>.from(state.tags)..remove(tag);
                    context.read<QuestCreationBloc>().add(
                      UpdateTags(updatedTags),
                    );
                  },
                )).toList(),
              ),
            const SizedBox(height: 8),
            OutlinedButton.icon(
              onPressed: () => _showAddTagDialog(context, state),
              icon: const Icon(Icons.add),
              label: const Text('Add Tag'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddParticipantDialog(BuildContext context) {
    showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Participant'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Email or Username',
                hintText: 'Enter participant email or username',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Note: Participant management is coming soon!',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Participant management coming soon!'),
                ),
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showAddTagDialog(BuildContext context, QuestCreationInProgress state) {
    final controller = TextEditingController();

    showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Tag'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Tag name',
            hintText: 'Enter a tag for your quest',
          ),
          autofocus: true,
          onSubmitted: (value) {
            if (value.trim().isNotEmpty && !state.tags.contains(value.trim())) {
              final updatedTags = List<String>.from(state.tags)..add(value.trim());
              context.read<QuestCreationBloc>().add(UpdateTags(updatedTags));
            }
            Navigator.of(context).pop();
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final value = controller.text.trim();
              if (value.isNotEmpty && !state.tags.contains(value)) {
                final updatedTags = List<String>.from(state.tags)..add(value);
                context.read<QuestCreationBloc>().add(UpdateTags(updatedTags));
              }
              Navigator.of(context).pop();
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}