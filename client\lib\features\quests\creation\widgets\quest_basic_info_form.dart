import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../models/quest_creation_validation.dart';

/// Widget for collecting basic quest information including title, description, and category
class QuestBasicInfoForm extends StatefulWidget {
  /// Callback when form values change
  final VoidCallback? onChanged;
  
  /// Whether to show advanced validation hints
  final bool showValidationHints;
  
  /// Custom padding for the form
  final EdgeInsetsGeometry? padding;

  const QuestBasicInfoForm({
    super.key,
    this.onChanged,
    this.showValidationHints = true,
    this.padding,
  });

  @override
  State<QuestBasicInfoForm> createState() => _QuestBasicInfoFormState();
}

class _QuestBasicInfoFormState extends State<QuestBasicInfoForm> {
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late FocusNode _titleFocusNode;
  late FocusNode _descriptionFocusNode;
  
  // Validation state
  QuestTitle _titleValidation = const QuestTitle.pure();
  QuestDescription _descriptionValidation = const QuestDescription.pure();
  bool _titleTouched = false;
  bool _descriptionTouched = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _titleFocusNode = FocusNode();
    _descriptionFocusNode = FocusNode();
    
    // Set up focus listeners for validation
    _titleFocusNode.addListener(_onTitleFocusChange);
    _descriptionFocusNode.addListener(_onDescriptionFocusChange);
    
    // Initialize with current state if available
    _initializeFromState();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.removeListener(_onTitleFocusChange);
    _descriptionFocusNode.removeListener(_onDescriptionFocusChange);
    _titleFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  void _initializeFromState() {
    final state = context.read<QuestCreationBloc>().state;
    if (state is QuestCreationInProgress) {
      _titleController.text = state.title;
      _descriptionController.text = state.description;
      
      // Initialize validation state
      if (state.title.isNotEmpty) {
        _titleValidation = QuestTitle.dirty(state.title);
        _titleTouched = true;
      }
      if (state.description.isNotEmpty) {
        _descriptionValidation = QuestDescription.dirty(state.description);
        _descriptionTouched = true;
      }
    }
  }

  void _onTitleFocusChange() {
    if (!_titleFocusNode.hasFocus && !_titleTouched) {
      setState(() {
        _titleTouched = true;
        _titleValidation = QuestTitle.dirty(_titleController.text);
      });
    }
  }

  void _onDescriptionFocusChange() {
    if (!_descriptionFocusNode.hasFocus && !_descriptionTouched) {
      setState(() {
        _descriptionTouched = true;
        _descriptionValidation = QuestDescription.dirty(_descriptionController.text);
      });
    }
  }

  void _onTitleChanged(String value) {
    setState(() {
      _titleValidation = QuestTitle.dirty(value);
      if (!_titleTouched) _titleTouched = true;
    });
    
    // Update BLoC state
    context.read<QuestCreationBloc>().add(
      UpdateBasicInfo(title: value),
    );
    
    widget.onChanged?.call();
  }

  void _onDescriptionChanged(String value) {
    setState(() {
      _descriptionValidation = QuestDescription.dirty(value);
      if (!_descriptionTouched) _descriptionTouched = true;
    });
    
    // Update BLoC state
    context.read<QuestCreationBloc>().add(
      UpdateBasicInfo(description: value),
    );
    
    widget.onChanged?.call();
  }

  void _onCategoryChanged(QuestCategory? category) {
    if (category != null) {
      context.read<QuestCreationBloc>().add(
        UpdateBasicInfo(category: category),
      );
      widget.onChanged?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      buildWhen: (previous, current) {
        // Only rebuild when basic info changes
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.title != current.title ||
                 previous.description != current.description ||
                 previous.category != current.category ||
                 previous.fieldErrors != current.fieldErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: widget.padding ?? const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section Header
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 24,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Basic Information',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Start by giving your quest a compelling title and description that motivates action.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Quest Title Field
              _buildTitleField(theme, state),
              
              const SizedBox(height: 20),
              
              // Quest Description Field
              _buildDescriptionField(theme, state),
              
              const SizedBox(height: 20),
              
              // Category Selection
              _buildCategoryField(theme, state),
              
              // Validation Summary (if there are errors)
              if (widget.showValidationHints && (_titleTouched || _descriptionTouched))
                _buildValidationSummary(theme),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTitleField(ThemeData theme, QuestCreationInProgress state) {
    final hasError = _titleTouched && _titleValidation.errorMessage != null;
    final serverError = state.fieldErrors['title'];
    final errorText = serverError ?? (_titleTouched ? _titleValidation.errorMessage : null);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _titleController,
          focusNode: _titleFocusNode,
          maxLength: QuestTitle.maxLength,
          textInputAction: TextInputAction.next,
          onFieldSubmitted: (_) => _descriptionFocusNode.requestFocus(),
          onChanged: _onTitleChanged,
          decoration: InputDecoration(
            labelText: 'Quest Title *',
            hintText: 'Enter a compelling quest title...',
            helperText: 'A clear, action-oriented title that motivates completion',
            errorText: errorText,
            prefixIcon: Icon(
              Icons.title,
              color: hasError 
                ? theme.colorScheme.error 
                : theme.colorScheme.onSurfaceVariant,
            ),
            border: const OutlineInputBorder(),
            counterStyle: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          style: theme.textTheme.bodyLarge,
        ),
        
        // Real-time validation hints
        if (widget.showValidationHints && _titleController.text.isNotEmpty && !hasError)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Great title! Clear and actionable.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildDescriptionField(ThemeData theme, QuestCreationInProgress state) {
    final hasError = _descriptionTouched && _descriptionValidation.errorMessage != null;
    final serverError = state.fieldErrors['description'];
    final errorText = serverError ?? (_descriptionTouched ? _descriptionValidation.errorMessage : null);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _descriptionController,
          focusNode: _descriptionFocusNode,
          maxLength: QuestDescription.maxLength,
          maxLines: 4,
          minLines: 2,
          textInputAction: TextInputAction.newline,
          onChanged: _onDescriptionChanged,
          decoration: InputDecoration(
            labelText: 'Quest Description *',
            hintText: 'Describe what this quest involves and why it matters...',
            helperText: 'Provide details about objectives, requirements, and expected outcomes',
            errorText: errorText,
            prefixIcon: Icon(
              Icons.description,
              color: hasError 
                ? theme.colorScheme.error 
                : theme.colorScheme.onSurfaceVariant,
            ),
            border: const OutlineInputBorder(),
            alignLabelWithHint: true,
            counterStyle: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          style: theme.textTheme.bodyLarge,
        ),
        
        // Real-time validation hints
        if (widget.showValidationHints && _descriptionController.text.isNotEmpty && !hasError)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Excellent description! Clear and detailed.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCategoryField(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category *',
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 8),
        
        DropdownButtonFormField<QuestCategory>(
          value: state.category,
          onChanged: _onCategoryChanged,
          decoration: InputDecoration(
            hintText: 'Select a category for your quest',
            prefixIcon: Icon(
              _getCategoryIcon(state.category),
              color: theme.colorScheme.onSurfaceVariant,
            ),
            border: const OutlineInputBorder(),
            helperText: 'Choose the category that best fits your quest',
          ),
          items: QuestCategory.values.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    size: 20,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _getCategoryName(category),
                        style: theme.textTheme.bodyLarge,
                      ),
                      Text(
                        _getCategoryDescription(category),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildValidationSummary(ThemeData theme) {
    final errors = <String>[];
    
    if (_titleTouched && _titleValidation.errorMessage != null) {
      errors.add('Title: ${_titleValidation.errorMessage}');
    }
    
    if (_descriptionTouched && _descriptionValidation.errorMessage != null) {
      errors.add('Description: ${_descriptionValidation.errorMessage}');
    }
    
    if (errors.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_outlined,
                size: 16,
                color: theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                'Please fix the following issues:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...errors.map((error) => Padding(
            padding: const EdgeInsets.only(left: 24, bottom: 4),
            child: Text(
              '• $error',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          )),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(QuestCategory category) {
    switch (category) {
      case QuestCategory.health:
        return Icons.fitness_center;
      case QuestCategory.learning:
        return Icons.school;
      case QuestCategory.productivity:
        return Icons.work;
      case QuestCategory.creative:
        return Icons.palette;
      case QuestCategory.social:
        return Icons.people;
      case QuestCategory.work:
        return Icons.business;
      case QuestCategory.personal:
        return Icons.person;
      case QuestCategory.other:
        return Icons.more_horiz;
    }
  }

  String _getCategoryName(QuestCategory category) {
    switch (category) {
      case QuestCategory.health:
        return 'Health & Fitness';
      case QuestCategory.learning:
        return 'Learning & Development';
      case QuestCategory.productivity:
        return 'Productivity';
      case QuestCategory.creative:
        return 'Creative & Arts';
      case QuestCategory.social:
        return 'Social & Community';
      case QuestCategory.work:
        return 'Work & Career';
      case QuestCategory.personal:
        return 'Personal Growth';
      case QuestCategory.other:
        return 'Other';
    }
  }

  String _getCategoryDescription(QuestCategory category) {
    switch (category) {
      case QuestCategory.health:
        return 'Fitness, wellness, and healthy habits';
      case QuestCategory.learning:
        return 'Education, skills, and knowledge';
      case QuestCategory.productivity:
        return 'Efficiency, organization, and systems';
      case QuestCategory.creative:
        return 'Art, creativity, and self-expression';
      case QuestCategory.social:
        return 'Relationships and community building';
      case QuestCategory.work:
        return 'Professional development and career';
      case QuestCategory.personal:
        return 'Self-improvement and personal goals';
      case QuestCategory.other:
        return 'Miscellaneous and uncategorized';
    }
  }
}