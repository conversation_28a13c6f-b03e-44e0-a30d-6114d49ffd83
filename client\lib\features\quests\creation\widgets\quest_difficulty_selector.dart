import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';

/// Interactive widget for selecting quest difficulty with visual indicators and point integration
class QuestDifficultySelector extends StatefulWidget {
  /// Callback when difficulty changes
  final ValueChanged<QuestDifficulty>? onDifficultyChanged;
  
  /// Callback when priority changes
  final ValueChanged<QuestPriority>? onPriorityChanged;
  
  /// Whether to show suggested points
  final bool showSuggestedPoints;
  
  /// Whether to animate difficulty changes
  final bool animateChanges;
  
  /// Custom padding for the widget
  final EdgeInsetsGeometry? padding;

  const QuestDifficultySelector({
    super.key,
    this.onDifficultyChanged,
    this.onPriorityChanged,
    this.showSuggestedPoints = true,
    this.animateChanges = true,
    this.padding,
  });

  @override
  State<QuestDifficultySelector> createState() => _QuestDifficultySelectorState();
}

class _QuestDifficultySelectorState extends State<QuestDifficultySelector>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _showPointsAnimation = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // Initialize from current state
    _initializeFromState();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _initializeFromState() {
    // State is now managed by BLoC - no local state needed
  }

  void _onDifficultySelected(QuestDifficulty difficulty) {
    setState(() {
      _showPointsAnimation = true;
    });
    
    // Trigger animations
    if (widget.animateChanges) {
      _scaleController.forward().then((_) {
        _scaleController.reverse();
      });
      _slideController.forward().then((_) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _showPointsAnimation = false;
            });
            _slideController.reverse();
          }
        });
      });
    }
    
    // Update BLoC state
    context.read<QuestCreationBloc>().add(
      UpdateDifficultyAndPriority(difficulty: difficulty),
    );
    
    widget.onDifficultyChanged?.call(difficulty);
  }

  void _onPrioritySelected(QuestPriority priority) {
    // Update BLoC state
    context.read<QuestCreationBloc>().add(
      UpdateDifficultyAndPriority(priority: priority),
    );
    
    widget.onPriorityChanged?.call(priority);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      buildWhen: (previous, current) {
        // Only rebuild when difficulty/priority changes
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.difficulty != current.difficulty ||
                 previous.priority != current.priority ||
                 previous.basePoints != current.basePoints;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: widget.padding ?? const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section Header
              Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    size: 24,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Difficulty & Priority',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Set the challenge level and importance to help with point calculation and prioritization.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Difficulty Selection
              _buildDifficultySection(theme, state),
              
              const SizedBox(height: 24),
              
              // Priority Selection
              _buildPrioritySection(theme, state),
              
              // Points Preview
              if (widget.showSuggestedPoints)
                _buildPointsPreview(theme, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDifficultySection(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Difficulty Level *',
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Difficulty Cards
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: QuestDifficulty.values.map((difficulty) {
            final isSelected = state.difficulty == difficulty;
            return AnimatedScale(
              scale: isSelected && _showPointsAnimation ? _scaleAnimation.value : 1.0,
              duration: const Duration(milliseconds: 300),
              child: _buildDifficultyCard(theme, difficulty, isSelected),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 12),
        
        // Difficulty Description
        SlideTransition(
          position: _slideAnimation,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getDifficultyColor(state.difficulty).withValues(alpha: 0.1),
              border: Border.all(
                color: _getDifficultyColor(state.difficulty).withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  _getDifficultyIcon(state.difficulty),
                  size: 20,
                  color: _getDifficultyColor(state.difficulty),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getDifficultyDescription(state.difficulty),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getDifficultyGuidance(state.difficulty),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDifficultyCard(ThemeData theme, QuestDifficulty difficulty, bool isSelected) {
    final color = _getDifficultyColor(difficulty);
    final suggestedPoints = _getSuggestedPointsForDifficulty(difficulty);
    
    return InkWell(
      onTap: () => _onDifficultySelected(difficulty),
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.15) : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : theme.colorScheme.outline.withValues(alpha: 0.5),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Difficulty Icon with Stars
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getDifficultyIcon(difficulty),
                  size: 24,
                  color: isSelected ? color : theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                ...List.generate(
                  _getDifficultyStars(difficulty),
                  (index) => Icon(
                    Icons.star,
                    size: 12,
                    color: isSelected ? color : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Text(
              _getDifficultyName(difficulty),
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? color : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 4),
            
            Text(
              '${suggestedPoints}pts',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: isSelected ? color : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySection(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority Level *',
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Priority Chips
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: QuestPriority.values.map((priority) {
            final isSelected = state.priority == priority;
            return FilterChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getPriorityIcon(priority),
                    size: 16,
                    color: isSelected 
                      ? theme.colorScheme.onPrimary 
                      : _getPriorityColor(priority),
                  ),
                  const SizedBox(width: 4),
                  Text(_getPriorityName(priority)),
                ],
              ),
              selected: isSelected,
              onSelected: (_) => _onPrioritySelected(priority),
              selectedColor: _getPriorityColor(priority),
              checkmarkColor: theme.colorScheme.onPrimary,
              labelStyle: TextStyle(
                color: isSelected 
                  ? theme.colorScheme.onPrimary 
                  : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPointsPreview(ThemeData theme, QuestCreationInProgress state) {
    final totalPoints = state.calculatedTotalPoints;
    final difficultyMultiplier = _getDifficultyMultiplier(state.difficulty);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            theme.colorScheme.secondaryContainer.withValues(alpha: 0.3),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.stars_rounded,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Points Calculation',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Text(
                  '$totalPoints pts',
                  key: ValueKey(totalPoints),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Calculation Breakdown
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Base Points:',
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      '${state.basePoints}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                
                if (state.bonusPoints > 0) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Bonus Points:',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        '+${state.bonusPoints}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                ],
                
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Difficulty Multiplier:',
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      '×${difficultyMultiplier.toStringAsFixed(1)}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: _getDifficultyColor(state.difficulty),
                      ),
                    ),
                  ],
                ),
                
                const Divider(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Points:',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '$totalPoints',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for difficulty
  Color _getDifficultyColor(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return Colors.green;
      case QuestDifficulty.intermediate:
        return Colors.orange;
      case QuestDifficulty.advanced:
        return Colors.red;
      case QuestDifficulty.expert:
        return Colors.purple;
      case QuestDifficulty.master:
        return Colors.indigo;
    }
  }

  IconData _getDifficultyIcon(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return Icons.sentiment_satisfied;
      case QuestDifficulty.intermediate:
        return Icons.trending_up;
      case QuestDifficulty.advanced:
        return Icons.local_fire_department;
      case QuestDifficulty.expert:
        return Icons.military_tech;
      case QuestDifficulty.master:
        return Icons.emoji_events;
    }
  }

  String _getDifficultyName(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 'Beginner';
      case QuestDifficulty.intermediate:
        return 'Intermediate';
      case QuestDifficulty.advanced:
        return 'Advanced';
      case QuestDifficulty.expert:
        return 'Expert';
      case QuestDifficulty.master:
        return 'Master';
    }
  }

  String _getDifficultyDescription(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 'Perfect for getting started';
      case QuestDifficulty.intermediate:
        return 'Moderate challenge with good rewards';
      case QuestDifficulty.advanced:
        return 'Significant effort required';
      case QuestDifficulty.expert:
        return 'High skill and dedication needed';
      case QuestDifficulty.master:
        return 'Ultimate challenge for experts';
    }
  }

  String _getDifficultyGuidance(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 'Can be completed in a few hours with basic knowledge';
      case QuestDifficulty.intermediate:
        return 'Requires some planning and moderate time investment';
      case QuestDifficulty.advanced:
        return 'Needs careful planning and sustained effort over time';
      case QuestDifficulty.expert:
        return 'Demands specialized skills and significant commitment';
      case QuestDifficulty.master:
        return 'Reserved for the most challenging and complex goals';
    }
  }

  int _getDifficultyStars(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 1;
      case QuestDifficulty.intermediate:
        return 2;
      case QuestDifficulty.advanced:
        return 3;
      case QuestDifficulty.expert:
        return 4;
      case QuestDifficulty.master:
        return 5;
    }
  }

  int _getSuggestedPointsForDifficulty(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 50;
      case QuestDifficulty.intermediate:
        return 100;
      case QuestDifficulty.advanced:
        return 200;
      case QuestDifficulty.expert:
        return 350;
      case QuestDifficulty.master:
        return 500;
    }
  }

  double _getDifficultyMultiplier(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 1.0;
      case QuestDifficulty.intermediate:
        return 1.5;
      case QuestDifficulty.advanced:
        return 2.0;
      case QuestDifficulty.expert:
        return 2.5;
      case QuestDifficulty.master:
        return 3.0;
    }
  }

  // Helper methods for priority
  Color _getPriorityColor(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Colors.blue;
      case QuestPriority.medium:
        return Colors.orange;
      case QuestPriority.high:
        return Colors.red;
      case QuestPriority.urgent:
        return Colors.deepOrange;
    }
  }

  IconData _getPriorityIcon(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return Icons.low_priority;
      case QuestPriority.medium:
        return Icons.remove;
      case QuestPriority.high:
        return Icons.priority_high;
      case QuestPriority.urgent:
        return Icons.warning;
    }
  }

  String _getPriorityName(QuestPriority priority) {
    switch (priority) {
      case QuestPriority.low:
        return 'Low';
      case QuestPriority.medium:
        return 'Medium';
      case QuestPriority.high:
        return 'High';
      case QuestPriority.urgent:
        return 'Urgent';
    }
  }
}