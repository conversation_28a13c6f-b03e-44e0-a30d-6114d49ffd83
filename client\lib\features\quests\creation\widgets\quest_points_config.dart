import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../models/quest_creation_validation.dart';

/// Widget for configuring quest points and rewards system
class QuestPointsConfig extends StatefulWidget {
  /// Callback when points configuration changes
  final VoidCallback? onChanged;
  
  /// Whether to show detailed point calculation hints
  final bool showCalculationHints;
  
  /// Custom padding for the configuration section
  final EdgeInsetsGeometry? padding;

  const QuestPointsConfig({
    super.key,
    this.onChanged,
    this.showCalculationHints = true,
    this.padding,
  });

  @override
  State<QuestPointsConfig> createState() => _QuestPointsConfigState();
}

class _QuestPointsConfigState extends State<QuestPointsConfig> with TickerProviderStateMixin {
  late TextEditingController _basePointsController;
  late TextEditingController _bonusPointsController;
  late FocusNode _basePointsFocusNode;
  late FocusNode _bonusPointsFocusNode;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Validation state
  QuestPoints _basePointsValidation = const QuestPoints.pure();
  QuestPoints _bonusPointsValidation = const QuestPoints.pure();
  bool _basePointsTouched = false;
  bool _bonusPointsTouched = false;
  
  // UI state
  bool _isExpanded = true;
  bool _showAdvancedOptions = false;
  String _selectedDistribution = 'Equal Distribution';

  @override
  void initState() {
    super.initState();
    _basePointsController = TextEditingController();
    _bonusPointsController = TextEditingController();
    _basePointsFocusNode = FocusNode();
    _bonusPointsFocusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();

    // Listen for focus changes to track field interactions
    _basePointsFocusNode.addListener(() {
      if (!_basePointsFocusNode.hasFocus && !_basePointsTouched) {
        setState(() {
          _basePointsTouched = true;
        });
      }
    });
    
    _bonusPointsFocusNode.addListener(() {
      if (!_bonusPointsFocusNode.hasFocus && !_bonusPointsTouched) {
        setState(() {
          _bonusPointsTouched = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _basePointsController.dispose();
    _bonusPointsController.dispose();
    _basePointsFocusNode.dispose();
    _bonusPointsFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocConsumer<QuestCreationBloc, QuestCreationState>(
      listenWhen: (previous, current) {
        // Listen for state changes that affect points configuration
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.basePoints != current.basePoints ||
                 previous.bonusPoints != current.bonusPoints ||
                 previous.difficulty != current.difficulty;
        }
        return false;
      },
      listener: (context, state) {
        if (state is QuestCreationInProgress) {
          // Update controllers when state changes (e.g., from difficulty selection)
          if (_basePointsController.text != state.basePoints.toString()) {
            _basePointsController.text = state.basePoints.toString();
          }
          if (_bonusPointsController.text != state.bonusPoints.toString()) {
            _bonusPointsController.text = state.bonusPoints.toString();
          }
        }
      },
      buildWhen: (previous, current) {
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.basePoints != current.basePoints ||
                 previous.bonusPoints != current.bonusPoints ||
                 previous.difficulty != current.difficulty ||
                 previous.validationStatus != current.validationStatus ||
                 previous.fieldErrors != current.fieldErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            padding: widget.padding ?? const EdgeInsets.all(16.0),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(theme, state),
                    const SizedBox(height: 16),
                    if (_isExpanded) ...[
                      _buildPointsConfiguration(theme, state),
                      const SizedBox(height: 16),
                      _buildPointsCalculation(theme, state),
                      if (widget.showCalculationHints) ...[
                        const SizedBox(height: 12),
                        _buildCalculationHints(theme, state),
                      ],
                      const SizedBox(height: 16),
                      _buildAdvancedOptions(theme, state),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, QuestCreationInProgress state) {
    return Row(
      children: [
        Icon(
          Icons.star_rounded,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Points & Rewards',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Configure point values and bonus rewards for quest completion',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: AnimatedRotation(
            turns: _isExpanded ? 0.5 : 0,
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.expand_more),
          ),
        ),
      ],
    );
  }

  Widget _buildPointsConfiguration(ThemeData theme, QuestCreationInProgress state) {
    final basePointsError = _getFieldError('basePoints', state);
    final bonusPointsError = _getFieldError('bonusPoints', state);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildPointsField(
                controller: _basePointsController,
                focusNode: _basePointsFocusNode,
                label: 'Base Points',
                hint: 'Points awarded for quest completion',
                icon: Icons.star_outline_rounded,
                validation: _basePointsValidation,
                touched: _basePointsTouched,
                error: basePointsError,
                onChanged: (value) {
                  final points = int.tryParse(value) ?? 0;
                  _updateBasePointsValidation(points);
                  context.read<QuestCreationBloc>().add(
                    UpdatePointsConfig(basePoints: points),
                  );
                  widget.onChanged?.call();
                },
                theme: theme,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPointsField(
                controller: _bonusPointsController,
                focusNode: _bonusPointsFocusNode,
                label: 'Bonus Points',
                hint: 'Additional points for early completion',
                icon: Icons.star_rounded,
                validation: _bonusPointsValidation,
                touched: _bonusPointsTouched,
                error: bonusPointsError,
                onChanged: (value) {
                  final points = int.tryParse(value) ?? 0;
                  _updateBonusPointsValidation(points);
                  context.read<QuestCreationBloc>().add(
                    UpdatePointsConfig(bonusPoints: points),
                  );
                  widget.onChanged?.call();
                },
                theme: theme,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPointsField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required String hint,
    required IconData icon,
    required QuestPoints validation,
    required bool touched,
    required String? error,
    required ValueChanged<String> onChanged,
    required ThemeData theme,
  }) {
    final hasError = touched && (validation.isNotValid || error != null);
    final errorText = error ?? (validation.isNotValid ? validation.error?.message : null);

    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(4),
      ],
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        suffixText: 'pts',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        errorText: hasError ? errorText : null,
        helperText: !hasError ? _getPointsHelperText(label) : null,
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildPointsCalculation(ThemeData theme, QuestCreationInProgress state) {
    final totalPoints = state.basePoints + state.bonusPoints;
    final difficultyMultiplier = _getDifficultyMultiplier(state.difficulty);
    final finalPoints = (totalPoints * difficultyMultiplier).round();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate_rounded,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Points Calculation',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildCalculationRow(
            'Base Points',
            '${state.basePoints} pts',
            theme,
          ),
          _buildCalculationRow(
            'Bonus Points',
            '${state.bonusPoints} pts',
            theme,
          ),
          _buildCalculationRow(
            'Difficulty Multiplier',
            '${difficultyMultiplier.toStringAsFixed(1)}x (${state.difficulty.name})',
            theme,
            isMultiplier: true,
          ),
          const Divider(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Points',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$finalPoints pts',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    String value,
    ThemeData theme, {
    bool isMultiplier = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isMultiplier ? FontWeight.w600 : FontWeight.w500,
              color: isMultiplier ? theme.colorScheme.primary : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationHints(ThemeData theme, QuestCreationInProgress state) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline_rounded,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                'Point Recommendations',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ..._getPointRecommendations(state).map((recommendation) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAdvancedOptions(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextButton.icon(
          onPressed: () {
            setState(() {
              _showAdvancedOptions = !_showAdvancedOptions;
            });
          },
          icon: AnimatedRotation(
            turns: _showAdvancedOptions ? 0.25 : 0,
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.chevron_right_rounded),
          ),
          label: Text(
            'Advanced Options',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          child: _showAdvancedOptions
              ? _buildAdvancedOptionsContent(theme, state)
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildAdvancedOptionsContent(ThemeData theme, QuestCreationInProgress state) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.only(top: 8.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reward Distribution',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          _buildRewardOption(
            'Equal Distribution',
            'Points split equally among all participants',
            true,
            theme,
          ),
          _buildRewardOption(
            'Contribution Based',
            'Points awarded based on individual contribution',
            false,
            theme,
          ),
          _buildRewardOption(
            'Achievement Based',
            'Bonus points for meeting specific milestones',
            false,
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildRewardOption(
    String title,
    String description,
    bool isSelected,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: InkWell(
        borderRadius: BorderRadius.circular(8.0),
        onTap: () {
          _showRewardDistributionDialog(context, title);
        },
        child: Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: isSelected 
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: isSelected 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Radio<String>(
                value: title,
                groupValue: _selectedDistribution,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedDistribution = value;
                    });
                  }
                },
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  String? _getFieldError(String fieldName, QuestCreationInProgress state) {
    return state.fieldErrors[fieldName];
  }

  String? _getPointsHelperText(String label) {
    switch (label) {
      case 'Base Points':
        return 'Standard completion reward';
      case 'Bonus Points':
        return 'Early completion bonus';
      default:
        return null;
    }
  }

  double _getDifficultyMultiplier(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 1.0;
      case QuestDifficulty.intermediate:
        return 1.2;
      case QuestDifficulty.advanced:
        return 1.5;
      case QuestDifficulty.expert:
        return 1.8;
      case QuestDifficulty.master:
        return 2.0;
    }
  }

  List<String> _getPointRecommendations(QuestCreationInProgress state) {
    final recommendations = <String>[];
    
    switch (state.difficulty) {
      case QuestDifficulty.beginner:
        recommendations.add('Beginner quests: 25-75 base points');
        break;
      case QuestDifficulty.intermediate:
        recommendations.add('Intermediate quests: 75-150 base points');
        break;
      case QuestDifficulty.advanced:
        recommendations.add('Advanced quests: 150-300 base points');
        break;
      case QuestDifficulty.expert:
        recommendations.add('Expert quests: 300-500 base points');
        break;
      case QuestDifficulty.master:
        recommendations.add('Master quests: 500+ base points');
        break;
    }

    recommendations.add('Bonus points: 10-25% of base points');
    recommendations.add('Consider quest duration when setting points');
    
    return recommendations;
  }

  void _updateBasePointsValidation(int value) {
    final newValidation = QuestPoints.dirty(value);
    if (newValidation != _basePointsValidation) {
      setState(() {
        _basePointsValidation = newValidation;
      });
    }
  }

  void _updateBonusPointsValidation(int value) {
    final newValidation = QuestPoints.dirty(value);
    if (newValidation != _bonusPointsValidation) {
      setState(() {
        _bonusPointsValidation = newValidation;
      });
    }
  }

  /// Show reward distribution configuration dialog
  void _showRewardDistributionDialog(BuildContext context, String distributionType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Configure $distributionType'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Configure how points will be distributed for $distributionType'),
              const SizedBox(height: 16),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Base Points',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: '100',
              ),
              const SizedBox(height: 12),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Bonus Multiplier',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: '1.5',
              ),
              const SizedBox(height: 12),
              SwitchListTile(
                title: const Text('Enable Performance Bonus'),
                value: true,
                onChanged: (value) {},
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$distributionType configuration saved'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}