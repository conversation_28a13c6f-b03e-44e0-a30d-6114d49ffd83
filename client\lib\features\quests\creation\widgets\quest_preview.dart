import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_state.dart';

/// Widget for real-time preview of the quest being created with gamification elements
class QuestPreview extends StatelessWidget {
  const QuestPreview({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPreviewCard(context, state),
            const SizedBox(height: 16),
            _buildValidationSummary(context, state),
          ],
        );
      },
    );
  }

  Widget _buildPreviewCard(BuildContext context, QuestCreationInProgress state) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and difficulty
            Row(
              children: [
                _buildCategoryChip(context, state.category),
                const SizedBox(width: 8),
                _buildDifficultyChip(context, state.difficulty),
                const Spacer(),
                _buildPointsBadge(context, state.totalPoints),
              ],
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              state.title.isEmpty ? 'Quest Title' : state.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: state.title.isEmpty
                    ? Theme.of(context).colorScheme.onSurfaceVariant
                    : null,
                fontStyle: state.title.isEmpty ? FontStyle.italic : null,
              ),
            ),
            const SizedBox(height: 8),

            // Description
            Text(
              state.description.isEmpty ? 'Quest description will appear here...' : state.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: state.description.isEmpty
                    ? Theme.of(context).colorScheme.onSurfaceVariant
                    : null,
                fontStyle: state.description.isEmpty ? FontStyle.italic : null,
              ),
            ),
            const SizedBox(height: 16),

            // Tasks section
            _buildTasksPreview(context, state),

            // Scheduling info
            if (state.deadline != null || state.estimatedHours != null) ...[
              const SizedBox(height: 16),
              _buildSchedulingInfo(context, state),
            ],

            // Tags
            if (state.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildTagsPreview(context, state),
            ],

            // Settings summary
            const SizedBox(height: 16),
            _buildSettingsSummary(context, state),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(BuildContext context, QuestCategory category) {
    return Chip(
      label: Text(
        category.name.toUpperCase(),
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
      ),
      backgroundColor: _getCategoryColor(category).withValues(alpha: 0.2),
      side: BorderSide(color: _getCategoryColor(category)),
    );
  }

  Widget _buildDifficultyChip(BuildContext context, QuestDifficulty difficulty) {
    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            size: 16,
            color: _getDifficultyColor(difficulty),
          ),
          const SizedBox(width: 4),
          Text(
            difficulty.name.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: _getDifficultyColor(difficulty),
            ),
          ),
        ],
      ),
      backgroundColor: _getDifficultyColor(difficulty).withValues(alpha: 0.2),
      side: BorderSide(color: _getDifficultyColor(difficulty)),
    );
  }

  Widget _buildPointsBadge(BuildContext context, int points) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.stars,
            size: 16,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
          const SizedBox(width: 4),
          Text(
            '$points pts',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksPreview(BuildContext context, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tasks',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (state.tasks.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.task_alt_outlined,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  'No tasks added yet',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          )
        else
          ...state.tasks.asMap().entries.map((entry) {
            final index = entry.key;
            final task = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      task.title,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            );
          }),
      ],
    );
  }

  Widget _buildSchedulingInfo(BuildContext context, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Scheduling',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (state.deadline != null)
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                'Due: ${_formatDate(state.deadline!)}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        if (state.estimatedHours != null) ...[
          if (state.deadline != null) const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.timer,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                'Estimated: ${state.estimatedHours} hours',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildTagsPreview(BuildContext context, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: state.tags.map((tag) => Chip(
            label: Text(
              tag,
              style: const TextStyle(fontSize: 12),
            ),
            backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildSettingsSummary(BuildContext context, QuestCreationInProgress state) {
    final settings = <String>[];

    if (state.isPrivate) settings.add('Private');
    if (state.allowCollaboration) settings.add('Collaborative');
    if (state.enableNotifications) settings.add('Notifications enabled');

    if (settings.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Settings',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: settings.map((setting) => Chip(
            label: Text(
              setting,
              style: const TextStyle(fontSize: 12),
            ),
            backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildValidationSummary(BuildContext context, QuestCreationInProgress state) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check for errors
    if (state.title.trim().isEmpty) errors.add('Title is required');
    if (state.description.trim().isEmpty) errors.add('Description is required');
    if (state.tasks.isEmpty) warnings.add('Consider adding tasks to break down your quest');

    if (errors.isEmpty && warnings.isEmpty) {
      return Card(
        color: Colors.green.withValues(alpha: 0.1),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
              ),
              const SizedBox(width: 8),
              Text(
                'Quest is ready to be created!',
                style: TextStyle(
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        if (errors.isNotEmpty)
          Card(
            color: Colors.red.withValues(alpha: 0.1),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.error,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Issues to fix:',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...errors.map((error) => Padding(
                    padding: const EdgeInsets.only(left: 32, bottom: 4),
                    child: Text(
                      '• $error',
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  )),
                ],
              ),
            ),
          ),
        if (warnings.isNotEmpty) ...[
          if (errors.isNotEmpty) const SizedBox(height: 8),
          Card(
            color: Colors.orange.withValues(alpha: 0.1),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Suggestions:',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...warnings.map((warning) => Padding(
                    padding: const EdgeInsets.only(left: 32, bottom: 4),
                    child: Text(
                      '• $warning',
                      style: TextStyle(color: Colors.orange.shade700),
                    ),
                  )),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Helper methods
  Color _getCategoryColor(QuestCategory category) {
    switch (category) {
      case QuestCategory.work:
        return Colors.blue;
      case QuestCategory.personal:
        return Colors.green;
      case QuestCategory.learning:
        return Colors.purple;
      case QuestCategory.health:
        return Colors.red;
      case QuestCategory.social:
        return Colors.orange;
      case QuestCategory.creative:
        return Colors.pink;
      case QuestCategory.productivity:
        return Colors.indigo;
      case QuestCategory.other:
        return Colors.grey;
    }
  }

  Color _getDifficultyColor(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return Colors.green;
      case QuestDifficulty.intermediate:
        return Colors.blue;
      case QuestDifficulty.advanced:
        return Colors.orange;
      case QuestDifficulty.expert:
        return Colors.red;
      case QuestDifficulty.master:
        return Colors.purple;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}