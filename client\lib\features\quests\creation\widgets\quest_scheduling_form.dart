import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../models/quest_creation_validation.dart';

/// Widget for setting quest schedules, deadlines, and time management
class QuestSchedulingForm extends StatefulWidget {
  /// Callback when scheduling configuration changes
  final VoidCallback? onChanged;
  
  /// Whether to show time zone information
  final bool showTimeZone;
  
  /// Custom padding for the form
  final EdgeInsetsGeometry? padding;

  const QuestSchedulingForm({
    super.key,
    this.onChanged,
    this.showTimeZone = true,
    this.padding,
  });

  @override
  State<QuestSchedulingForm> createState() => _QuestSchedulingFormState();
}

class _QuestSchedulingFormState extends State<QuestSchedulingForm> with TickerProviderStateMixin {
  late TextEditingController _estimatedHoursController;
  late FocusNode _estimatedHoursFocusNode;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  // Validation state
  QuestEstimatedHours _estimatedHoursValidation = const QuestEstimatedHours.pure();
  bool _estimatedHoursTouched = false;
  
  // Date and time state
  DateTime? _startDate;
  TimeOfDay? _startTime;
  DateTime? _deadline;
  TimeOfDay? _deadlineTime;
  
  // UI state
  bool _isExpanded = true;
  bool _showAdvancedScheduling = false;
  bool _enableRecurrence = false;
  bool _enableTimeTracking = false;
  RecurrenceType _recurrenceType = RecurrenceType.none;

  @override
  void initState() {
    super.initState();
    _estimatedHoursController = TextEditingController();
    _estimatedHoursFocusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();

    // Listen for focus changes
    _estimatedHoursFocusNode.addListener(() {
      if (!_estimatedHoursFocusNode.hasFocus && !_estimatedHoursTouched) {
        setState(() {
          _estimatedHoursTouched = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _estimatedHoursController.dispose();
    _estimatedHoursFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocConsumer<QuestCreationBloc, QuestCreationState>(
      listenWhen: (previous, current) {
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.startDate != current.startDate ||
                 previous.deadline != current.deadline ||
                 previous.estimatedHours != current.estimatedHours;
        }
        return false;
      },
      listener: (context, state) {
        if (state is QuestCreationInProgress) {
          // Update local state when BLoC state changes
          if (_estimatedHoursController.text != state.estimatedHours.toString()) {
            _estimatedHoursController.text = state.estimatedHours.toString();
          }
          
          _startDate = state.startDate;
          _deadline = state.deadline;
        }
      },
      buildWhen: (previous, current) {
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.startDate != current.startDate ||
                 previous.deadline != current.deadline ||
                 previous.estimatedHours != current.estimatedHours ||
                 previous.validationStatus != current.validationStatus ||
                 previous.fieldErrors != current.fieldErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return SlideTransition(
          position: _slideAnimation.drive(
            Tween<Offset>(
              begin: const Offset(0.0, 0.3),
              end: Offset.zero,
            ),
          ),
          child: FadeTransition(
            opacity: _slideAnimation,
            child: Container(
              padding: widget.padding ?? const EdgeInsets.all(16.0),
              child: Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(theme, state),
                      const SizedBox(height: 16),
                      if (_isExpanded) ...[
                        _buildDateTimeSection(theme, state),
                        const SizedBox(height: 20),
                        _buildEstimatedHours(theme, state),
                        const SizedBox(height: 16),
                        _buildTimeManagement(theme, state),
                        const SizedBox(height: 16),
                        _buildAdvancedScheduling(theme, state),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, QuestCreationInProgress state) {
    return Row(
      children: [
        Icon(
          Icons.schedule_rounded,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Schedule & Timing',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Set start date, deadline, and time management preferences',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: AnimatedRotation(
            turns: _isExpanded ? 0.5 : 0,
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.expand_more),
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeSection(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dates & Times',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDateTimeField(
                label: 'Start Date',
                icon: Icons.event_available_rounded,
                date: _startDate ?? DateTime.now(),
                time: _startTime,
                onDateChanged: (date) {
                  setState(() {
                    _startDate = date;
                  });
                  context.read<QuestCreationBloc>().add(
                    UpdateScheduling(startDate: date),
                  );
                  widget.onChanged?.call();
                },
                onTimeChanged: (time) {
                  setState(() {
                    _startTime = time;
                  });
                  widget.onChanged?.call();
                },
                theme: theme,
                isRequired: false,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateTimeField(
                label: 'Deadline',
                icon: Icons.event_busy_rounded,
                date: _deadline,
                time: _deadlineTime,
                onDateChanged: (date) {
                  setState(() {
                    _deadline = date;
                  });
                  context.read<QuestCreationBloc>().add(
                    UpdateScheduling(deadline: date),
                  );
                  widget.onChanged?.call();
                },
                onTimeChanged: (time) {
                  setState(() {
                    _deadlineTime = time;
                  });
                  widget.onChanged?.call();
                },
                theme: theme,
                isRequired: true,
                isDeadline: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateTimeField({
    required String label,
    required IconData icon,
    required DateTime? date,
    required TimeOfDay? time,
    required ValueChanged<DateTime> onDateChanged,
    required ValueChanged<TimeOfDay> onTimeChanged,
    required ThemeData theme,
    required bool isRequired,
    bool isDeadline = false,
  }) {
    final hasDate = date != null;
    final dateText = hasDate 
        ? '${date.day}/${date.month}/${date.year}' 
        : 'Select date';
    
    final hasTime = time != null;
    final timeText = hasTime 
        ? time.format(context) 
        : 'Set time';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isRequired ? ' *' : ''),
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: isRequired ? theme.colorScheme.primary : theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          borderRadius: BorderRadius.circular(12.0),
          onTap: () => _selectDate(onDateChanged, initialDate: date),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(12.0),
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: hasDate 
                      ? theme.colorScheme.primary 
                      : theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    dateText,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: hasDate 
                          ? theme.colorScheme.onSurface 
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                if (hasDate)
                  IconButton(
                    onPressed: () => _selectTime(onTimeChanged, initialTime: time),
                    icon: Icon(
                      Icons.access_time_rounded,
                      size: 16,
                      color: hasTime 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
        ),
        if (hasDate && hasTime) ...[
          const SizedBox(height: 4),
          Text(
            'Time: $timeText',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
        if (isDeadline && hasDate) ...[
          const SizedBox(height: 4),
          _buildDeadlineInfo(theme, date),
        ],
      ],
    );
  }

  Widget _buildDeadlineInfo(ThemeData theme, DateTime deadline) {
    final now = DateTime.now();
    final difference = deadline.difference(now);
    final days = difference.inDays;
    
    Color infoColor;
    String infoText;
    IconData infoIcon;

    if (days < 0) {
      infoColor = theme.colorScheme.error;
      infoText = 'Overdue by ${-days} days';
      infoIcon = Icons.error_outline_rounded;
    } else if (days == 0) {
      infoColor = theme.colorScheme.error;
      infoText = 'Due today';
      infoIcon = Icons.warning_amber_rounded;
    } else if (days <= 3) {
      infoColor = theme.colorScheme.tertiary;
      infoText = 'Due in $days days';
      infoIcon = Icons.schedule_rounded;
    } else {
      infoColor = theme.colorScheme.primary;
      infoText = 'Due in $days days';
      infoIcon = Icons.check_circle_outline_rounded;
    }

    return Row(
      children: [
        Icon(
          infoIcon,
          size: 14,
          color: infoColor,
        ),
        const SizedBox(width: 4),
        Text(
          infoText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: infoColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildEstimatedHours(ThemeData theme, QuestCreationInProgress state) {
    final error = _getFieldError('estimatedHours', state);
    final hasError = _estimatedHoursTouched && (_estimatedHoursValidation.isNotValid || error != null);
    final errorText = error ?? (_estimatedHoursValidation.isNotValid ? _estimatedHoursValidation.error?.message : null);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Time Estimation',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _estimatedHoursController,
                focusNode: _estimatedHoursFocusNode,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                  LengthLimitingTextInputFormatter(5),
                ],
                decoration: InputDecoration(
                  labelText: 'Estimated Hours',
                  hintText: 'How long will this take?',
                  prefixIcon: const Icon(Icons.schedule_rounded),
                  suffixText: 'hrs',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  errorText: hasError ? errorText : null,
                  helperText: !hasError ? 'Rough time estimate' : null,
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                ),
                onChanged: (value) {
                  final hours = double.tryParse(value) ?? 0.0;
                  _updateEstimatedHoursValidation(value);
                  context.read<QuestCreationBloc>().add(
                    UpdateScheduling(estimatedHours: hours.toInt()),
                  );
                  widget.onChanged?.call();
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 3,
              child: _buildTimePresets(theme),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimePresets(ThemeData theme) {
    final presets = [
      {'label': '30min', 'value': 0.5},
      {'label': '1hr', 'value': 1.0},
      {'label': '2hrs', 'value': 2.0},
      {'label': '1 day', 'value': 8.0},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Presets',
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8.0,
          children: presets.map((preset) {
            return ActionChip(
              label: Text(preset['label'] as String),
              onPressed: () {
                final value = preset['value'] as double;
                _estimatedHoursController.text = value.toString();
                context.read<QuestCreationBloc>().add(
                  UpdateScheduling(estimatedHours: value.toInt()),
                );
                widget.onChanged?.call();
              },
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTimeManagement(ThemeData theme, QuestCreationInProgress state) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timer_rounded,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Time Management',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTimeManagementOption(
            'Break tasks into time blocks',
            'Divide work into focused time segments',
            true,
            theme,
          ),
          _buildTimeManagementOption(
            'Enable progress reminders',
            'Get notified about deadlines and milestones',
            false,
            theme,
          ),
          _buildTimeManagementOption(
            'Track actual time spent',
            'Monitor how much time you actually spend vs. estimate',
            _enableTimeTracking,
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeManagementOption(
    String title,
    String description,
    bool isSelected,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (value) {
              setState(() {
                _enableTimeTracking = value ?? false;
              });
            },
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedScheduling(ThemeData theme, QuestCreationInProgress state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextButton.icon(
          onPressed: () {
            setState(() {
              _showAdvancedScheduling = !_showAdvancedScheduling;
            });
          },
          icon: AnimatedRotation(
            turns: _showAdvancedScheduling ? 0.25 : 0,
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.chevron_right_rounded),
          ),
          label: Text(
            'Advanced Scheduling',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          child: _showAdvancedScheduling
              ? _buildAdvancedSchedulingContent(theme, state)
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildAdvancedSchedulingContent(ThemeData theme, QuestCreationInProgress state) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.only(top: 8.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SwitchListTile(
            title: Text(
              'Enable Recurrence',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              'Repeat this quest on a schedule',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            value: _enableRecurrence,
            onChanged: (value) {
              setState(() {
                _enableRecurrence = value;
              });
            },
            contentPadding: EdgeInsets.zero,
          ),
          if (_enableRecurrence) ...[
            const SizedBox(height: 12),
            _buildRecurrenceOptions(theme),
          ],
          if (widget.showTimeZone) ...[
            const SizedBox(height: 16),
            _buildTimeZoneInfo(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildRecurrenceOptions(ThemeData theme) {
    final recurrenceOptions = [
      RecurrenceType.daily,
      RecurrenceType.weekly,
      RecurrenceType.monthly,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Repeat Frequency',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8.0,
          children: recurrenceOptions.map((recurrence) {
            final isSelected = _recurrenceType == recurrence;
            return FilterChip(
              label: Text(_getRecurrenceLabel(recurrence)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _recurrenceType = selected ? recurrence : RecurrenceType.none;
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTimeZoneInfo(ThemeData theme) {
    final timeZone = DateTime.now().timeZoneName;
    
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(
            Icons.public_rounded,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Time Zone: $timeZone',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String? _getFieldError(String fieldName, QuestCreationInProgress state) {
    return state.fieldErrors[fieldName];
  }

  String _getRecurrenceLabel(RecurrenceType type) {
    switch (type) {
      case RecurrenceType.none:
        return 'None';
      case RecurrenceType.daily:
        return 'Daily';
      case RecurrenceType.weekly:
        return 'Weekly';
      case RecurrenceType.monthly:
        return 'Monthly';
    }
  }

  void _updateEstimatedHoursValidation(String value) {
    final newValidation = QuestEstimatedHours.dirty(value);
    if (newValidation != _estimatedHoursValidation) {
      setState(() {
        _estimatedHoursValidation = newValidation;
      });
    }
  }

  Future<void> _selectDate(ValueChanged<DateTime> onChanged, {DateTime? initialDate}) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (selectedDate != null) {
      onChanged(selectedDate);
    }
  }

  Future<void> _selectTime(ValueChanged<TimeOfDay> onChanged, {TimeOfDay? initialTime}) async {
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (selectedTime != null) {
      onChanged(selectedTime);
    }
  }
}

/// Enum for recurrence types
enum RecurrenceType {
  none,
  daily,
  weekly,
  monthly,
}