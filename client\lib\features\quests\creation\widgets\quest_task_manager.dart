import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../models/quest_creation_validation.dart';

/// Advanced widget for managing tasks within a quest
class QuestTaskManager extends StatefulWidget {
  /// Callback when task list changes
  final VoidCallback? onChanged;
  
  /// Whether to show advanced task features
  final bool showAdvancedFeatures;
  
  /// Custom padding for the task manager
  final EdgeInsetsGeometry? padding;

  const QuestTaskManager({
    super.key,
    this.onChanged,
    this.showAdvancedFeatures = true,
    this.padding,
  });

  @override
  State<QuestTaskManager> createState() => _QuestTaskManagerState();
}

class _QuestTaskManagerState extends State<QuestTaskManager> with TickerProviderStateMixin {
  late TextEditingController _taskTitleController;
  late TextEditingController _taskDescriptionController;
  late FocusNode _taskTitleFocusNode;
  late FocusNode _taskDescriptionFocusNode;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  // Validation state
  TaskTitle _taskTitleValidation = const TaskTitle.pure();
  bool _taskTitleTouched = false;
  
  // UI state
  bool _isExpanded = true;
  bool _isAddingTask = false;
  bool _showTaskTemplates = false;
  int? _editingTaskIndex;

  @override
  void initState() {
    super.initState();
    _taskTitleController = TextEditingController();
    _taskDescriptionController = TextEditingController();
    _taskTitleFocusNode = FocusNode();
    _taskDescriptionFocusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();

    // Listen for focus changes
    _taskTitleFocusNode.addListener(() {
      if (!_taskTitleFocusNode.hasFocus && !_taskTitleTouched) {
        setState(() {
          _taskTitleTouched = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _taskTitleController.dispose();
    _taskDescriptionController.dispose();
    _taskTitleFocusNode.dispose();
    _taskDescriptionFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      buildWhen: (previous, current) {
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.tasks != current.tasks ||
                 previous.validationStatus != current.validationStatus ||
                 previous.fieldErrors != current.fieldErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! QuestCreationInProgress) {
          return const SizedBox.shrink();
        }

        return SlideTransition(
          position: _slideAnimation.drive(
            Tween<Offset>(
              begin: const Offset(0.0, 0.3),
              end: Offset.zero,
            ),
          ),
          child: FadeTransition(
            opacity: _slideAnimation,
            child: Container(
              padding: widget.padding ?? const EdgeInsets.all(16.0),
              child: Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(theme, state),
                      const SizedBox(height: 16),
                      if (_isExpanded) ...[
                        _buildTaskList(theme, state),
                        const SizedBox(height: 16),
                        _buildAddTaskSection(theme, state),
                        if (widget.showAdvancedFeatures) ...[
                          const SizedBox(height: 16),
                          _buildAdvancedFeatures(theme, state),
                        ],
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, QuestCreationInProgress state) {
    final taskCount = state.tasks.length;
    final completedTasks = state.tasks.where((t) => t.isCompleted).length;
    
    return Row(
      children: [
        Icon(
          Icons.task_rounded,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Quest Tasks',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (taskCount > 0) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$completedTasks/$taskCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 4),
              Text(
                taskCount == 0 
                    ? 'Break your quest into manageable tasks'
                    : 'Manage and organize quest sub-tasks',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: AnimatedRotation(
            turns: _isExpanded ? 0.5 : 0,
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.expand_more),
          ),
        ),
      ],
    );
  }

  Widget _buildTaskList(ThemeData theme, QuestCreationInProgress state) {
    if (state.tasks.isEmpty) {
      return _buildEmptyState(theme);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tasks (${state.tasks.length})',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: state.tasks.length,
          onReorder: (oldIndex, newIndex) {
            context.read<QuestCreationBloc>().add(
              ReorderTasks(oldIndex: oldIndex, newIndex: newIndex),
            );
            widget.onChanged?.call();
          },
          itemBuilder: (context, index) {
            final task = state.tasks[index];
            return _buildTaskItem(theme, task, index, state);
          },
        ),
      ],
    );
  }

  Widget _buildTaskItem(
    ThemeData theme,
    QuestTaskItem task,
    int index,
    QuestCreationInProgress state,
  ) {
    final isEditing = _editingTaskIndex == index;
    
    return Card(
      key: ValueKey(task.id),
      margin: const EdgeInsets.only(bottom: 8.0),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: isEditing ? 
          _buildTaskEditForm(theme, task, index) :
          _buildTaskDisplay(theme, task, index),
      ),
    );
  }

  Widget _buildTaskDisplay(ThemeData theme, QuestTaskItem task, int index) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Drag handle
        ReorderableDragStartListener(
          index: index,
          child: Container(
            padding: const EdgeInsets.all(4.0),
            child: Icon(
              Icons.drag_handle_rounded,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Checkbox
        Checkbox(
          value: task.isCompleted,
          onChanged: (value) {
            context.read<QuestCreationBloc>().add(
              UpdateTask(
                taskIndex: index,
                isCompleted: value ?? false,
              ),
            );
            widget.onChanged?.call();
          },
        ),
        const SizedBox(width: 12),
        // Task content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                task.title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                  color: task.isCompleted 
                      ? theme.colorScheme.onSurfaceVariant 
                      : theme.colorScheme.onSurface,
                ),
              ),
              if (task.description.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  task.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
              const SizedBox(height: 4),
              Text(
                'Task ${index + 1} of ${context.read<QuestCreationBloc>().state is QuestCreationInProgress ? (context.read<QuestCreationBloc>().state as QuestCreationInProgress).tasks.length : 0}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.outline,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
        // Actions
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () {
                setState(() {
                  _editingTaskIndex = index;
                  _taskTitleController.text = task.title;
                  _taskDescriptionController.text = task.description;
                });
              },
              icon: const Icon(Icons.edit_rounded),
              iconSize: 16,
            ),
            IconButton(
              onPressed: () => _showDeleteConfirmation(context, index),
              icon: const Icon(Icons.delete_rounded),
              iconSize: 16,
              color: theme.colorScheme.error,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTaskEditForm(ThemeData theme, QuestTaskItem task, int index) {
    final error = _getFieldError('taskTitle', context.read<QuestCreationBloc>().state);
    final hasError = _taskTitleTouched && (_taskTitleValidation.isNotValid || error != null);
    final errorText = error ?? (_taskTitleValidation.isNotValid ? _taskTitleValidation.errorMessage : null);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _taskTitleController,
          focusNode: _taskTitleFocusNode,
          decoration: InputDecoration(
            labelText: 'Task Title',
            hintText: 'Enter task title',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            errorText: hasError ? errorText : null,
            isDense: true,
          ),
          onChanged: (value) {
            _updateTaskTitleValidation(value);
          },
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _taskDescriptionController,
          decoration: InputDecoration(
            labelText: 'Description (Optional)',
            hintText: 'Enter task description',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            isDense: true,
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _editingTaskIndex = null;
                  _taskTitleController.clear();
                  _taskDescriptionController.clear();
                });
              },
              child: const Text('Cancel'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => _saveTaskEdit(index),
              child: const Text('Save'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.task_alt_rounded,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'No tasks added yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Break your quest into smaller, manageable tasks.\nTasks help you track progress and stay organized.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _isAddingTask = true;
              });
            },
            icon: const Icon(Icons.add_rounded),
            label: const Text('Add First Task'),
          ),
        ],
      ),
    );
  }

  Widget _buildAddTaskSection(ThemeData theme, QuestCreationInProgress state) {
    if (state.tasks.isEmpty && !_isAddingTask) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!_isAddingTask) ...[
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isAddingTask = true;
                  });
                },
                icon: const Icon(Icons.add_rounded),
                label: const Text('Add Task'),
              ),
              const SizedBox(width: 12),
              if (widget.showAdvancedFeatures)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _showTaskTemplates = !_showTaskTemplates;
                    });
                  },
                  icon: const Icon(Icons.library_books_outlined),
                  label: const Text('Templates'),
                ),
            ],
          ),
          if (_showTaskTemplates) ...[
            const SizedBox(height: 12),
            _buildTaskTemplates(theme),
          ],
        ] else ...[
          _buildAddTaskForm(theme),
        ],
      ],
    );
  }

  Widget _buildAddTaskForm(ThemeData theme) {
    final error = _getFieldError('taskTitle', context.read<QuestCreationBloc>().state);
    final hasError = _taskTitleTouched && (_taskTitleValidation.isNotValid || error != null);
    final errorText = error ?? (_taskTitleValidation.isNotValid ? _taskTitleValidation.errorMessage : null);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Add New Task',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _taskTitleController,
            focusNode: _taskTitleFocusNode,
            decoration: InputDecoration(
              labelText: 'Task Title *',
              hintText: 'What needs to be done?',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              errorText: hasError ? errorText : null,
              prefixIcon: const Icon(Icons.task_alt_rounded),
            ),
            onChanged: (value) {
              _updateTaskTitleValidation(value);
            },
            autofocus: true,
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _taskDescriptionController,
            decoration: InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Add details about this task',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              prefixIcon: const Icon(Icons.description_outlined),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => _cancelAddTask(),
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _taskTitleValidation.isValid ? () => _addTask() : null,
                icon: const Icon(Icons.add_rounded),
                label: const Text('Add Task'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaskTemplates(ThemeData theme) {
    final templates = [
      {'title': 'Research', 'description': 'Gather information and resources'},
      {'title': 'Plan & Design', 'description': 'Create detailed plans and designs'},
      {'title': 'Implementation', 'description': 'Execute the main work'},
      {'title': 'Testing & Review', 'description': 'Test and review the results'},
      {'title': 'Documentation', 'description': 'Document the process and outcomes'},
    ];

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Templates',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: templates.map((template) {
              return ActionChip(
                label: Text(template['title']!),
                onPressed: () {
                  _taskTitleController.text = template['title']!;
                  _taskDescriptionController.text = template['description']!;
                  setState(() {
                    _isAddingTask = true;
                    _showTaskTemplates = false;
                  });
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedFeatures(ThemeData theme, QuestCreationInProgress state) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_rounded,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Advanced Options',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: state.tasks.isNotEmpty ? () => _clearAllTasks() : null,
                  icon: const Icon(Icons.clear_all_rounded),
                  label: const Text('Clear All'),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: theme.colorScheme.error,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: state.tasks.isNotEmpty ? () => _duplicateSelectedTasks() : null,
                  icon: const Icon(Icons.copy_rounded),
                  label: const Text('Bulk Actions'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods
  String? _getFieldError(String fieldName, QuestCreationState state) {
    if (state is QuestCreationInProgress) {
      return state.fieldErrors[fieldName];
    }
    return null;
  }

  void _updateTaskTitleValidation(String value) {
    final newValidation = TaskTitle.dirty(value);
    if (newValidation != _taskTitleValidation) {
      setState(() {
        _taskTitleValidation = newValidation;
      });
    }
  }

  void _addTask() {
    if (_taskTitleValidation.isValid) {
      context.read<QuestCreationBloc>().add(
        AddTask(
          taskTitle: _taskTitleController.text.trim(),
          taskDescription: _taskDescriptionController.text.trim(),
        ),
      );
      _cancelAddTask();
      widget.onChanged?.call();
    }
  }

  void _cancelAddTask() {
    setState(() {
      _isAddingTask = false;
      _taskTitleController.clear();
      _taskDescriptionController.clear();
      _taskTitleValidation = const TaskTitle.pure();
      _taskTitleTouched = false;
    });
  }

  void _saveTaskEdit(int index) {
    if (_taskTitleValidation.isValid) {
      context.read<QuestCreationBloc>().add(
        UpdateTask(
          taskIndex: index,
          title: _taskTitleController.text.trim(),
          description: _taskDescriptionController.text.trim(),
        ),
      );
      setState(() {
        _editingTaskIndex = null;
        _taskTitleController.clear();
        _taskDescriptionController.clear();
      });
      widget.onChanged?.call();
    }
  }

  void _showDeleteConfirmation(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: const Text('Are you sure you want to delete this task? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<QuestCreationBloc>().add(RemoveTask(index));
              widget.onChanged?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _clearAllTasks() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Tasks'),
        content: const Text('Are you sure you want to remove all tasks? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final state = context.read<QuestCreationBloc>().state;
              if (state is QuestCreationInProgress) {
                for (int i = state.tasks.length - 1; i >= 0; i--) {
                  context.read<QuestCreationBloc>().add(RemoveTask(i));
                }
                widget.onChanged?.call();
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _duplicateSelectedTasks() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bulk Actions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate All Tasks'),
              onTap: () {
                Navigator.of(context).pop();
                _performBulkDuplicate();
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('Mark All Complete'),
              onTap: () {
                Navigator.of(context).pop();
                _performBulkComplete(true);
              },
            ),
            ListTile(
              leading: const Icon(Icons.radio_button_unchecked),
              title: const Text('Mark All Incomplete'),
              onTap: () {
                Navigator.of(context).pop();
                _performBulkComplete(false);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Delete All Tasks'),
              onTap: () {
                Navigator.of(context).pop();
                _clearAllTasks();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _performBulkDuplicate() {
    final state = context.read<QuestCreationBloc>().state;
    if (state is QuestCreationInProgress) {
      for (final task in state.tasks) {
        context.read<QuestCreationBloc>().add(
          AddTask(
            taskTitle: '${task.title} (Copy)',
            taskDescription: task.description,
          ),
        );
      }
      widget.onChanged?.call();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Duplicated ${state.tasks.length} tasks'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _performBulkComplete(bool isCompleted) {
    final state = context.read<QuestCreationBloc>().state;
    if (state is QuestCreationInProgress) {
      for (int i = 0; i < state.tasks.length; i++) {
        context.read<QuestCreationBloc>().add(
          UpdateTask(
            taskIndex: i,
            isCompleted: isCompleted,
          ),
        );
      }
      widget.onChanged?.call();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isCompleted
              ? 'Marked ${state.tasks.length} tasks as complete'
              : 'Marked ${state.tasks.length} tasks as incomplete'
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}