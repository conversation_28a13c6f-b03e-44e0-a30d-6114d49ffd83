import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../bloc/quest_creation_bloc.dart';
import '../bloc/quest_creation_event.dart';
import '../bloc/quest_creation_state.dart';
import '../mixins/real_time_validation_mixin.dart';
import '../widgets/validation_error_display.dart';
import '../services/quest_validation_service.dart';

/// Enhanced quest basic info form with real-time validation
class ValidatedQuestBasicInfoForm extends StatefulWidget {
  /// Callback when form values change
  final VoidCallback? onChanged;
  
  /// Whether to show advanced validation hints
  final bool showValidationHints;
  
  /// Whether to show real-time validation feedback
  final bool enableRealTimeValidation;
  
  /// Custom padding for the form
  final EdgeInsetsGeometry? padding;

  const ValidatedQuestBasicInfoForm({
    super.key,
    this.onChanged,
    this.showValidationHints = true,
    this.enableRealTimeValidation = true,
    this.padding,
  });

  @override
  State<ValidatedQuestBasicInfoForm> createState() => _ValidatedQuestBasicInfoFormState();
}

class _ValidatedQuestBasicInfoFormState extends State<ValidatedQuestBasicInfoForm> 
    with RealTimeValidationMixin {
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late FocusNode _titleFocusNode;
  late FocusNode _descriptionFocusNode;
  
  // Category selection state
  QuestCategory? _selectedCategory;

  @override
  bool get validationEnabled => widget.enableRealTimeValidation;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _titleFocusNode = FocusNode();
    _descriptionFocusNode = FocusNode();

    // Listen for BLoC state changes to update form
    _setupStateListener();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  void _setupStateListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<QuestCreationBloc>();
      final currentState = bloc.state;
      
      if (currentState is QuestCreationInProgress) {
        _titleController.text = currentState.title;
        _descriptionController.text = currentState.description;
        _selectedCategory = currentState.category;
        
        // Trigger initial validation
        if (widget.enableRealTimeValidation) {
          triggerValidation();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return buildValidationListener(
      child: Container(
        padding: widget.padding ?? const EdgeInsets.all(16.0),
        child: Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(theme),
                const SizedBox(height: 16),
                
                // Real-time validation status
                if (currentValidationResult != null && widget.enableRealTimeValidation)
                  _buildValidationStatus(theme),
                
                const SizedBox(height: 16),
                
                // Form fields with real-time validation
                _buildTitleField(theme),
                const SizedBox(height: 16),
                
                _buildDescriptionField(theme),
                const SizedBox(height: 16),
                
                _buildCategoryField(theme),
                
                // Validation errors display
                if (currentValidationResult != null)
                  _buildValidationErrorsDisplay(theme),
                
                if (widget.showValidationHints)
                  _buildValidationHints(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.info_rounded,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Basic Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Define your quest\'s core identity and purpose',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        // Progress indicator
        if (currentValidationResult != null) ...[
          const SizedBox(width: 12),
          ValidationStatusIndicator(
            validationSummary: validationService.getValidationSummary(
              context.read<QuestCreationBloc>().state as QuestCreationInProgress? ?? 
              const QuestCreationInProgress(),
            ),
            showStatusText: false,
            showNextSteps: true,
          ),
        ],
      ],
    );
  }

  Widget _buildValidationStatus(ThemeData theme) {
    final summary = validationService.getValidationSummary(
      context.read<QuestCreationBloc>().state as QuestCreationInProgress? ?? 
      const QuestCreationInProgress(),
    );

    return ValidationProgressIndicator(
      validationSummary: summary,
      totalFields: 3, // title, description, category
      validFields: _getValidFieldCount(),
    );
  }

  int _getValidFieldCount() {
    int count = 0;
    if (!hasFieldError('title') && _titleController.text.trim().isNotEmpty) count++;
    if (!hasFieldError('description') && _descriptionController.text.trim().isNotEmpty) count++;
    if (!hasFieldError('category') && _selectedCategory != null) count++;
    return count;
  }

  Widget _buildTitleField(ThemeData theme) {
    return buildValidatedTextField(
      controller: _titleController,
      fieldName: 'title',
      labelText: 'Quest Title *',
      hintText: 'Enter a compelling title for your quest',
      prefixIcon: Icons.title_rounded,
      focusNode: _titleFocusNode,
      maxLength: 100,
      onChanged: (value) {
        context.read<QuestCreationBloc>().add(
          UpdateBasicInfo(title: value),
        );
        widget.onChanged?.call();
      },
    );
  }

  Widget _buildDescriptionField(ThemeData theme) {
    return buildValidatedTextField(
      controller: _descriptionController,
      fieldName: 'description',
      labelText: 'Quest Description *',
      hintText: 'Describe what this quest is about and why it matters',
      prefixIcon: Icons.description_rounded,
      focusNode: _descriptionFocusNode,
      maxLines: 4,
      maxLength: 1000,
      onChanged: (value) {
        context.read<QuestCreationBloc>().add(
          UpdateBasicInfo(description: value),
        );
        widget.onChanged?.call();
      },
    );
  }

  Widget _buildCategoryField(ThemeData theme) {
    return BlocBuilder<QuestCreationBloc, QuestCreationState>(
      buildWhen: (previous, current) {
        if (previous is QuestCreationInProgress && current is QuestCreationInProgress) {
          return previous.category != current.category;
        }
        return true;
      },
      builder: (context, state) {
        if (state is QuestCreationInProgress) {
          _selectedCategory = state.category;
        }

        return buildValidatedDropdownField<QuestCategory>(
          value: _selectedCategory,
          fieldName: 'category',
          labelText: 'Category *',
          hintText: 'Choose a category for your quest',
          prefixIcon: Icons.category_rounded,
          items: QuestCategory.values.map((category) {
            return DropdownMenuItem<QuestCategory>(
              value: category,
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(_getCategoryDisplayName(category)),
                ],
              ),
            );
          }).toList(),
          onChanged: (category) {
            setState(() {
              _selectedCategory = category;
            });
            
            context.read<QuestCreationBloc>().add(
              UpdateBasicInfo(category: category),
            );
            widget.onChanged?.call();
          },
        );
      },
    );
  }

  Widget _buildValidationErrorsDisplay(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: ValidationErrorDisplay(
        validationResult: currentValidationResult!,
        showWarnings: widget.showValidationHints,
        maxErrorsToShow: 3,
      ),
    );
  }

  Widget _buildValidationHints(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: 16.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline_rounded,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                'Tips for Better Quests',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ..._getValidationHints().map((hint) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      hint,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  List<String> _getValidationHints() {
    return [
      'Use action words in your title to make it engaging',
      'Include the "why" in your description to stay motivated',
      'Choose the most specific category to help others discover your quest',
      'Consider what success looks like before you start',
    ];
  }

  IconData _getCategoryIcon(QuestCategory category) {
    switch (category) {
      case QuestCategory.productivity:
        return Icons.work_rounded;
      case QuestCategory.learning:
        return Icons.school_rounded;
      case QuestCategory.health:
        return Icons.favorite_rounded;
      case QuestCategory.creative:
        return Icons.palette_rounded;
      case QuestCategory.social:
        return Icons.people_rounded;
      case QuestCategory.personal:
        return Icons.person_rounded;
      case QuestCategory.work:
        return Icons.interests_rounded;
      case QuestCategory.other:
        return Icons.more_horiz_rounded;
    }
  }

  String _getCategoryDisplayName(QuestCategory category) {
    switch (category) {
      case QuestCategory.productivity:
        return 'Productivity';
      case QuestCategory.learning:
        return 'Learning & Education';
      case QuestCategory.health:
        return 'Health & Fitness';
      case QuestCategory.creative:
        return 'Creative Projects';
      case QuestCategory.social:
        return 'Social & Community';
      case QuestCategory.personal:
        return 'Personal Growth';
      case QuestCategory.work:
        return 'Work & Career';
      case QuestCategory.other:
        return 'Other';
    }
  }
}

/// Floating action button for validation status
class ValidationFloatingActionButton extends StatelessWidget {
  /// The current validation summary
  final ValidationSummary validationSummary;
  
  /// Callback when tapped
  final VoidCallback? onPressed;
  
  /// Whether to show when valid
  final bool showWhenValid;

  const ValidationFloatingActionButton({
    super.key,
    required this.validationSummary,
    this.onPressed,
    this.showWhenValid = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showWhenValid && validationSummary.isValid) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final isValid = validationSummary.isValid;
    
    return FloatingActionButton.extended(
      onPressed: onPressed,
      icon: Icon(
        isValid ? Icons.check_rounded : Icons.error_outline_rounded,
      ),
      label: Text(
        isValid ? 'Continue' : 'Fix Errors (${validationSummary.errorCount})',
      ),
      backgroundColor: isValid ? theme.colorScheme.primary : theme.colorScheme.error,
      foregroundColor: isValid ? theme.colorScheme.onPrimary : theme.colorScheme.onError,
    );
  }
}