import 'package:flutter/material.dart';
import '../services/quest_validation_service.dart';

/// Widget for displaying validation errors and warnings in quest creation forms
class ValidationErrorDisplay extends StatelessWidget {
  /// The validation result to display
  final QuestValidationResult validationResult;
  
  /// Whether to show warnings in addition to errors
  final bool showWarnings;
  
  /// Whether to show field-specific errors
  final bool showFieldErrors;
  
  /// Whether to show general form errors
  final bool showGeneralErrors;
  
  /// Maximum number of errors to display before showing "and X more"
  final int maxErrorsToShow;
  
  /// Custom padding for the display
  final EdgeInsetsGeometry? padding;

  const ValidationErrorDisplay({
    super.key,
    required this.validationResult,
    this.showWarnings = true,
    this.showFieldErrors = true,
    this.showGeneralErrors = true,
    this.maxErrorsToShow = 5,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (validationResult.isValid && 
        (!showWarnings || validationResult.warnings.isEmpty)) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showGeneralErrors && validationResult.generalErrors.isNotEmpty)
            _buildGeneralErrors(theme),
          
          if (showFieldErrors && validationResult.fieldErrors.isNotEmpty)
            _buildFieldErrors(theme),
          
          if (showWarnings && validationResult.warnings.isNotEmpty)
            _buildWarnings(theme),
        ],
      ),
    );
  }

  Widget _buildGeneralErrors(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline_rounded,
                size: 20,
                color: theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                'Form Errors',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationResult.generalErrors.take(maxErrorsToShow).map((error) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      error,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
          if (validationResult.generalErrors.length > maxErrorsToShow)
            Text(
              'and ${validationResult.generalErrors.length - maxErrorsToShow} more...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error.withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFieldErrors(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                size: 20,
                color: theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                'Field Errors',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationResult.fieldErrors.entries.take(maxErrorsToShow).map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                        children: [
                          TextSpan(
                            text: '${_formatFieldName(entry.key)}: ',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          TextSpan(text: entry.value),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
          if (validationResult.fieldErrors.length > maxErrorsToShow)
            Text(
              'and ${validationResult.fieldErrors.length - maxErrorsToShow} more...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error.withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWarnings(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.tertiaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: theme.colorScheme.tertiary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                size: 20,
                color: theme.colorScheme.tertiary,
              ),
              const SizedBox(width: 8),
              Text(
                'Suggestions',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.tertiary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationResult.warnings.take(maxErrorsToShow).map((warning) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.tertiary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      warning,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.tertiary,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
          if (validationResult.warnings.length > maxErrorsToShow)
            Text(
              'and ${validationResult.warnings.length - maxErrorsToShow} more...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.tertiary.withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
    );
  }

  String _formatFieldName(String fieldName) {
    // Convert camelCase to Title Case
    return fieldName
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .trim()
        .split(' ')
        .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}

/// Compact validation status indicator for forms
class ValidationStatusIndicator extends StatelessWidget {
  /// The validation summary to display
  final ValidationSummary validationSummary;
  
  /// Whether to show the status text
  final bool showStatusText;
  
  /// Whether to show the next steps
  final bool showNextSteps;

  const ValidationStatusIndicator({
    super.key,
    required this.validationSummary,
    this.showStatusText = true,
    this.showNextSteps = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = _getStatusColor(theme);
    final icon = _getStatusIcon();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.0),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          if (showStatusText) ...[
            const SizedBox(width: 6),
            Text(
              validationSummary.statusText,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
          if (showNextSteps && validationSummary.nextSteps.isNotEmpty) ...[
            const SizedBox(width: 8),
            Tooltip(
              message: validationSummary.nextSteps.join('\n'),
              child: Icon(
                Icons.help_outline_rounded,
                size: 14,
                color: color.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(ThemeData theme) {
    switch (validationSummary.severity) {
      case ValidationSeverity.none:
        return theme.colorScheme.primary;
      case ValidationSeverity.info:
        return theme.colorScheme.secondary;
      case ValidationSeverity.warning:
        return theme.colorScheme.tertiary;
      case ValidationSeverity.error:
      case ValidationSeverity.critical:
        return theme.colorScheme.error;
    }
  }

  IconData _getStatusIcon() {
    switch (validationSummary.severity) {
      case ValidationSeverity.none:
        return Icons.check_circle_outline_rounded;
      case ValidationSeverity.info:
        return Icons.info_outline_rounded;
      case ValidationSeverity.warning:
        return Icons.warning_amber_rounded;
      case ValidationSeverity.error:
      case ValidationSeverity.critical:
        return Icons.error_outline_rounded;
    }
  }
}

/// Real-time field validation display for individual form fields
class FieldValidationDisplay extends StatelessWidget {
  /// The field name to validate
  final String fieldName;
  
  /// The validation result containing field errors
  final QuestValidationResult validationResult;
  
  /// Whether to show the error inline or as a separate widget
  final bool inline;
  
  /// Custom error text color
  final Color? errorColor;

  const FieldValidationDisplay({
    super.key,
    required this.fieldName,
    required this.validationResult,
    this.inline = true,
    this.errorColor,
  });

  @override
  Widget build(BuildContext context) {
    final error = validationResult.fieldErrors[fieldName];
    
    if (error == null) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final color = errorColor ?? theme.colorScheme.error;

    if (inline) {
      return Padding(
        padding: const EdgeInsets.only(top: 4.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 12,
              color: color,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                error,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 8.0),
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Validation progress indicator for forms
class ValidationProgressIndicator extends StatelessWidget {
  /// The validation summary to display progress for
  final ValidationSummary validationSummary;
  
  /// Total number of required fields
  final int totalFields;
  
  /// Number of valid fields
  final int validFields;
  
  /// Whether to show the progress text
  final bool showProgressText;

  const ValidationProgressIndicator({
    super.key,
    required this.validationSummary,
    required this.totalFields,
    required this.validFields,
    this.showProgressText = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = totalFields > 0 ? validFields / totalFields : 0.0;
    final color = validationSummary.isValid ? theme.colorScheme.primary : theme.colorScheme.error;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            if (showProgressText) ...[
              const SizedBox(width: 12),
              Text(
                '$validFields/$totalFields',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Text(
          _getProgressText(),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  String _getProgressText() {
    if (validationSummary.isValid) {
      return 'All required fields completed';
    } else {
      final remaining = totalFields - validFields;
      return '$remaining field${remaining == 1 ? '' : 's'} remaining';
    }
  }
}

/// Floating validation summary for long forms
class FloatingValidationSummary extends StatelessWidget {
  /// The validation summary to display
  final ValidationSummary validationSummary;
  
  /// Callback when the summary is tapped
  final VoidCallback? onTap;
  
  /// Whether to show the summary when form is valid
  final bool showWhenValid;

  const FloatingValidationSummary({
    super.key,
    required this.validationSummary,
    this.onTap,
    this.showWhenValid = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showWhenValid && validationSummary.isValid) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final color = validationSummary.isValid ? theme.colorScheme.primary : theme.colorScheme.error;

    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(24.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(24.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(24.0),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                validationSummary.isValid 
                    ? Icons.check_circle_outline_rounded
                    : Icons.error_outline_rounded,
                size: 20,
                color: color,
              ),
              const SizedBox(width: 8),
              Text(
                validationSummary.statusText,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (onTap != null) ...[
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_up_rounded,
                  size: 16,
                  color: color.withValues(alpha: 0.7),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}