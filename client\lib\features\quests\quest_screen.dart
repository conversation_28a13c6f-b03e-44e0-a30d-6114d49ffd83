import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'bloc/quest_bloc.dart';
import 'widgets/quest_card.dart';
import 'widgets/quest_filter_tabs.dart';
import 'widgets/create_quest_dialog.dart';
import 'package:shared/shared.dart';

class QuestScreen extends StatefulWidget {
  const QuestScreen({super.key});

  @override
  State<QuestScreen> createState() => _QuestScreenState();
}

class _QuestScreenState extends State<QuestScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    context.read<QuestBloc>().add(const LoadQuests());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quests & Tasks'),
        backgroundColor: theme.colorScheme.surfaceContainer,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_task),
            onPressed: () => _showCreateQuestDialog(context),
            tooltip: 'Create Quest',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
            tooltip: 'Filter Quests',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter tabs
          QuestFilterTabs(
            tabController: _tabController,
            onFilterChanged: (filter) {
              setState(() {
                _selectedFilter = filter;
              });
              context.read<QuestBloc>().add(LoadQuests(status: filter));
            },
          ),
          
          // Quest content
          Expanded(
            child: BlocConsumer<QuestBloc, QuestState>(
              listener: (context, state) {
                if (state is QuestError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: theme.colorScheme.error,
                    ),
                  );
                } else if (state is QuestSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: theme.colorScheme.primary,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is QuestLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (state is QuestError) {
                  return _buildErrorState(theme, state.message);
                }

                if (state is QuestLoaded) {
                  return _buildQuestList(theme, state);
                }

                return _buildEmptyState(theme);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateQuestDialog(context),
        tooltip: 'Create New Quest',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildQuestList(ThemeData theme, QuestLoaded state) {
    final filteredQuests = _filterQuests(state.quests);

    if (filteredQuests.isEmpty) {
      return _buildEmptyState(theme);
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<QuestBloc>().add(const LoadQuests());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: filteredQuests.length,
        itemBuilder: (context, index) {
          final quest = filteredQuests[index];
          final progress = state.questProgress[quest.id];

          return Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: QuestCard(
              quest: quest,
              progress: progress,
              onTap: () => _showQuestDetails(context, quest, progress),
              onComplete: () => _completeQuest(context, quest.id),
              onEdit: () => _editQuest(context, quest),
              onDelete: () => _deleteQuest(context, quest.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Quests Available',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first quest to get started!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () => _showCreateQuestDialog(context),
            child: const Text('Create Quest'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Quests',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () {
              context.read<QuestBloc>().add(const LoadQuests());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  List<Quest> _filterQuests(List<Quest> quests) {
    switch (_selectedFilter) {
      case 'active':
        return quests.where((q) => q.status == QuestStatus.active).toList();
      case 'completed':
        return quests.where((q) => q.status == QuestStatus.completed).toList();
      case 'overdue':
        return quests.where((q) => 
          q.deadline != null && 
          q.deadline!.isBefore(DateTime.now()) && 
          q.status != QuestStatus.completed
        ).toList();
      default:
        return quests;
    }
  }

  void _showCreateQuestDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateQuestDialog(
        onQuestCreated: (questData) {
          context.read<QuestBloc>().add(CreateQuest(questData));
        },
      ),
    );
  }

  void _showQuestDetails(BuildContext context, Quest quest, QuestProgress? progress) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        quest.title,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  quest.description,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 24),
                if (progress != null) ...[
                  Text(
                    'Progress',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: progress.tasksCompleted / progress.totalTasks,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${progress.tasksCompleted}/${progress.totalTasks} tasks completed',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                ],
                if (quest.taskIds.isNotEmpty) ...[
                  Text(
                    'Tasks',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  ...quest.taskIds.asMap().entries.map((entry) => Card(
                    child: ListTile(
                      leading: const Icon(
                        Icons.radio_button_unchecked,
                        color: Colors.grey,
                      ),
                      title: Text('Task ${entry.key + 1}'),
                      subtitle: Text('Task ID: ${entry.value}'),
                      trailing: Text('${quest.totalPoints ~/ quest.taskIds.length} pts'),
                    ),
                  )),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _completeQuest(BuildContext context, String questId) {
    context.read<QuestBloc>().add(CompleteQuest(questId, 'current_user'));
  }

  void _editQuest(BuildContext context, Quest quest) async {
    // Navigate to quest creation page with the quest data pre-filled
    final result = await Navigator.of(context).pushNamed(
      '/quests/create',
      arguments: {'editingQuest': quest},
    );

    // Refresh the quest list if the quest was updated
    if (result == true && context.mounted) {
      context.read<QuestBloc>().add(const LoadQuests());
    }
  }

  void _deleteQuest(BuildContext context, String questId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Quest'),
        content: const Text('Are you sure you want to delete this quest? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<QuestBloc>().add(DeleteQuest(questId));
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Quests'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('All Quests'),
              value: 'all',
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                  _tabController.index = 0;
                });
                Navigator.pop(context);
                context.read<QuestBloc>().add(const LoadQuests());
              },
            ),
            RadioListTile<String>(
              title: const Text('Active'),
              value: 'active',
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                  _tabController.index = 1;
                });
                Navigator.pop(context);
                context.read<QuestBloc>().add(LoadQuests(status: value));
              },
            ),
            RadioListTile<String>(
              title: const Text('Completed'),
              value: 'completed',
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                  _tabController.index = 2;
                });
                Navigator.pop(context);
                context.read<QuestBloc>().add(LoadQuests(status: value));
              },
            ),
            RadioListTile<String>(
              title: const Text('Overdue'),
              value: 'overdue',
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                  _tabController.index = 3;
                });
                Navigator.pop(context);
                context.read<QuestBloc>().add(LoadQuests(status: value));
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}