import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

class CreateQuestDialog extends StatefulWidget {
  final Function(CreateQuestDTO) onQuestCreated;

  const CreateQuestDialog({
    super.key,
    required this.onQuestCreated,
  });

  @override
  State<CreateQuestDialog> createState() => _CreateQuestDialogState();
}

class _CreateQuestDialogState extends State<CreateQuestDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  QuestCategory _selectedCategory = QuestCategory.productivity;
  QuestDifficulty _selectedDifficulty = QuestDifficulty.intermediate;
  int _pointsReward = 100;
  DateTime? _deadline;
  
  final List<String> _tasks = [];
  final _taskController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _taskController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Row(
                    children: [
                      Text(
                        'Create New Quest',
                        style: theme.textTheme.headlineSmall,
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Title field
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Quest Title',
                      border: OutlineInputBorder(),
                      hintText: 'Enter a compelling quest title',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a quest title';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Description field
                  TextFormField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                      hintText: 'Describe the quest objectives and requirements',
                      alignLabelWithHint: true,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a quest description';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Category and Difficulty
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<QuestCategory>(
                          value: _selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'Category',
                            border: OutlineInputBorder(),
                          ),
                          items: QuestCategory.values.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Row(
                                children: [
                                  Icon(_getCategoryIcon(category), size: 20),
                                  const SizedBox(width: 8),
                                  Text(_getCategoryName(category)),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      Expanded(
                        child: DropdownButtonFormField<QuestDifficulty>(
                          value: _selectedDifficulty,
                          decoration: const InputDecoration(
                            labelText: 'Difficulty',
                            border: OutlineInputBorder(),
                          ),
                          items: QuestDifficulty.values.map((difficulty) {
                            return DropdownMenuItem(
                              value: difficulty,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 20,
                                    color: _getDifficultyColor(difficulty),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(_getDifficultyName(difficulty)),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedDifficulty = value!;
                              _updatePointsForDifficulty();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Points and Due Date
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: _pointsReward.toString(),
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Points Reward',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.stars),
                          ),
                          onChanged: (value) {
                            _pointsReward = int.tryParse(value) ?? 100;
                          },
                          validator: (value) {
                            final points = int.tryParse(value ?? '');
                            if (points == null || points <= 0) {
                              return 'Please enter valid points';
                            }
                            return null;
                          },
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDueDate(context),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'Due Date (Optional)',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            child: Text(
                              _deadline != null
                                  ? '${_deadline!.day}/${_deadline!.month}/${_deadline!.year}'
                                  : 'Select date',
                              style: _deadline != null
                                  ? null
                                  : TextStyle(color: theme.colorScheme.onSurfaceVariant),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Tasks section
                  Text(
                    'Tasks',
                    style: theme.textTheme.titleMedium,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Add task field
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _taskController,
                          decoration: const InputDecoration(
                            hintText: 'Add a task',
                            border: OutlineInputBorder(),
                          ),
                          onFieldSubmitted: (_) => _addTask(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: _addTask,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Tasks list
                  if (_tasks.isNotEmpty)
                    Container(
                      height: 120,
                      child: ListView.builder(
                        itemCount: _tasks.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            dense: true,
                            leading: const Icon(Icons.task_alt),
                            title: Text(_tasks[index]),
                            trailing: IconButton(
                              icon: const Icon(Icons.remove_circle_outline),
                              onPressed: () => _removeTask(index),
                            ),
                          );
                        },
                      ),
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: FilledButton(
                          onPressed: _createQuest,
                          child: const Text('Create Quest'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _addTask() {
    if (_taskController.text.trim().isNotEmpty) {
      setState(() {
        _tasks.add(_taskController.text.trim());
        _taskController.clear();
      });
    }
  }

  void _removeTask(int index) {
    setState(() {
      _tasks.removeAt(index);
    });
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _deadline = date;
      });
    }
  }

  void _updatePointsForDifficulty() {
    switch (_selectedDifficulty) {
      case QuestDifficulty.beginner:
        _pointsReward = 100;
        break;
      case QuestDifficulty.intermediate:
        _pointsReward = 250;
        break;
      case QuestDifficulty.advanced:
        _pointsReward = 500;
        break;
      case QuestDifficulty.expert:
        _pointsReward = 750;
        break;
      case QuestDifficulty.master:
        _pointsReward = 1000;
        break;
    }
  }

  void _createQuest() {
    if (_formKey.currentState?.validate() ?? false) {
      final questData = CreateQuestDTO(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        difficulty: _selectedDifficulty,
        pointsReward: _pointsReward,
        dueDate: _deadline,
        taskTitles: _tasks,
      );

      widget.onQuestCreated(questData);
      Navigator.pop(context);
    }
  }

  IconData _getCategoryIcon(QuestCategory category) {
    switch (category) {
      case QuestCategory.health:
        return Icons.fitness_center;
      case QuestCategory.learning:
        return Icons.school;
      case QuestCategory.productivity:
        return Icons.work;
      case QuestCategory.creative:
        return Icons.palette;
      case QuestCategory.social:
        return Icons.people;
      case QuestCategory.work:
        return Icons.group_work;
      default:
        return Icons.task;
    }
  }

  String _getCategoryName(QuestCategory category) {
    switch (category) {
      case QuestCategory.health:
        return 'Fitness';
      case QuestCategory.learning:
        return 'Learning';
      case QuestCategory.productivity:
        return 'Productivity';
      case QuestCategory.creative:
        return 'Creativity';
      case QuestCategory.social:
        return 'Social';
      case QuestCategory.work:
        return 'Collaboration';
      default:
        return 'Other';
    }
  }

  String _getDifficultyName(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return 'Easy';
      case QuestDifficulty.intermediate:
        return 'Medium';
      case QuestDifficulty.advanced:
        return 'Hard';
      case QuestDifficulty.expert:
        return 'Expert';
      case QuestDifficulty.master:
        return 'Master';
    }
  }

  Color _getDifficultyColor(QuestDifficulty difficulty) {
    switch (difficulty) {
      case QuestDifficulty.beginner:
        return Colors.green;
      case QuestDifficulty.intermediate:
        return Colors.orange;
      case QuestDifficulty.advanced:
        return Colors.red;
      case QuestDifficulty.expert:
        return Colors.purple;
      case QuestDifficulty.master:
        return Colors.black;
    }
  }
}