import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

class QuestCard extends StatelessWidget {
  final Quest quest;
  final QuestProgress? progress;
  final VoidCallback? onTap;
  final VoidCallback? onComplete;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const QuestCard({
    super.key,
    required this.quest,
    this.progress,
    this.onTap,
    this.onComplete,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      quest.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(theme),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Description
              Text(
                quest.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // Category and difficulty
              Row(
                children: [
                  _buildCategoryChip(theme),
                  const SizedBox(width: 8),
                  _buildDifficultyChip(theme),
                  const Spacer(),
                  Icon(
                    Icons.stars,
                    size: 16,
                    color: Colors.amber,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${quest.totalPoints} pts',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Progress bar (if progress available)
              if (progress != null) ...[
                LinearProgressIndicator(
                  value: progress!.totalTasks > 0 
                      ? progress!.tasksCompleted / progress!.totalTasks 
                      : 0.0,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${progress!.tasksCompleted}/${progress!.totalTasks} tasks',
                      style: theme.textTheme.bodySmall,
                    ),
                    Text(
                      '${progress!.pointsEarned} pts earned',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
              ],
              
              // Due date and actions
              Row(
                children: [
                  if (quest.deadline != null) ...[
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: _getDueDateColor(theme),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDueDate(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getDueDateColor(theme),
                      ),
                    ),
                  ],
                  const Spacer(),
                  _buildActionButtons(theme),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ThemeData theme) {
    Color chipColor;
    String statusText;
    
    switch (quest.status) {
      case QuestStatus.active:
        chipColor = Colors.blue;
        statusText = 'Active';
        break;
      case QuestStatus.completed:
        chipColor = Colors.green;
        statusText = 'Completed';
        break;
      case QuestStatus.inProgress:
        chipColor = Colors.orange;
        statusText = 'In Progress';
        break;
      case QuestStatus.archived:
        chipColor = Colors.grey;
        statusText = 'Archived';
        break;
      case QuestStatus.cancelled:
        chipColor = Colors.red;
        statusText = 'Cancelled';
        break;
      case QuestStatus.draft:
        chipColor = Colors.grey;
        statusText = 'Draft';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: chipColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCategoryChip(ThemeData theme) {
    IconData icon;
    Color color;
    String categoryText;
    
    switch (quest.category) {
      case QuestCategory.health:
        icon = Icons.fitness_center;
        color = Colors.red;
        categoryText = 'Health';
        break;
      case QuestCategory.learning:
        icon = Icons.school;
        color = Colors.purple;
        categoryText = 'Learning';
        break;
      case QuestCategory.productivity:
        icon = Icons.trending_up;
        color = Colors.teal;
        categoryText = 'Productivity';
        break;
      case QuestCategory.creative:
        icon = Icons.palette;
        color = Colors.pink;
        categoryText = 'Creative';
        break;
      case QuestCategory.social:
        icon = Icons.people;
        color = Colors.green;
        categoryText = 'Social';
        break;
      case QuestCategory.work:
        icon = Icons.work;
        color = Colors.blue;
        categoryText = 'Work';
        break;
      case QuestCategory.personal:
        icon = Icons.person;
        color = Colors.indigo;
        categoryText = 'Personal';
        break;
      case QuestCategory.other:
        icon = Icons.task;
        color = Colors.grey;
        categoryText = 'Other';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            categoryText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyChip(ThemeData theme) {
    Color color;
    String difficultyText;
    
    switch (quest.difficulty) {
      case QuestDifficulty.beginner:
        color = Colors.green;
        difficultyText = 'Beginner';
        break;
      case QuestDifficulty.intermediate:
        color = Colors.orange;
        difficultyText = 'Intermediate';
        break;
      case QuestDifficulty.advanced:
        color = Colors.red;
        difficultyText = 'Advanced';
        break;
      case QuestDifficulty.expert:
        color = Colors.purple;
        difficultyText = 'Expert';
        break;
      case QuestDifficulty.master:
        color = Colors.black;
        difficultyText = 'Master';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        difficultyText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (quest.status == QuestStatus.active && onComplete != null)
          IconButton(
            icon: const Icon(Icons.check_circle_outline),
            onPressed: onComplete,
            tooltip: 'Complete Quest',
            iconSize: 20,
          ),
        if (onEdit != null)
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: onEdit,
            tooltip: 'Edit Quest',
            iconSize: 20,
          ),
        if (onDelete != null)
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: onDelete,
            tooltip: 'Delete Quest',
            iconSize: 20,
          ),
      ],
    );
  }

  Color _getDueDateColor(ThemeData theme) {
    if (quest.deadline == null) return theme.colorScheme.onSurfaceVariant;
    
    final now = DateTime.now();
    final dueDate = quest.deadline!;
    
    if (quest.status == QuestStatus.completed) {
      return Colors.green;
    } else if (dueDate.isBefore(now)) {
      return Colors.red; // Overdue
    } else if (dueDate.difference(now).inDays <= 1) {
      return Colors.orange; // Due soon
    } else {
      return theme.colorScheme.onSurfaceVariant; // Normal
    }
  }

  String _formatDueDate() {
    if (quest.deadline == null) return '';
    
    final now = DateTime.now();
    final dueDate = quest.deadline!;
    final difference = dueDate.difference(now);
    
    if (quest.status == QuestStatus.completed) {
      return 'Completed';
    } else if (dueDate.isBefore(now)) {
      final overdueDays = now.difference(dueDate).inDays;
      return overdueDays == 0 ? 'Overdue today' : 'Overdue by $overdueDays days';
    } else if (difference.inDays == 0) {
      return 'Due today';
    } else if (difference.inDays == 1) {
      return 'Due tomorrow';
    } else if (difference.inDays <= 7) {
      return 'Due in ${difference.inDays} days';
    } else {
      return 'Due ${dueDate.day}/${dueDate.month}/${dueDate.year}';
    }
  }
}