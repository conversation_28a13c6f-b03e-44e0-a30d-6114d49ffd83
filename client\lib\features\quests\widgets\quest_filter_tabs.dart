import 'package:flutter/material.dart';

class QuestFilterTabs extends StatelessWidget {
  final TabController tabController;
  final Function(String) onFilterChanged;

  const QuestFilterTabs({
    super.key,
    required this.tabController,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: TabB<PERSON>(
        controller: tabController,
        indicatorColor: theme.colorScheme.primary,
        labelColor: theme.colorScheme.primary,
        unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
        onTap: (index) {
          final filters = ['all', 'active', 'completed', 'overdue'];
          onFilterChanged(filters[index]);
        },
        tabs: const [
          Tab(
            icon: Icon(Icons.all_inclusive),
            text: 'All',
          ),
          Tab(
            icon: Icon(Icons.play_arrow),
            text: 'Active',
          ),
          Tab(
            icon: Icon(Icons.check_circle),
            text: 'Completed',
          ),
          Tab(
            icon: Icon(Icons.warning),
            text: 'Overdue',
          ),
        ],
      ),
    );
  }
}