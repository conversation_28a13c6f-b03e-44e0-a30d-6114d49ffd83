import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../../core/bloc/realtime/realtime_bloc.dart';

class LiveNotificationsPanel extends StatefulWidget {
  final String? userId;
  final VoidCallback? onNotificationTap;

  const LiveNotificationsPanel({
    super.key,
    this.userId,
    this.onNotificationTap,
  });

  @override
  State<LiveNotificationsPanel> createState() => _LiveNotificationsPanelState();
}

class _LiveNotificationsPanelState extends State<LiveNotificationsPanel> {
  final List<WebSocketEvent> _notifications = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _listenToRealtimeEvents();
  }

  void _listenToRealtimeEvents() {
    context.read<RealtimeBloc>().eventsStream.listen((event) {
      if (_shouldShowNotification(event)) {
        setState(() {
          _notifications.insert(0, event);
          // Keep only last 50 notifications
          if (_notifications.length > 50) {
            _notifications.removeRange(50, _notifications.length);
          }
        });
        
        // Auto-scroll to top for new notifications
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }

  bool _shouldShowNotification(WebSocketEvent event) {
    // Filter notifications based on user preferences and relevance
    if (widget.userId != null) {
      // Show notifications targeted to this user or global notifications
      if (event.targetUserIds != null && !event.targetUserIds!.contains(widget.userId)) {
        return false;
      }
    }

    // Filter by event types that should show as notifications
    return event.type == WebSocketEventType.questUpdate || 
           event.type == WebSocketEventType.achievementUnlock ||
           event.type == WebSocketEventType.leaderboardUpdate ||
           event.type == WebSocketEventType.userPresence ||
           event.type == WebSocketEventType.collaborationInvite ||
           event.type == WebSocketEventType.taskUpdate ||
           event.type == WebSocketEventType.liveEditing;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _notifications.isEmpty
                ? _buildEmptyState()
                : _buildNotificationsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.notifications_active,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Live Notifications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (_notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () {
                setState(() {
                  _notifications.clear();
                });
              },
              tooltip: 'Clear all notifications',
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ll see real-time updates here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: _notifications.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildNotificationItem(WebSocketEvent notification) {
    return GestureDetector(
      onTap: () {
        widget.onNotificationTap?.call();
        // Handle notification tap based on type
        _handleNotificationTap(notification);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: _getNotificationColor(notification).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getNotificationColor(notification).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              _getNotificationIcon(notification),
              color: _getNotificationColor(notification),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getNotificationTitle(notification),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (_getNotificationSubtitle(notification) != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      _getNotificationSubtitle(notification)!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    _getTimeAgo(notification.timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getNotificationColor(WebSocketEvent notification) {
    switch (notification.type) {
      case WebSocketEventType.achievementUnlock:
        return Colors.amber;
      case WebSocketEventType.questUpdate:
        return Colors.blue;
      case WebSocketEventType.taskUpdate:
        return Colors.green;
      case WebSocketEventType.collaborationInvite:
        return Colors.purple;
      case WebSocketEventType.userPresence:
        return Colors.teal;
      case WebSocketEventType.leaderboardUpdate:
        return Colors.orange;
      case WebSocketEventType.liveEditing:
        return Colors.indigo;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getNotificationIcon(WebSocketEvent notification) {
    switch (notification.type) {
      case WebSocketEventType.achievementUnlock:
        return Icons.emoji_events;
      case WebSocketEventType.questUpdate:
        return Icons.assignment;
      case WebSocketEventType.taskUpdate:
        return Icons.check_circle;
      case WebSocketEventType.collaborationInvite:
        return Icons.group_add;
      case WebSocketEventType.userPresence:
        return Icons.person;
      case WebSocketEventType.leaderboardUpdate:
        return Icons.leaderboard;
      case WebSocketEventType.liveEditing:
        return Icons.edit;
      default:
        return Icons.notifications;
    }
  }

  String _getNotificationTitle(WebSocketEvent notification) {
    switch (notification.type) {
      case WebSocketEventType.achievementUnlock:
        return 'Achievement Unlocked!';
      case WebSocketEventType.questUpdate:
        return 'Quest Updated';
      case WebSocketEventType.taskUpdate:
        return 'Task Updated';
      case WebSocketEventType.collaborationInvite:
        return 'Collaboration Invite';
      case WebSocketEventType.userPresence:
        return 'User Status Update';
      case WebSocketEventType.leaderboardUpdate:
        return 'Leaderboard Update';
      case WebSocketEventType.liveEditing:
        return 'Live Editing Session';
      default:
        return 'Notification';
    }
  }

  String? _getNotificationSubtitle(WebSocketEvent notification) {
    final data = notification.data;
    
    switch (notification.type) {
      case WebSocketEventType.achievementUnlock:
        return data['achievement_name'] as String? ?? 'New achievement earned!';
      case WebSocketEventType.questUpdate:
        final questTitle = data['quest_title'] as String?;
        final action = data['action'] as String?;
        return questTitle != null && action != null 
            ? '$questTitle was $action'
            : 'A quest has been updated';
      case WebSocketEventType.taskUpdate:
        final taskTitle = data['task_title'] as String?;
        final action = data['action'] as String?;
        return taskTitle != null && action != null
            ? '"$taskTitle" was $action' 
            : 'A task was updated';
      case WebSocketEventType.collaborationInvite:
        final inviter = data['inviter_name'] as String?;
        final sessionTitle = data['session_title'] as String?;
        return inviter != null && sessionTitle != null
            ? '$inviter invited you to "$sessionTitle"'
            : 'You have a collaboration invite';
      case WebSocketEventType.userPresence:
        final userName = data['user_name'] as String?;
        final status = data['status'] as String?;
        return userName != null && status != null
            ? '$userName is now $status'
            : 'User status changed';
      case WebSocketEventType.leaderboardUpdate:
        return 'Rankings have been updated';
      case WebSocketEventType.liveEditing:
        final documentTitle = data['document_title'] as String?;
        return documentTitle != null
            ? 'Someone is editing "$documentTitle"'
            : 'Live editing session active';
      default:
        return null;
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _handleNotificationTap(WebSocketEvent notification) {
    // Handle specific notification tap actions
    switch (notification.type) {
      case WebSocketEventType.questUpdate:
        // Navigate to quest details
        if (notification.data['quest_id'] != null) {
          Navigator.of(context).pushNamed(
            '/quest/${notification.data['quest_id']}',
          );
        }
        break;
      case WebSocketEventType.taskUpdate:
        // Navigate to task details
        if (notification.data['task_id'] != null) {
          Navigator.of(context).pushNamed(
            '/task/${notification.data['task_id']}',
          );
        }
        break;
      case WebSocketEventType.collaborationInvite:
        // Open collaboration session
        if (notification.data['session_id'] != null) {
          Navigator.of(context).pushNamed(
            '/collaboration/${notification.data['session_id']}',
          );
        }
        break;
      case WebSocketEventType.achievementUnlock:
        // Show achievement details
        _showAchievementDialog(notification);
        break;
      case WebSocketEventType.leaderboardUpdate:
        // Navigate to leaderboard
        Navigator.of(context).pushNamed('/leaderboard');
        break;
      case WebSocketEventType.liveEditing:
        // Join editing session
        if (notification.data['document_id'] != null) {
          Navigator.of(context).pushNamed(
            '/editor/${notification.data['document_id']}',
          );
        }
        break;
      default:
        // Handle other notification types
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(notification.data['message'] as String? ?? 'New notification'),
            action: SnackBarAction(
              label: 'View',
              onPressed: () {
                // Generic notification handler
                Navigator.of(context).pushNamed('/notifications');
              },
            ),
          ),
        );
        break;
    }
  }

  void _showAchievementDialog(WebSocketEvent notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.emoji_events, color: Colors.amber, size: 32),
            const SizedBox(width: 12),
            const Text('Achievement Unlocked!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              notification.data['achievement_name'] as String? ?? 'New Achievement',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              notification.data['description'] as String? ?? 'You\'ve unlocked a new achievement!',
              textAlign: TextAlign.center,
            ),
            if (notification.data['points_awarded'] != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '+${notification.data['points_awarded']} points',
                  style: const TextStyle(
                    color: Colors.amber,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushNamed('/achievements');
            },
            child: const Text('View All'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}

class LiveNotificationsBadge extends StatelessWidget {
  final String? userId;
  final Widget child;

  const LiveNotificationsBadge({
    super.key,
    this.userId,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<WebSocketEvent>(
      stream: context.read<RealtimeBloc>().eventsStream,
      builder: (context, snapshot) {
        // In a real implementation, you'd track unread notifications count
        final hasNotifications = snapshot.hasData;
        
        return Badge(
          isLabelVisible: hasNotifications,
          backgroundColor: Theme.of(context).colorScheme.error,
          child: child,
        );
      },
    );
  }
}

class RealtimeConnectionStatus extends StatelessWidget {
  const RealtimeConnectionStatus({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RealtimeBloc, RealtimeState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(state).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getStatusColor(state),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _getStatusColor(state),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                _getStatusText(state),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getStatusColor(state),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getStatusColor(RealtimeState state) {
    if (state is RealtimeConnected) {
      return Colors.green;
    } else if (state is RealtimeConnecting || state is RealtimeReconnecting) {
      return Colors.orange;
    } else if (state is RealtimeError) {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }

  String _getStatusText(RealtimeState state) {
    if (state is RealtimeConnected) {
      return 'Connected';
    } else if (state is RealtimeConnecting) {
      return 'Connecting...';
    } else if (state is RealtimeReconnecting) {
      return 'Reconnecting...';
    } else if (state is RealtimeError) {
      return 'Error';
    } else {
      return 'Disconnected';
    }
  }
}
