import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../../core/bloc/realtime/realtime_bloc.dart';

class UserPresenceIndicator extends StatelessWidget {
  final String userId;
  final Widget child;
  final bool showActivity;
  final double size;

  const UserPresenceIndicator({
    super.key,
    required this.userId,
    required this.child,
    this.showActivity = true,
    this.size = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        Positioned(
          bottom: 0,
          right: 0,
          child: StreamBuilder<UserPresence>(
            stream: context.read<RealtimeBloc>().presenceStream,
            builder: (context, snapshot) {
              if (!snapshot.hasData) {
                return const SizedBox.shrink();
              }

              final presence = snapshot.data!;
              if (presence.userId != userId) {
                return const SizedBox.shrink();
              }

              return Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: _getStatusColor(presence.status),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface,
                    width: 2,
                  ),
                ),
                child: showActivity && presence.activity != null
                    ? Icon(
                        _getActivityIcon(presence.activity!),
                        size: size * 0.6,
                        color: Colors.white,
                      )
                    : null,
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(PresenceStatus status) {
    switch (status) {
      case PresenceStatus.online:
        return Colors.green;
      case PresenceStatus.away:
        return Colors.orange;
      case PresenceStatus.busy:
        return Colors.red;
      case PresenceStatus.offline:
        return Colors.grey;
      case PresenceStatus.invisible:
        return Colors.grey.withValues(alpha: 0.5);
    }
  }

  IconData _getActivityIcon(UserActivity activity) {
    switch (activity) {
      case UserActivity.viewingDashboard:
        return Icons.visibility;
      case UserActivity.editingQuest:
        return Icons.edit;
      case UserActivity.editingTask:
        return Icons.task_alt;
      case UserActivity.typing:
        return Icons.keyboard;
      case UserActivity.idle:
        return Icons.schedule;
      case UserActivity.inMeeting:
        return Icons.videocam;
      case UserActivity.workingOnTask:
        return Icons.work;
    }
  }
}

class UserPresenceList extends StatelessWidget {
  final List<String> userIds;
  final Widget Function(String userId, UserPresence? presence) itemBuilder;

  const UserPresenceList({
    super.key,
    required this.userIds,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserPresence>(
      stream: context.read<RealtimeBloc>().presenceStream,
      builder: (context, snapshot) {
        // Collect all presence data
        final presenceMap = <String, UserPresence>{};
        // Note: In a real implementation, you'd maintain a map of all user presences
        
        return ListView.builder(
          itemCount: userIds.length,
          itemBuilder: (context, index) {
            final userId = userIds[index];
            final presence = presenceMap[userId];
            return itemBuilder(userId, presence);
          },
        );
      },
    );
  }
}

class UserPresenceBadge extends StatelessWidget {
  final UserPresence presence;
  final bool showStatusMessage;
  final VoidCallback? onTap;

  const UserPresenceBadge({
    super.key,
    required this.presence,
    this.showStatusMessage = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getStatusColor(presence.status).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getStatusColor(presence.status),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _getStatusColor(presence.status),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              _getStatusText(presence.status),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStatusColor(presence.status),
                fontWeight: FontWeight.w500,
              ),
            ),
            if (presence.activity != null) ...[
              const SizedBox(width: 4),
              Icon(
                _getActivityIcon(presence.activity!),
                size: 12,
                color: _getStatusColor(presence.status),
              ),
            ],
            if (showStatusMessage && presence.statusMessage != null) ...[
              const SizedBox(width: 6),
              Text(
                presence.statusMessage!,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(PresenceStatus status) {
    switch (status) {
      case PresenceStatus.online:
        return Colors.green;
      case PresenceStatus.away:
        return Colors.orange;
      case PresenceStatus.busy:
        return Colors.red;
      case PresenceStatus.offline:
        return Colors.grey;
      case PresenceStatus.invisible:
        return Colors.grey.withValues(alpha: 0.5);
    }
  }

  String _getStatusText(PresenceStatus status) {
    switch (status) {
      case PresenceStatus.online:
        return 'Online';
      case PresenceStatus.away:
        return 'Away';
      case PresenceStatus.busy:
        return 'Busy';
      case PresenceStatus.offline:
        return 'Offline';
      case PresenceStatus.invisible:
        return 'Invisible';
    }
  }

  IconData _getActivityIcon(UserActivity activity) {
    switch (activity) {
      case UserActivity.viewingDashboard:
        return Icons.visibility;
      case UserActivity.editingQuest:
        return Icons.edit;
      case UserActivity.editingTask:
        return Icons.task_alt;
      case UserActivity.typing:
        return Icons.keyboard;
      case UserActivity.idle:
        return Icons.schedule;
      case UserActivity.inMeeting:
        return Icons.videocam;
      case UserActivity.workingOnTask:
        return Icons.work;
    }
  }
}

class CollaboratorsList extends StatelessWidget {
  final String sessionId;

  const CollaboratorsList({
    super.key,
    required this.sessionId,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CollaborationSession>(
      stream: context.read<RealtimeBloc>().collaborationStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.id != sessionId) {
          return const SizedBox.shrink();
        }

        final session = snapshot.data!;
        final participants = session.participants;

        if (participants.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Collaborators (${participants.length})',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: participants.map((participant) {
                  return Chip(
                    avatar: UserPresenceIndicator(
                      userId: participant.userId,
                      size: 8,
                      child: CircleAvatar(
                        radius: 12,
                        child: Text(
                          participant.displayName.isNotEmpty 
                              ? participant.displayName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                    label: Text(
                      participant.displayName,
                      style: const TextStyle(fontSize: 12),
                    ),
                    visualDensity: VisualDensity.compact,
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }
}
