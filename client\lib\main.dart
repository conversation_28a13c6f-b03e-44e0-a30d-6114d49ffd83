import 'package:flutter/material.dart';
import 'app/app.dart';
import 'core/di/dependency_injection.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  debugPrint('🚀 Starting Quester App...');
  
  // Initialize dependency injection with error handling
  try {
    debugPrint('📦 Initializing dependencies...');
    await DependencyInjection.instance.initialize();
    debugPrint('✅ Dependencies initialized successfully');
  } catch (e, stackTrace) {
    debugPrint('❌ Dependency injection initialization failed: $e');
    debugPrint('Stack trace: $stackTrace');
  }
  
  debugPrint('🏃 Running app...');
  runApp(const QuesterApp());
}
