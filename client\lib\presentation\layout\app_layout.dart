import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/utils/responsive_helper.dart';
import '../../core/enums/device_type.dart';
import '../../domain/bloc/app_bloc.dart';
import 'components/app_bar/responsive_app_bar.dart';
import 'components/navigation/bottom_navigation_rail.dart';
import 'components/navigation/side_navigation_rail.dart';
import 'components/sidebars/notification_sidebar.dart';
import 'components/sidebars/user_account_sidebar.dart';

/// Universal responsive app layout that adapts to different screen sizes
/// Features:
/// - Top app bar with Quest<PERSON> logo and action icons
/// - Bottom rail for mobile devices
/// - Side rail for tablet/desktop devices
/// - Real-time notifications and user data
/// - Responsive design with clean architecture
class AppLayout extends StatelessWidget {
  final Widget child;

  const AppLayout({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    try {
      return BlocBuilder<AppBloc, AppState>(
        builder: (context, appState) {
          try {
            final deviceType = ResponsiveHelper.getDeviceType(context);
            
            return Scaffold(
              body: _buildBody(context, deviceType, appState),
              bottomNavigationBar: _buildBottomNavigation(context, deviceType),
              drawer: _buildDrawer(context, deviceType),
              endDrawer: _buildEndDrawer(context, appState),
            );
          } catch (e) {
            debugPrint('AppLayout inner build error: $e');
            return _buildFallbackLayout(context);
          }
        },
      );
    } catch (e) {
      debugPrint('AppLayout outer build error: $e');
      return _buildFallbackLayout(context);
    }
  }

  Widget _buildFallbackLayout(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quester'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.flag_rounded, size: 64, color: Colors.blue),
            const SizedBox(height: 16),
            const Text(
              'Quester App Loading...',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('Initializing application components'),
            const SizedBox(height: 24),
            Expanded(child: child),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context, DeviceType deviceType, AppState appState) {
    return Column(
      children: [
        // Responsive App Bar
        ResponsiveAppBar(deviceType: deviceType),
        
        // Main Content Area
        Expanded(
          child: Row(
            children: [
              // Side Navigation (Desktop/Tablet only)
              if (deviceType.isDesktop) ...[
                SideNavigationRail(deviceType: deviceType),
                const VerticalDivider(width: 1),
              ],
              
              // Main Content
              Expanded(
                child: Stack(
                  children: [
                    // Page Content
                    child,
                    
                    // Notification Sidebar Overlay
                    if (appState.isNotificationSidebarOpen)
                      NotificationSidebar(
                        onClose: () => context.read<AppBloc>().add(
                          const CloseNotificationSidebar(),
                        ),
                      ),
                    
                    // User Account Sidebar Overlay
                    if (appState.isUserSidebarOpen)
                      UserAccountSidebar(
                        onClose: () => context.read<AppBloc>().add(
                          const CloseUserSidebar(),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget? _buildBottomNavigation(BuildContext context, DeviceType deviceType) {
    if (deviceType.isMobile) {
      return BottomNavigationRail(deviceType: deviceType);
    }
    return null;
  }

  Widget? _buildDrawer(BuildContext context, DeviceType deviceType) {
    if (deviceType.isMobile) {
      return SideNavigationRail(
        deviceType: deviceType,
        isDrawer: true,
      );
    }
    return null;
  }

  Widget? _buildEndDrawer(BuildContext context, AppState appState) {
    // End drawer can be used for additional panels if needed
    return null;
  }
}