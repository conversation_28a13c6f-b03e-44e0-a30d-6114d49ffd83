import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/enums/device_type.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/responsive_helper.dart';
import '../../../../domain/bloc/app_bloc.dart';
import '../../../../domain/bloc/notification_bloc.dart';
import '../../../widgets/common/quester_logo.dart';
import '../../../widgets/common/search_button.dart';
import '../../../widgets/common/notification_icon_button.dart';
import '../../../widgets/common/account_icon_button.dart';

/// Responsive app bar with elevated grey design
/// Features:
/// - <PERSON><PERSON> logo on the starting side
/// - Search, notification, and account icons on ending side
/// - Responsive design for mobile, tablet, and desktop
/// - Elevated grey background with clean styling
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final DeviceType deviceType;

  const ResponsiveAppBar({
    super.key,
    required this.deviceType,
  });

  @override
  Size get preferredSize => Size.fromHeight(AppConfig.appBarHeight);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        return Container(
          height: AppConfig.appBarHeight,
          decoration: BoxDecoration(
            color: AppColors.grey100,
            boxShadow: const [
              BoxShadow(
                color: AppColors.shadow,
                offset: Offset(0, 1),
                blurRadius: 3,
                spreadRadius: 0,
              ),
            ],
          ),
          child: SafeArea(
            bottom: false,
            child: Padding(
              padding: ResponsiveHelper.responsivePadding(context).copyWith(
                top: 0,
                bottom: 0,
              ),
              child: Row(
                children: [
                  // Menu Button (Mobile only)
                  if (deviceType.isMobile) ...[
                    IconButton(
                      icon: Icon(
                        Icons.menu_rounded,
                        size: ResponsiveHelper.responsiveIconSize(context, 24),
                        color: AppColors.onSurface,
                      ),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                      tooltip: 'Menu',
                    ),
                    SizedBox(width: ResponsiveHelper.responsiveFontSize(context, 8)),
                  ],
                  
                  // Quester Logo
                  QuesterLogo(
                    deviceType: deviceType,
                  ),
                  
                  const Spacer(),
                  
                  // Action Icons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Search Button
                      SearchButton(
                        deviceType: deviceType,
                        onPressed: () => _handleSearchPressed(context),
                      ),
                      
                      SizedBox(width: ResponsiveHelper.responsiveFontSize(context, 8)),
                      
                      // Notification Icon with Badge
                      NotificationIconButton(
                        deviceType: deviceType,
                        notificationCount: notificationState.unreadCount,
                        onPressed: () => _handleNotificationPressed(context),
                      ),
                      
                      SizedBox(width: ResponsiveHelper.responsiveFontSize(context, 8)),
                      
                      // Account Icon
                      AccountIconButton(
                        deviceType: deviceType,
                        onPressed: () => _handleAccountPressed(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleSearchPressed(BuildContext context) {
    // Navigate to search screen or show search overlay
    showSearch(
      context: context,
      delegate: QuestSearchDelegate(),
    );
  }

  void _handleNotificationPressed(BuildContext context) {
    final appBloc = context.read<AppBloc>();
    
    if (deviceType.isMobile) {
      // Navigate to notifications screen on mobile
      Navigator.of(context).pushNamed('/notifications');
    } else {
      // Toggle notification sidebar on desktop/tablet
      appBloc.add(const ToggleNotificationSidebar());
    }
  }

  void _handleAccountPressed(BuildContext context) {
    final appBloc = context.read<AppBloc>();
    
    if (deviceType.isMobile) {
      // Navigate to profile screen on mobile
      Navigator.of(context).pushNamed('/profile');
    } else {
      // Toggle user account sidebar on desktop/tablet
      appBloc.add(const ToggleUserSidebar());
    }
  }
}

/// Custom search delegate for Quester application
class QuestSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'Search quests, users, achievements...';

  @override
  TextStyle get searchFieldStyle => AppTextStyles.bodyMedium;

  @override
  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.onSurface,
        elevation: 0,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: AppTextStyles.bodyMedium,
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear_rounded),
          onPressed: () => query = '',
          tooltip: 'Clear',
        ),
      IconButton(
        icon: const Icon(Icons.search_rounded),
        onPressed: () => showResults(context),
        tooltip: 'Search',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back_rounded),
      onPressed: () => close(context, ''),
      tooltip: 'Back',
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter a search term to see results'),
      );
    }

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _performSearch(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Search error: ${snapshot.error}'),
              ],
            ),
          );
        }

        final results = snapshot.data ?? [];

        if (results.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.search_off, size: 48, color: Colors.grey),
                const SizedBox(height: 16),
                Text('No results found for "$query"'),
                const SizedBox(height: 8),
                const Text(
                  'Try different keywords or check your spelling',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: results.length,
          itemBuilder: (context, index) {
            final result = results[index];
            return ListTile(
              leading: CircleAvatar(
                child: Icon(_getIconForType(result['type'])),
              ),
              title: Text(result['title']),
              subtitle: Text(result['description']),
              trailing: Chip(
                label: Text(result['type']),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              onTap: () {
                close(context, result['title']);
                _navigateToResult(context, result);
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return ListView(
        children: const [
          ListTile(
            leading: Icon(Icons.lightbulb_outline),
            title: Text('Try searching for:'),
            subtitle: Text('Quests, tasks, achievements, or users'),
          ),
          ListTile(
            leading: Icon(Icons.assignment),
            title: Text('My active quests'),
          ),
          ListTile(
            leading: Icon(Icons.star),
            title: Text('Achievements'),
          ),
          ListTile(
            leading: Icon(Icons.people),
            title: Text('Team members'),
          ),
        ],
      );
    }

    // Filter suggestions based on query
    final suggestions = _getSearchSuggestions(query);

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return ListTile(
          leading: Icon(suggestion.icon),
          title: Text(suggestion.title),
          subtitle: suggestion.subtitle != null ? Text(suggestion.subtitle!) : null,
          onTap: () {
            query = suggestion.title;
            showResults(context);
          },
        );
      },
    );
  }

  List<SearchSuggestion> _getSearchSuggestions(String query) {
    final allSuggestions = [
      SearchSuggestion(
        title: 'My Quests',
        subtitle: 'View your active quests',
        icon: Icons.assignment,
      ),
      SearchSuggestion(
        title: 'Completed Tasks',
        subtitle: 'See your completed tasks',
        icon: Icons.check_circle,
      ),
      SearchSuggestion(
        title: 'Achievements',
        subtitle: 'View your achievements',
        icon: Icons.star,
      ),
      SearchSuggestion(
        title: 'Leaderboard',
        subtitle: 'Check your ranking',
        icon: Icons.leaderboard,
      ),
      SearchSuggestion(
        title: 'Team Members',
        subtitle: 'Find team members',
        icon: Icons.people,
      ),
      SearchSuggestion(
        title: 'Create Quest',
        subtitle: 'Start a new quest',
        icon: Icons.add_circle,
      ),
    ];

    return allSuggestions
        .where((suggestion) =>
            suggestion.title.toLowerCase().contains(query.toLowerCase()) ||
            (suggestion.subtitle?.toLowerCase().contains(query.toLowerCase()) ?? false))
        .toList();
  }

  /// Perform search and return results
  Future<List<Map<String, dynamic>>> _performSearch(String query) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Mock search results
    final allResults = [
      {
        'type': 'quest',
        'title': 'Complete Flutter Project',
        'description': 'Build a complete Flutter application with authentication',
        'id': 'quest_1',
      },
      {
        'type': 'task',
        'title': 'Setup Firebase Authentication',
        'description': 'Configure Firebase Auth for the Flutter app',
        'id': 'task_1',
      },
      {
        'type': 'user',
        'title': 'John Doe',
        'description': 'Senior Flutter Developer',
        'id': 'user_1',
      },
      {
        'type': 'achievement',
        'title': 'Flutter Master',
        'description': 'Complete 10 Flutter projects',
        'id': 'achievement_1',
      },
    ];

    // Filter results based on query
    return allResults.where((result) {
      final title = result['title'].toString().toLowerCase();
      final description = result['description'].toString().toLowerCase();
      final searchQuery = query.toLowerCase();

      return title.contains(searchQuery) || description.contains(searchQuery);
    }).toList();
  }

  /// Get icon for result type
  IconData _getIconForType(String type) {
    switch (type) {
      case 'quest':
        return Icons.flag;
      case 'task':
        return Icons.task_alt;
      case 'user':
        return Icons.person;
      case 'achievement':
        return Icons.emoji_events;
      default:
        return Icons.search;
    }
  }

  /// Navigate to search result
  void _navigateToResult(BuildContext context, Map<String, dynamic> result) {
    final type = result['type'];
    final id = result['id'];

    switch (type) {
      case 'quest':
        Navigator.of(context).pushNamed('/quests/$id');
        break;
      case 'task':
        Navigator.of(context).pushNamed('/tasks/$id');
        break;
      case 'user':
        Navigator.of(context).pushNamed('/users/$id');
        break;
      case 'achievement':
        Navigator.of(context).pushNamed('/achievements/$id');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Cannot navigate to ${result['title']}')),
        );
    }
  }
}

class SearchSuggestion {
  final String title;
  final String? subtitle;
  final IconData icon;

  const SearchSuggestion({
    required this.title,
    this.subtitle,
    required this.icon,
  });
}