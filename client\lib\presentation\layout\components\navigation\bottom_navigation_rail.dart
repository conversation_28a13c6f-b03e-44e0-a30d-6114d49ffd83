import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/enums/device_type.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/responsive_helper.dart';
import '../../../../domain/bloc/navigation_bloc.dart';
import 'navigation_items.dart';

/// Bottom navigation rail for mobile devices
/// Features:
/// - Clean Material 3 design
/// - Optimized for mobile touch interaction
/// - Real-time navigation state updates
/// - Smooth transitions and animations
class BottomNavigationRail extends StatelessWidget {
  final DeviceType deviceType;

  const BottomNavigationRail({
    super.key,
    required this.deviceType,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationBloc, NavigationState>(
      builder: (context, navigationState) {
        return Container(
          height: AppConfig.bottomNavHeight,
          decoration: const BoxDecoration(
            color: AppColors.surface,
            border: Border(
              top: BorderSide(
                color: AppColors.border,
                width: 0.5,
              ),
            ),
          ),
          child: SafeArea(
            top: false,
            child: NavigationBar(
              height: AppConfig.bottomNavHeight,
              backgroundColor: Colors.transparent,
              elevation: 0,
              selectedIndex: navigationState.selectedIndex,
              onDestinationSelected: (index) {
                _handleNavigation(context, index);
              },
              destinations: NavigationItems.getBottomNavItems(context)
                  .map((item) => NavigationDestination(
                        icon: Icon(
                          item.icon,
                          size: ResponsiveHelper.responsiveIconSize(context, 24),
                        ),
                        selectedIcon: Icon(
                          item.selectedIcon ?? item.icon,
                          size: ResponsiveHelper.responsiveIconSize(context, 24),
                        ),
                        label: item.label,
                        tooltip: item.tooltip,
                      ))
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  void _handleNavigation(BuildContext context, int index) {
    final navigationBloc = context.read<NavigationBloc>();
    final navItems = NavigationItems.getBottomNavItems(context);
    
    if (index < navItems.length) {
      final destination = navItems[index];
      
      // Update navigation state
      navigationBloc.add(NavigateToIndex(index));
      
      // Navigate using GoRouter
      context.go(destination.route);
    }
  }
}