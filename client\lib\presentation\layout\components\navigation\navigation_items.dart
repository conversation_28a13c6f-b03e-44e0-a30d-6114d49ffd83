import 'package:flutter/material.dart';

import '../../../../core/constants/app_constants.dart';

/// Navigation item model
class NavigationItem {
  final String label;
  final IconData icon;
  final IconData? selectedIcon;
  final String route;
  final String? tooltip;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.selectedIcon,
    required this.route,
    this.tooltip,
  });
}

/// Navigation items configuration for different layouts
class NavigationItems {
  /// Get navigation items for bottom navigation (mobile)
  static List<NavigationItem> getBottomNavItems(BuildContext context) {
    return [
      const NavigationItem(
        label: 'Dashboard',
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard_rounded,
        route: AppConstants.dashboardRoute,
        tooltip: 'Dashboard',
      ),
      const NavigationItem(
        label: 'Quests',
        icon: Icons.flag_outlined,
        selectedIcon: Icons.flag_rounded,
        route: AppConstants.questsRoute,
        tooltip: 'Quests',
      ),
      const NavigationItem(
        label: 'Freelancing',
        icon: Icons.work_outline_rounded,
        selectedIcon: Icons.work_rounded,
        route: AppConstants.freelancingRoute,
        tooltip: 'Freelancing',
      ),
      const NavigationItem(
        label: 'Learning',
        icon: Icons.school_outlined,
        selectedIcon: Icons.school_rounded,
        route: AppConstants.learningRoute,
        tooltip: 'Learning',
      ),
    ];
  }

  /// Get navigation items for side navigation (tablet/desktop)
  static List<NavigationItem> getSideNavItems(BuildContext context) {
    return [
      const NavigationItem(
        label: 'Dashboard',
        icon: Icons.dashboard_outlined,
        selectedIcon: Icons.dashboard_rounded,
        route: AppConstants.dashboardRoute,
        tooltip: 'Dashboard',
      ),
      const NavigationItem(
        label: 'Quests',
        icon: Icons.flag_outlined,
        selectedIcon: Icons.flag_rounded,
        route: AppConstants.questsRoute,
        tooltip: 'Quests & Tasks',
      ),
      const NavigationItem(
        label: 'Freelancing',
        icon: Icons.work_outline_rounded,
        selectedIcon: Icons.work_rounded,
        route: AppConstants.freelancingRoute,
        tooltip: 'Freelancing Marketplace',
      ),
      const NavigationItem(
        label: 'Learning',
        icon: Icons.school_outlined,
        selectedIcon: Icons.school_rounded,
        route: AppConstants.learningRoute,
        tooltip: 'Learning Management',
      ),
      const NavigationItem(
        label: 'Gamification',
        icon: Icons.videogame_asset_outlined,
        selectedIcon: Icons.videogame_asset_rounded,
        route: AppConstants.gamificationRoute,
        tooltip: 'Gamification',
      ),
      const NavigationItem(
        label: 'Analytics',
        icon: Icons.analytics_outlined,
        selectedIcon: Icons.analytics_rounded,
        route: AppConstants.analyticsRoute,
        tooltip: 'Analytics & Reports',
      ),
      const NavigationItem(
        label: 'Enterprise',
        icon: Icons.business_outlined,
        selectedIcon: Icons.business_rounded,
        route: AppConstants.enterpriseRoute,
        tooltip: 'Enterprise',
      ),
      const NavigationItem(
        label: 'Settings',
        icon: Icons.settings_outlined,
        selectedIcon: Icons.settings_rounded,
        route: AppConstants.settingsRoute,
        tooltip: 'Settings',
      ),
    ];
  }

  /// Get all available navigation items
  static List<NavigationItem> getAllNavItems(BuildContext context) {
    return getSideNavItems(context);
  }

  /// Find navigation item by route
  static NavigationItem? findByRoute(BuildContext context, String route) {
    final allItems = getAllNavItems(context);
    try {
      return allItems.firstWhere((item) => item.route == route);
    } catch (e) {
      return null;
    }
  }

  /// Get navigation index by route
  static int getIndexByRoute(BuildContext context, String route) {
    final allItems = getAllNavItems(context);
    for (int i = 0; i < allItems.length; i++) {
      if (allItems[i].route == route) {
        return i;
      }
    }
    return 0; // Default to first item (Dashboard)
  }
}