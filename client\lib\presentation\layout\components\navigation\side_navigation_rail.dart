import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/enums/device_type.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/responsive_helper.dart';
import '../../../../domain/bloc/navigation_bloc.dart';
import 'navigation_items.dart';

/// Side navigation rail for tablet and desktop devices
/// Features:
/// - Expandable/collapsible sidebar
/// - Clean Material 3 design
/// - Optimized for mouse and touch interaction
/// - Real-time navigation state updates
/// - Drawer mode for mobile devices
class SideNavigationRail extends StatefulWidget {
  final DeviceType deviceType;
  final bool isDrawer;

  const SideNavigationRail({
    super.key,
    required this.deviceType,
    this.isDrawer = false,
  });

  @override
  State<SideNavigationRail> createState() => _SideNavigationRailState();
}

class _SideNavigationRailState extends State<SideNavigationRail>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _widthAnimation;
  bool _isExpanded = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _widthAnimation = Tween<double>(
      begin: AppConfig.sideNavWidthCollapsed,
      end: AppConfig.sideNavWidth,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Initialize expanded state based on device type
    _isExpanded = !widget.deviceType.isMobile && !widget.isDrawer;
    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationBloc, NavigationState>(
      builder: (context, navigationState) {
        if (widget.isDrawer) {
          return _buildDrawer(context, navigationState);
        }
        
        return AnimatedBuilder(
          animation: _widthAnimation,
          builder: (context, child) {
            return Container(
              width: _widthAnimation.value,
              decoration: const BoxDecoration(
                color: AppColors.surface,
                border: Border(
                  right: BorderSide(
                    color: AppColors.border,
                    width: 0.5,
                  ),
                ),
              ),
              child: _buildContent(context, navigationState),
            );
          },
        );
      },
    );
  }

  Widget _buildDrawer(BuildContext context, NavigationState navigationState) {
    return Drawer(
      backgroundColor: AppColors.surface,
      child: SafeArea(
        child: Column(
          children: [
            // Drawer Header
            Container(
              height: 64,
              padding: ResponsiveHelper.responsivePadding(context),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.border,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.flag_rounded,
                    size: ResponsiveHelper.responsiveIconSize(context, 32),
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Quester',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            
            // Navigation Items
            Expanded(
              child: _buildNavigationItems(context, navigationState, true),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, NavigationState navigationState) {
    return Column(
      children: [
        // Collapse/Expand Button (Desktop only)
        if (widget.deviceType.isDesktop) ...[
          Container(
            height: 48,
            alignment: Alignment.center,
            child: IconButton(
              icon: AnimatedRotation(
                turns: _isExpanded ? 0.0 : 0.5,
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  Icons.chevron_left_rounded,
                  size: ResponsiveHelper.responsiveIconSize(context, 24),
                  color: AppColors.grey600,
                ),
              ),
              onPressed: _toggleExpanded,
              tooltip: _isExpanded ? 'Collapse' : 'Expand',
            ),
          ),
          const Divider(height: 1),
        ],
        
        // Navigation Items
        Expanded(
          child: _buildNavigationItems(context, navigationState, _isExpanded),
        ),
      ],
    );
  }

  Widget _buildNavigationItems(
    BuildContext context, 
    NavigationState navigationState, 
    bool showLabels,
  ) {
    final navItems = NavigationItems.getSideNavItems(context);
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: navItems.length,
      itemBuilder: (context, index) {
        final item = navItems[index];
        final isSelected = navigationState.selectedIndex == index;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          child: Material(
            color: isSelected 
                ? AppColors.primary.withValues(alpha: 0.1) 
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: () => _handleNavigation(context, index),
              child: Container(
                height: 48,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Icon(
                      isSelected ? (item.selectedIcon ?? item.icon) : item.icon,
                      size: ResponsiveHelper.responsiveIconSize(context, 24),
                      color: isSelected ? AppColors.primary : AppColors.grey600,
                    ),
                    if (showLabels) ...[
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          item.label,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: isSelected ? AppColors.primary : AppColors.onSurface,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _handleNavigation(BuildContext context, int index) {
    final navigationBloc = context.read<NavigationBloc>();
    final navItems = NavigationItems.getSideNavItems(context);
    
    if (index < navItems.length) {
      final destination = navItems[index];
      
      // Update navigation state
      navigationBloc.add(NavigateToIndex(index));
      
      // Navigate using GoRouter
      context.go(destination.route);
      
      // Close drawer if in drawer mode
      if (widget.isDrawer) {
        Navigator.of(context).pop();
      }
    }
  }
}