import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/responsive_helper.dart';
import '../../../../data/models/notification_model.dart';
import '../../../../domain/bloc/notification_bloc.dart';
import '../../../widgets/notifications/notification_list_item.dart';
import '../../../widgets/notifications/notification_empty_state.dart';
import '../../../widgets/notifications/notification_error_state.dart';
import '../../../widgets/common/loading_indicator.dart';

/// Notification sidebar with real-time updates
/// Features:
/// - Real-time notification streaming via WebSocket
/// - Mark as read/unread functionality
/// - Infinite scrolling
/// - Quick actions
/// - Responsive design
class NotificationSidebar extends StatefulWidget {
  final VoidCallback onClose;

  const NotificationSidebar({
    super.key,
    required this.onClose,
  });

  @override
  State<NotificationSidebar> createState() => _NotificationSidebarState();
}

class _NotificationSidebarState extends State<NotificationSidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _scrollController = ScrollController();
    
    _scrollController.addListener(_onScroll);
    _animationController.forward();
    
    // Load notifications when sidebar opens
    context.read<NotificationBloc>().add(const LoadNotifications());
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent * 0.9) {
      context.read<NotificationBloc>().add(const LoadMoreNotifications());
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Container(
        color: Colors.black26,
        child: Row(
          children: [
            Expanded(child: Container()), // Clickable area to close
            SlideTransition(
              position: _slideAnimation,
              child: Container(
                width: ResponsiveHelper.isDesktop(context) ? 400 : 320,
                height: double.infinity,
                decoration: const BoxDecoration(
                  color: AppColors.surface,
                  border: Border(
                    left: BorderSide(
                      color: AppColors.border,
                      width: 1,
                    ),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow,
                      offset: Offset(-2, 0),
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: _buildSidebarContent(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSidebarContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        _buildHeader(context),
        
        // Notifications List
        Expanded(
          child: BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              if (state is NotificationLoading) {
                return const LoadingIndicator(message: 'Loading notifications...');
              } else if (state is NotificationError) {
                return NotificationErrorState(
                  message: state.message,
                  onRetry: () => context.read<NotificationBloc>().add(
                    const LoadNotifications(),
                  ),
                );
              } else if (state is NotificationLoaded) {
                if (state.notifications.isEmpty) {
                  return const NotificationEmptyState();
                }
                
                return _buildNotificationsList(context, state);
              }
              
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: ResponsiveHelper.responsivePadding(context),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.notifications_rounded,
                size: ResponsiveHelper.responsiveIconSize(context, 24),
                color: AppColors.primary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Notifications',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontSize: ResponsiveHelper.responsiveFontSize(context, 20),
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.close_rounded,
                  size: ResponsiveHelper.responsiveIconSize(context, 24),
                  color: AppColors.grey600,
                ),
                onPressed: widget.onClose,
                tooltip: 'Close',
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Quick Actions
          Row(
            children: [
              Expanded(
                child: TextButton.icon(
                  icon: const Icon(Icons.done_all_rounded, size: 16),
                  label: const Text('Mark all read'),
                  onPressed: () => context.read<NotificationBloc>().add(
                    const MarkAllNotificationsAsRead(),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.refresh_rounded, size: 20),
                onPressed: () => context.read<NotificationBloc>().add(
                  const RefreshNotifications(),
                ),
                tooltip: 'Refresh',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(
    BuildContext context, 
    NotificationLoaded state,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<NotificationBloc>().add(const RefreshNotifications());
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: state.notifications.length + (state.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.notifications.length) {
            // Loading indicator for more items
            return state.isLoadingMore
                ? const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  )
                : const SizedBox.shrink();
          }
          
          final notification = state.notifications[index];
          
          return NotificationListItem(
            notification: notification,
            onTap: () => _handleNotificationTap(context, notification),
            onMarkAsRead: () => _handleMarkAsRead(context, notification),
            onDelete: () => _handleDelete(context, notification),
          );
        },
      ),
    );
  }

  void _handleNotificationTap(
    BuildContext context, 
    NotificationModel notification,
  ) {
    // Mark as read if unread
    if (!notification.isRead) {
      context.read<NotificationBloc>().add(
        MarkNotificationAsRead(notification.id),
      );
    }
    
    // Navigate to action URL if available
    if (notification.actionUrl != null) {
      try {
        // Parse the action URL and navigate accordingly
        final uri = Uri.parse(notification.actionUrl!);
        final path = uri.path;

        // Close the sidebar first
        widget.onClose();

        // Navigate based on the path
        if (path.startsWith('/quests/')) {
          Navigator.of(context).pushNamed(path);
        } else if (path.startsWith('/profile/')) {
          Navigator.of(context).pushNamed(path);
        } else if (path.startsWith('/achievements/')) {
          Navigator.of(context).pushNamed(path);
        } else {
          // Default navigation
          Navigator.of(context).pushNamed(path);
        }

        Logger.debug('Navigated to: ${notification.actionUrl}', tag: 'NotificationSidebar');
      } catch (e) {
        Logger.error('Failed to navigate to: ${notification.actionUrl}', tag: 'NotificationSidebar', error: e);
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to navigate to the requested page'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleMarkAsRead(
    BuildContext context, 
    NotificationModel notification,
  ) {
    context.read<NotificationBloc>().add(
      MarkNotificationAsRead(notification.id),
    );
  }

  void _handleDelete(
    BuildContext context, 
    NotificationModel notification,
  ) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content: const Text('Are you sure you want to delete this notification?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<NotificationBloc>().add(
                DeleteNotification(notification.id),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}