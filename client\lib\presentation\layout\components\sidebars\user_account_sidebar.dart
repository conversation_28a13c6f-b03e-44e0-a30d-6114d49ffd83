import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/responsive_helper.dart';
import '../../../../data/models/user_model.dart';
import '../../../../domain/bloc/user_bloc.dart';
import '../../../widgets/user/user_profile_header.dart';
import '../../../widgets/user/user_stats_card.dart';
import '../../../widgets/user/user_quick_actions.dart';
import '../../../widgets/user/user_recent_activity.dart';
import '../../../widgets/common/loading_indicator.dart';

/// User account sidebar with real-time data updates
/// Features:
/// - Real-time user data via WebSocket
/// - Profile information
/// - Statistics overview
/// - Quick actions
/// - Recent activity
/// - Settings access
class UserAccountSidebar extends StatefulWidget {
  final VoidCallback onClose;

  const UserAccountSidebar({
    super.key,
    required this.onClose,
  });

  @override
  State<UserAccountSidebar> createState() => _UserAccountSidebarState();
}

class _UserAccountSidebarState extends State<UserAccountSidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _scrollController = ScrollController();
    
    _animationController.forward();
    
    // Load user data when sidebar opens
    final userBloc = context.read<UserBloc>();
    if (userBloc.state is! UserLoadedState) {
      userBloc.add(const LoadUser());
    } else {
      // Refresh current user data
      userBloc.add(const RefreshUser());
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Container(
        color: Colors.black26,
        child: Row(
          children: [
            Expanded(child: Container()), // Clickable area to close
            SlideTransition(
              position: _slideAnimation,
              child: Container(
                width: ResponsiveHelper.isDesktop(context) ? 380 : 300,
                height: double.infinity,
                decoration: const BoxDecoration(
                  color: AppColors.surface,
                  border: Border(
                    left: BorderSide(
                      color: AppColors.border,
                      width: 1,
                    ),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow,
                      offset: Offset(-2, 0),
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: _buildSidebarContent(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSidebarContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        _buildHeader(context),
        
        // User Content
        Expanded(
          child: BlocBuilder<UserBloc, UserState>(
            builder: (context, state) {
              if (state is UserLoading) {
                return const LoadingIndicator(message: 'Loading user data...');
              } else if (state is UserError) {
                return _buildErrorState(context, state.message);
              } else if (state is UserLoadedState) {
                return _buildUserContent(context, state);
              }
              
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: ResponsiveHelper.responsivePadding(context),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.account_circle_rounded,
            size: ResponsiveHelper.responsiveIconSize(context, 24),
            color: AppColors.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Account',
              style: AppTextStyles.titleLarge.copyWith(
                fontSize: ResponsiveHelper.responsiveFontSize(context, 20),
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.close_rounded,
              size: ResponsiveHelper.responsiveIconSize(context, 24),
              color: AppColors.grey600,
            ),
            onPressed: widget.onClose,
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: ResponsiveHelper.responsivePadding(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: ResponsiveHelper.responsiveIconSize(context, 48),
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load user data',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.read<UserBloc>().add(const LoadUser()),
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserContent(BuildContext context, UserLoadedState state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<UserBloc>().add(const RefreshUser());
      },
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Profile Header
            UserProfileHeader(user: state.user),
            
            const SizedBox(height: 20),
            
            // Stats Overview
            UserStatsCard(user: state.user),
            
            const SizedBox(height: 20),
            
            // Quick Actions
            UserQuickActions(
              user: state.user,
              onEditProfile: () => _handleEditProfile(context, state.user),
              onViewAchievements: () => _handleViewAchievements(context),
              onSettings: () => _handleSettings(context),
            ),
            
            const SizedBox(height: 20),
            
            // Recent Activity
            UserRecentActivity(userId: state.user.id),
            
            const SizedBox(height: 20),
            
            // Sign Out Button
            _buildSignOutButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSignOutButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: OutlinedButton.icon(
        onPressed: () => _handleSignOut(context),
        icon: Icon(
          Icons.logout_rounded,
          size: ResponsiveHelper.responsiveIconSize(context, 20),
        ),
        label: const Text('Sign Out'),
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.error,
          side: const BorderSide(color: AppColors.error),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  void _handleEditProfile(BuildContext context, UserModel user) {
    widget.onClose();
    try {
      Navigator.of(context).pushNamed('/profile/edit');
    } catch (e) {
      // Fallback navigation
      Navigator.of(context).pushNamed('/profile');
    }
  }

  void _handleViewAchievements(BuildContext context) {
    widget.onClose();
    try {
      Navigator.of(context).pushNamed('/achievements');
    } catch (e) {
      // Fallback navigation
      Navigator.of(context).pushNamed('/gamification');
    }
  }

  void _handleSettings(BuildContext context) {
    widget.onClose();
    try {
      Navigator.of(context).pushNamed('/settings');
    } catch (e) {
      // Show error message if navigation fails
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Settings page is not available yet'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _handleSignOut(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onClose();
              context.read<UserBloc>().add(const LogoutUser());

              // Navigate to login screen after logout
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/login',
                (route) => false,
              );
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}