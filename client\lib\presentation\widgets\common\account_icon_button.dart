import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/enums/device_type.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../domain/bloc/user_bloc.dart';

/// Account icon button with user avatar and real-time updates
/// Features:
/// - User avatar with fallback to account icon
/// - Real-time user data updates via BLoC
/// - Responsive sizing
/// - Interactive press feedback
/// - Status indicator for online/offline
class AccountIconButton extends StatefulWidget {
  final DeviceType deviceType;
  final VoidCallback onPressed;
  final bool isSelected;

  const AccountIconButton({
    super.key,
    required this.deviceType,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  State<AccountIconButton> createState() => _AccountIconButtonState();
}

class _AccountIconButtonState extends State<AccountIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveHelper.responsiveIconSize(context, 24);
    
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Account Button
                  Material(
                    color: widget.isSelected 
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8),
                      onTap: () {
                        _animationController.forward().then((_) {
                          _animationController.reverse();
                        });
                        widget.onPressed();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: _buildAccountIcon(userState, iconSize),
                      ),
                    ),
                  ),
                  
                  // Online Status Indicator
                  if (userState is UserLoadedState && userState.user.isOnline)
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.surface,
                              spreadRadius: 1,
                              blurRadius: 0,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAccountIcon(UserState userState, double iconSize) {
    if (userState is UserLoadedState && userState.user.profileImageUrl != null) {
      // User Avatar
      return CircleAvatar(
        radius: iconSize / 2,
        backgroundImage: NetworkImage(userState.user.profileImageUrl!),
        backgroundColor: AppColors.grey200,
        onBackgroundImageError: (exception, stackTrace) {
          // Fallback to account icon on image load error
        },
        child: userState.user.profileImageUrl == null 
            ? Icon(
                Icons.account_circle_outlined,
                size: iconSize,
                color: widget.isSelected 
                    ? AppColors.primary 
                    : AppColors.onSurface,
              )
            : null,
      );
    } else {
      // Default Account Icon
      return Icon(
        Icons.account_circle_outlined,
        size: iconSize,
        color: widget.isSelected 
            ? AppColors.primary 
            : AppColors.onSurface,
      );
    }
  }
}