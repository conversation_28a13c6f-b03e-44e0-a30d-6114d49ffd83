import 'package:flutter/material.dart';

import '../../../core/enums/device_type.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';

/// Notification icon button with selectable badge count
/// Features:
/// - Animated notification icon
/// - Notification count badge
/// - Responsive sizing
/// - Interactive press feedback
class NotificationIconButton extends StatefulWidget {
  final DeviceType deviceType;
  final int notificationCount;
  final VoidCallback onPressed;
  final bool isSelected;

  const NotificationIconButton({
    super.key,
    required this.deviceType,
    required this.notificationCount,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  State<NotificationIconButton> createState() => _NotificationIconButtonState();
}

class _NotificationIconButtonState extends State<NotificationIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(NotificationIconButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate when new notifications arrive
    if (widget.notificationCount > oldWidget.notificationCount) {
      _animateBounce();
    }
  }

  void _animateBounce() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveHelper.responsiveIconSize(context, 24);
    final hasNotifications = widget.notificationCount > 0;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Icon Button
              Material(
                color: widget.isSelected 
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    _animationController.forward().then((_) {
                      _animationController.reverse();
                    });
                    widget.onPressed();
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(
                      hasNotifications 
                          ? Icons.notifications_active_rounded
                          : Icons.notifications_outlined,
                      size: iconSize,
                      color: widget.isSelected 
                          ? AppColors.primary 
                          : AppColors.onSurface,
                    ),
                  ),
                ),
              ),
              
              // Notification Badge
              if (hasNotifications)
                Positioned(
                  top: 0,
                  right: 0,
                  child: AnimatedBuilder(
                    animation: _bounceAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _bounceAnimation.value,
                        child: Container(
                          constraints: const BoxConstraints(minWidth: 16),
                          height: 18,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 1,
                          ),
                          decoration: const BoxDecoration(
                            color: AppColors.notificationBadge,
                            borderRadius: BorderRadius.all(Radius.circular(9)),
                          ),
                          child: Center(
                            child: Text(
                              _formatNotificationCount(widget.notificationCount),
                              style: AppTextStyles.notificationBadge.copyWith(
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  String _formatNotificationCount(int count) {
    if (count > 99) {
      return '99+';
    } else if (count > 0) {
      return count.toString();
    }
    return '';
  }
}