import 'package:flutter/material.dart';

import '../../../core/enums/device_type.dart';
import '../../../core/config/app_config.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';

/// Quester logo component with responsive sizing
/// Displays the Quester brand logo and text appropriate for device type
class QuesterLogo extends StatelessWidget {
  final DeviceType deviceType;
  final bool showText;
  final VoidCallback? onTap;

  const QuesterLogo({
    super.key,
    required this.deviceType,
    this.showText = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final logoSize = _getLogoSize(context);
    final showLogoText = showText && !deviceType.isMobile;
    
    return GestureDetector(
      onTap: onTap ?? () => _navigateToHome(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Logo Icon
          Container(
            width: logoSize,
            height: logoSize,
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.flag_rounded,
              size: logoSize * 0.6,
              color: AppColors.onPrimary,
            ),
          ),
          
          // Logo Text (Desktop/Tablet only)
          if (showLogoText) ...[
            SizedBox(width: ResponsiveHelper.responsiveFontSize(context, 12)),
            Text(
              AppConfig.appName,
              style: AppTextStyles.appBarTitle.copyWith(
                fontSize: ResponsiveHelper.responsiveFontSize(context, 20),
                fontWeight: FontWeight.w700,
                color: AppColors.onSurface,
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getLogoSize(BuildContext context) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 32.0;
      case DeviceType.tablet:
        return 36.0;
      case DeviceType.desktop:
        return 40.0;
      case DeviceType.largeDesktop:
        return 44.0;
    }
  }

  void _navigateToHome(BuildContext context) {
    Navigator.of(context).pushReplacementNamed('/');
  }
}