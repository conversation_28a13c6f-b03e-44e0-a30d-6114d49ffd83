import 'package:flutter/material.dart';

import '../../../core/enums/device_type.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/utils/responsive_helper.dart';

/// Search button component with responsive design
/// Features:
/// - Clean search icon
/// - Responsive sizing
/// - Interactive press feedback
/// - Consistent with app bar styling
class SearchButton extends StatefulWidget {
  final DeviceType deviceType;
  final VoidCallback onPressed;
  final bool isSelected;

  const SearchButton({
    super.key,
    required this.deviceType,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  State<SearchButton> createState() => _SearchButtonState();
}

class _SearchButtonState extends State<SearchButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveHelper.responsiveIconSize(context, 24);
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Material(
            color: widget.isSelected 
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: () {
                _animationController.forward().then((_) {
                  _animationController.reverse();
                });
                widget.onPressed();
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Icon(
                  Icons.search_rounded,
                  size: iconSize,
                  color: widget.isSelected 
                      ? AppColors.primary 
                      : AppColors.onSurface,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}