import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../data/models/notification_model.dart';

/// Individual notification list item with interactive features
/// Features:
/// - Read/unread visual states
/// - Swipe actions
/// - Priority indicators
/// - Time display
/// - Action buttons
class NotificationListItem extends StatefulWidget {
  final NotificationModel notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationListItem({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  State<NotificationListItem> createState() => _NotificationListItemState();
}

class _NotificationListItemState extends State<NotificationListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            child: Dismissible(
              key: Key(widget.notification.id),
              direction: DismissDirection.endToStart,
              background: _buildSwipeBackground(),
              onDismissed: (direction) => widget.onDelete(),
              child: Material(
                color: widget.notification.isRead 
                    ? AppColors.surface
                    : AppColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    _animationController.forward().then((_) {
                      _animationController.reverse();
                    });
                    widget.onTap();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: _buildContent(context),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSwipeBackground() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.error,
        borderRadius: BorderRadius.circular(8),
      ),
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 16),
      child: const Icon(
        Icons.delete_rounded,
        color: AppColors.onPrimary,
        size: 24,
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Priority/Type Indicator
        _buildIndicator(context),
        
        const SizedBox(width: 12),
        
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      widget.notification.title,
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: widget.notification.isRead 
                            ? FontWeight.w500
                            : FontWeight.w700,
                        color: widget.notification.isRead
                            ? AppColors.onSurface
                            : AppColors.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.notification.timeAgo,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              // Message
              Text(
                widget.notification.message,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurface.withValues(alpha: 0.8),
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              // Actions Row (for unread notifications)
              if (!widget.notification.isRead) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    TextButton.icon(
                      icon: const Icon(Icons.done_rounded, size: 16),
                      label: const Text('Mark as read'),
                      onPressed: widget.onMarkAsRead,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        
        // Unread indicator
        if (!widget.notification.isRead) ...[
          const SizedBox(width: 8),
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildIndicator(BuildContext context) {
    final iconData = _getIconData(widget.notification.iconName);
    final color = _getPriorityColor();
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        size: ResponsiveHelper.responsiveIconSize(context, 20),
        color: color,
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'trophy':
        return Icons.emoji_events_rounded;
      case 'stars':
        return Icons.star_rounded;
      case 'flag':
        return Icons.flag_rounded;
      case 'trending_up':
        return Icons.trending_up_rounded;
      case 'group_add':
        return Icons.group_add_rounded;
      case 'alternate_email':
        return Icons.alternate_email_rounded;
      case 'info':
        return Icons.info_rounded;
      case 'schedule':
        return Icons.schedule_rounded;
      default:
        return Icons.notifications_rounded;
    }
  }

  Color _getPriorityColor() {
    switch (widget.notification.priority) {
      case NotificationPriority.urgent:
        return AppColors.error;
      case NotificationPriority.high:
        return AppColors.warning;
      case NotificationPriority.normal:
        return AppColors.primary;
      case NotificationPriority.low:
        return AppColors.grey600;
    }
  }
}