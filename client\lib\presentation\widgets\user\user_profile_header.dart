import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../data/models/user_model.dart';

/// User profile header with avatar and basic info
class UserProfileHeader extends StatelessWidget {
  final UserModel user;

  const UserProfileHeader({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Avatar
          _buildAvatar(context),
          
          const SizedBox(height: 12),
          
          // Name and Username
          Text(
            user.displayNameOrUsername,
            style: AppTextStyles.titleMedium.copyWith(
              fontSize: ResponsiveHelper.responsiveFontSize(context, 16),
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          if (user.displayName != null) ...[
            const SizedBox(height: 4),
            Text(
              '@${user.username}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          const SizedBox(height: 8),
          
          // Online Status
          _buildOnlineStatus(context),
          
          const SizedBox(height: 12),
          
          // Level and Role Badge
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLevelBadge(context),
              const SizedBox(width: 12),
              _buildRoleBadge(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    final avatarSize = ResponsiveHelper.isDesktop(context) ? 80.0 : 64.0;
    
    return Stack(
      children: [
        Container(
          width: avatarSize,
          height: avatarSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: user.isOnline ? AppColors.success : AppColors.grey300,
              width: 3,
            ),
          ),
          child: ClipOval(
            child: user.profileImageUrl != null
                ? Image.network(
                    user.profileImageUrl!,
                    width: avatarSize,
                    height: avatarSize,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildDefaultAvatar(avatarSize);
                    },
                  )
                : _buildDefaultAvatar(avatarSize),
          ),
        ),
        
        // Online Status Indicator
        if (user.isOnline)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 20,
              height: 20,
              decoration: const BoxDecoration(
                color: AppColors.success,
                shape: BoxShape.circle,
                border: Border.fromBorderSide(
                  BorderSide(color: AppColors.surface, width: 2),
                ),
              ),
              child: const Icon(
                Icons.circle,
                color: AppColors.surface,
                size: 8,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDefaultAvatar(double size) {
    return Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(
        color: AppColors.grey200,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person_rounded,
        size: size * 0.6,
        color: AppColors.grey600,
      ),
    );
  }

  Widget _buildOnlineStatus(BuildContext context) {
    if (user.isOnline) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: AppColors.success,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            'Online',
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.success,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    } else {
      final lastSeenText = user.lastSeen != null
          ? _formatLastSeen(user.lastSeen!)
          : 'Offline';
          
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: AppColors.grey400,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            lastSeenText,
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.grey600,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildLevelBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.trending_up_rounded,
            size: 14,
            color: AppColors.onPrimary,
          ),
          const SizedBox(width: 4),
          Text(
            'Level ${user.currentLevel}',
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBadge(BuildContext context) {
    final roleColor = user.isPremium ? AppColors.secondary : AppColors.grey600;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: roleColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: roleColor, width: 1),
      ),
      child: Text(
        user.role.toUpperCase(),
        style: AppTextStyles.labelSmall.copyWith(
          color: roleColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return 'Last seen ${difference.inDays ~/ 7}w ago';
    }
  }
}