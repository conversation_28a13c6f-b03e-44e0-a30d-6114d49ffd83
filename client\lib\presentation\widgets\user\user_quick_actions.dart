import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../data/models/user_model.dart';

/// Quick action buttons for user account sidebar
class UserQuickActions extends StatelessWidget {
  final UserModel user;
  final VoidCallback onEditProfile;
  final VoidCallback onViewAchievements;
  final VoidCallback onSettings;

  const UserQuickActions({
    super.key,
    required this.user,
    required this.onEditProfile,
    required this.onViewAchievements,
    required this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.flash_on_rounded,
                size: ResponsiveHelper.responsiveIconSize(context, 20),
                color: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Quick Actions',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Action Buttons
          Column(
            children: [
              _buildActionButton(
                context,
                icon: Icons.edit_rounded,
                label: 'Edit Profile',
                onTap: onEditProfile,
              ),
              
              const SizedBox(height: 8),
              
              _buildActionButton(
                context,
                icon: Icons.emoji_events_rounded,
                label: 'View Achievements',
                badge: user.achievements.isNotEmpty ? user.achievements.length.toString() : null,
                onTap: onViewAchievements,
              ),
              
              const SizedBox(height: 8),
              
              _buildActionButton(
                context,
                icon: Icons.settings_rounded,
                label: 'Settings',
                onTap: onSettings,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    String? badge,
  }) {
    return Material(
      color: AppColors.surface,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: ResponsiveHelper.responsiveIconSize(context, 18),
                  color: AppColors.grey700,
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Text(
                  label,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              if (badge != null) ...[
                Container(
                  constraints: const BoxConstraints(minWidth: 20),
                  height: 20,
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                    child: Text(
                      badge,
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.onPrimary,
                        fontWeight: FontWeight.w700,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
              ],
              
              Icon(
                Icons.chevron_right_rounded,
                size: ResponsiveHelper.responsiveIconSize(context, 20),
                color: AppColors.grey400,
              ),
            ],
          ),
        ),
      ),
    );
  }
}