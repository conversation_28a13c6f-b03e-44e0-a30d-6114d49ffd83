import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../data/repositories/user_repository.dart';
import '../../../core/di/dependency_injection.dart';

/// User recent activity widget with real-time updates
class UserRecentActivity extends StatefulWidget {
  final String userId;

  const UserRecentActivity({
    super.key,
    required this.userId,
  });

  @override
  State<UserRecentActivity> createState() => _UserRecentActivityState();
}

class _UserRecentActivityState extends State<UserRecentActivity> {
  List<Map<String, dynamic>>? _activities;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadRecentActivity();
  }

  Future<void> _loadRecentActivity() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final userRepository = DependencyInjection.instance.get<UserRepository>();
      final activities = await userRepository.getUserActivity(
        widget.userId,
        limit: 5,
      );

      if (mounted) {
        setState(() {
          _activities = activities;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.history_rounded,
                size: ResponsiveHelper.responsiveIconSize(context, 20),
                color: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Recent Activity',
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (!_isLoading)
                IconButton(
                  icon: Icon(
                    Icons.refresh_rounded,
                    size: ResponsiveHelper.responsiveIconSize(context, 18),
                  ),
                  onPressed: _loadRecentActivity,
                  tooltip: 'Refresh',
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Content
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState(context);
    } else if (_error != null) {
      return _buildErrorState(context);
    } else if (_activities == null || _activities!.isEmpty) {
      return _buildEmptyState(context);
    } else {
      return _buildActivityList(context);
    }
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: ResponsiveHelper.responsiveIconSize(context, 24),
            color: AppColors.error,
          ),
          const SizedBox(height: 8),
          Text(
            'Failed to load activity',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(
            Icons.history_rounded,
            size: ResponsiveHelper.responsiveIconSize(context, 32),
            color: AppColors.grey400,
          ),
          const SizedBox(height: 8),
          Text(
            'No recent activity',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList(BuildContext context) {
    return Column(
      children: _activities!.map((activity) {
        return _buildActivityItem(context, activity);
      }).toList(),
    );
  }

  Widget _buildActivityItem(BuildContext context, Map<String, dynamic> activity) {
    final activityType = activity['activity_type'] as String? ?? '';
    final message = activity['message'] as String? ?? '';
    final timestamp = activity['timestamp'] as String?;
    
    final icon = _getActivityIcon(activityType);
    final color = _getActivityColor(activityType);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: ResponsiveHelper.responsiveIconSize(context, 16),
              color: color,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                if (timestamp != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    _formatTimestamp(timestamp),
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String activityType) {
    switch (activityType) {
      case 'points_earned':
        return Icons.star_rounded;
      case 'achievement_earned':
        return Icons.emoji_events_rounded;
      case 'quest_completed':
        return Icons.flag_rounded;
      case 'level_up':
        return Icons.trending_up_rounded;
      case 'social_interaction':
        return Icons.people_rounded;
      default:
        return Icons.circle_rounded;
    }
  }

  Color _getActivityColor(String activityType) {
    switch (activityType) {
      case 'points_earned':
        return AppColors.warning;
      case 'achievement_earned':
        return AppColors.secondary;
      case 'quest_completed':
        return AppColors.success;
      case 'level_up':
        return AppColors.primary;
      case 'social_interaction':
        return AppColors.info;
      default:
        return AppColors.grey600;
    }
  }

  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return timestamp;
    }
  }
}