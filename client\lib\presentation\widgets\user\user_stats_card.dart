import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/responsive_helper.dart';
import '../../../data/models/user_model.dart';

/// User statistics card with progress indicators
class UserStatsCard extends StatelessWidget {
  final UserModel user;

  const UserStatsCard({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            offset: Offset(0, 1),
            blurRadius: 3,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.analytics_rounded,
                size: ResponsiveHelper.responsiveIconSize(context, 20),
                color: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Statistics',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Level Progress
          _buildLevelProgress(context),
          
          const SizedBox(height: 16),
          
          // Stats Grid
          _buildStatsGrid(context),
        ],
      ),
    );
  }

  Widget _buildLevelProgress(BuildContext context) {
    final progressPercentage = user.levelProgressPercentage;
    final pointsToNext = user.pointsToNextLevel;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Level ${user.currentLevel}',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '$pointsToNext points to next level',
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Progress Bar
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: AppColors.grey200,
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progressPercentage,
            child: Container(
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsGrid(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.star_rounded,
            label: 'Points',
            value: _formatNumber(user.totalPoints),
            color: AppColors.warning,
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.local_fire_department_rounded,
            label: 'Streak',
            value: '${user.currentStreak} days',
            color: AppColors.error,
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.emoji_events_rounded,
            label: 'Achievements',
            value: user.achievements.length.toString(),
            color: AppColors.secondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: ResponsiveHelper.responsiveIconSize(context, 20),
            color: color,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          value,
          style: AppTextStyles.titleSmall.copyWith(
            fontSize: ResponsiveHelper.responsiveFontSize(context, 14),
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 2),
        
        Text(
          label,
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}