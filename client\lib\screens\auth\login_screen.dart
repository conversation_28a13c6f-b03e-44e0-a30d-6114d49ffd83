import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../features/auth/bloc/auth_bloc.dart';
import '../../features/auth/bloc/auth_event.dart';
import '../../features/auth/bloc/auth_state.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/auth/social_login_buttons.dart';
import 'register_screen.dart';
import 'forgot_password_screen.dart';
import 'two_factor_verification_screen.dart';

/// Login screen with comprehensive authentication options
class LoginScreen extends StatefulWidget {
  final String? initialEmail;
  final String? organizationId;
  final String? invitationToken;

  const LoginScreen({
    super.key,
    this.initialEmail,
    this.organizationId,
    this.invitationToken,
  });

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _rememberMe = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize form with provided email
    if (widget.initialEmail != null) {
      _emailController.text = widget.initialEmail!;
    }

    // Setup animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: BlocListener<AuthBloc, AuthenticationState>(
        listener: _handleAuthenticationStateChange,
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    colorScheme.primary.withValues(alpha: 0.1),
                    colorScheme.surface,
                    colorScheme.secondary.withValues(alpha: 0.05),
                  ],
                ),
              ),
            ),

            // Main content
            SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: _buildLoginForm(context, theme),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Loading overlay
            BlocBuilder<AuthBloc, AuthenticationState>(
              builder: (context, state) {
                return LoadingOverlay(
                  isLoading: state.isLoading,
                  loadingText: 'Signing in...',
                  child: const SizedBox.shrink(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginForm(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 8,
      shadowColor: theme.colorScheme.primary.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildHeader(theme),
              const SizedBox(height: 32),
              _buildEmailField(theme),
              const SizedBox(height: 16),
              _buildPasswordField(theme),
              const SizedBox(height: 16),
              _buildRememberMeAndForgotPassword(theme),
              const SizedBox(height: 24),
              _buildLoginButton(theme),
              const SizedBox(height: 12),
              _buildDemoLoginButton(theme),
              const SizedBox(height: 16),
              _buildDivider(theme),
              const SizedBox(height: 16),
              _buildSocialLogin(theme),
              const SizedBox(height: 24),
              _buildRegisterLink(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        // Logo or icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.secondary,
              ],
            ),
          ),
          child: Icon(
            Icons.rocket_launch,
            size: 40,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Welcome to Quester',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Sign in to continue your quest',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField(ThemeData theme) {
    return TextFormField(
      controller: _emailController,
      focusNode: _emailFocusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: 'Email Address',
        hintText: 'Enter your email',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Email is required';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Please enter a valid email address';
        }
        return null;
      },
      onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
    );
  }

  Widget _buildPasswordField(ThemeData theme) {
    return TextFormField(
      controller: _passwordController,
      focusNode: _passwordFocusNode,
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Enter your password',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password is required';
        }
        if (value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        return null;
      },
      onFieldSubmitted: (_) => _handleLogin(),
    );
  }

  Widget _buildRememberMeAndForgotPassword(ThemeData theme) {
    return Row(
      children: [
        Checkbox(
          value: _rememberMe,
          onChanged: (value) {
            setState(() {
              _rememberMe = value ?? false;
            });
          },
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Remember me',
            style: theme.textTheme.bodyMedium,
          ),
        ),
        TextButton(
          onPressed: () => _navigateToForgotPassword(),
          child: Text(
            'Forgot Password?',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton(ThemeData theme) {
    return BlocBuilder<AuthBloc, AuthenticationState>(
      builder: (context, state) {
        return FilledButton(
          onPressed: state.isLoading ? null : _handleLogin,
          style: FilledButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (state.isLoading) ...[
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],
              Text(
                state.isLoading ? 'Signing In...' : 'Sign In',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDemoLoginButton(ThemeData theme) {
    return BlocBuilder<AuthBloc, AuthenticationState>(
      builder: (context, state) {
        return OutlinedButton.icon(
          onPressed: state.isLoading ? null : _handleDemoLogin,
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 14),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            side: BorderSide(
              color: theme.colorScheme.primary.withValues(alpha: 0.7),
              width: 1.5,
            ),
          ),
          icon: Icon(
            Icons.psychology,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          label: Text(
            'Try Demo Login',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
        );
      },
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'or continue with',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialLogin(ThemeData theme) {
    return SocialLoginButtons(
      onGoogleLogin: _handleGoogleLogin,
      onMicrosoftLogin: _handleMicrosoftLogin,
      onGitHubLogin: _handleGitHubLogin,
    );
  }

  Widget _buildRegisterLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        TextButton(
          onPressed: () => _navigateToRegister(),
          child: Text(
            'Sign Up',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  void _handleAuthenticationStateChange(BuildContext context, AuthenticationState state) {
    if (state is AuthError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.onError,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      state.userFriendlyMessage,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onError,
                      ),
                    ),
                    if (state.suggestedAction != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        state.suggestedAction!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onError.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 6),
          action: state.isRecoverable
              ? SnackBarAction(
                  label: 'Retry',
                  textColor: Theme.of(context).colorScheme.onError,
                  onPressed: () {
                    if (state.isInvalidCredentials) {
                      _passwordController.clear();
                      _passwordFocusNode.requestFocus();
                    }
                  },
                )
              : null,
        ),
      );
    } else if (state is AuthRequiresTwoFactor) {
      _navigateToTwoFactorVerification(
        state.sessionToken,
        state.availableMethods,
      );
    } else if (state is AuthAuthenticated) {
      if (state.requiresEmailVerification) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.email_outlined, color: Colors.white),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Please check your email to verify your account.',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 8),
          ),
        );
      }
      
      // Navigate to main app
      Navigator.pushReplacementNamed(context, '/home');
    }
  }

  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      FocusScope.of(context).unfocus();
      
      context.read<AuthBloc>().add(AuthLoginRequested(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        rememberMe: _rememberMe,
        organizationId: widget.organizationId,
      ));
    }
  }

  void _handleDemoLogin() {
    FocusScope.of(context).unfocus();
    
    // Pre-fill the form with demo data for user to see
    _emailController.text = '<EMAIL>';
    _passwordController.text = 'demo123';
    _rememberMe = true;
    
    // Trigger the demo login
    context.read<AuthBloc>().add(const AuthDemoLoginRequested());
  }

  void _handleGoogleLogin() async {
    if (!mounted) return;

    try {
      // Mock Google OAuth flow
      await Future.delayed(const Duration(seconds: 1)); // Simulate OAuth flow

      if (!mounted) return;

      // In a real implementation, you would:
      // 1. Open Google OAuth URL
      // 2. Handle the callback with authorization code
      // 3. Send the code to your backend

      // Mock successful response (in real app, send mockAuthCode to backend)
      final response = {
        'success': true,
        'user': {'id': '123', 'email': '<EMAIL>', 'name': 'Google User'},
        'accessToken': 'mock_access_token',
      };

      if (!mounted) return;

      if (response['success'] == true) {
        // Handle successful login
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google login successful!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to home or handle login success
        Navigator.of(context).pushReplacementNamed('/home');
      } else {
        throw Exception(response['error'] ?? 'Google login failed');
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Google login failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleMicrosoftLogin() async {
    if (!mounted) return;

    try {
      // Mock Microsoft OAuth flow
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      // Mock successful response
      final response = {
        'success': true,
        'user': {'id': '456', 'email': '<EMAIL>', 'name': 'Microsoft User'},
        'accessToken': 'mock_microsoft_token',
      };

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microsoft login successful!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Microsoft login failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleGitHubLogin() async {
    if (!mounted) return;

    try {
      // Mock GitHub OAuth flow
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      // Mock successful response
      final response = {
        'success': true,
        'user': {'id': '789', 'email': '<EMAIL>', 'name': 'GitHub User'},
        'accessToken': 'mock_github_token',
      };

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('GitHub login successful!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('GitHub login failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _navigateToRegister() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterScreen(
          initialEmail: _emailController.text.trim(),
          organizationId: widget.organizationId,
          invitationToken: widget.invitationToken,
        ),
      ),
    );
  }

  void _navigateToForgotPassword() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ForgotPasswordScreen(
          initialEmail: _emailController.text.trim(),
        ),
      ),
    );
  }

  void _navigateToTwoFactorVerification(
    String sessionToken,
    List<TwoFactorMethod> availableMethods,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TwoFactorVerificationScreen(
          sessionToken: sessionToken,
          availableMethods: availableMethods.map<String>((method) => method.toString().split('.').last).toList(),
        ),
      ),
    );
  }
}