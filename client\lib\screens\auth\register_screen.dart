import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/auth/bloc/auth_bloc.dart';
import '../../features/auth/bloc/auth_event.dart';
import '../../features/auth/bloc/auth_state.dart';
import 'login_screen.dart';

/// Registration screen with comprehensive form validation and enterprise features
class RegisterScreen extends StatefulWidget {
  final String? initialEmail;
  final String? organizationId;
  final String? invitationToken;

  const RegisterScreen({
    super.key,
    this.initialEmail,
    this.organizationId,
    this.invitationToken,
  });

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _organizationNameController = TextEditingController();
  
  // Focus nodes for better UX
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  final _firstNameFocusNode = FocusNode();
  final _lastNameFocusNode = FocusNode();
  final _organizationNameFocusNode = FocusNode();

  // UI state
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _acceptTerms = false;
  bool _subscribeToNewsletter = false;
  bool _isCreatingOrganization = false;
  
  // Password strength
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.grey;

  // Animation controllers
  late AnimationController _animationController;
  late AnimationController _strengthController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _strengthAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize form with provided email
    if (widget.initialEmail != null) {
      _emailController.text = widget.initialEmail!;
    }
    
    // Show organization creation option if no invitation token
    _isCreatingOrganization = widget.invitationToken == null;

    // Setup animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _strengthController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _strengthAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_strengthController);

    _animationController.forward();
    
    // Add password strength listener
    _passwordController.addListener(_evaluatePasswordStrength);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _strengthController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _organizationNameController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _firstNameFocusNode.dispose();
    _lastNameFocusNode.dispose();
    _organizationNameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: BlocListener<AuthBloc, AuthenticationState>(
        listener: _handleAuthenticationStateChange,
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    colorScheme.primary.withValues(alpha: 0.1),
                    colorScheme.secondary.withValues(alpha: 0.05),
                  ],
                ),
              ),
            ),
            
            // Main content
            SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(maxWidth: 400),
                            child: Card(
                              elevation: 8,
                              child: Padding(
                                padding: const EdgeInsets.all(32),
                                child: Form(
                                  key: _formKey,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildHeader(theme),
                                      const SizedBox(height: 32),
                                      _buildPersonalInfoSection(theme),
                                      const SizedBox(height: 24),
                                      _buildOrganizationSection(theme),
                                      const SizedBox(height: 24),
                                      _buildPasswordSection(theme),
                                      const SizedBox(height: 24),
                                      _buildTermsAndNewsletter(theme),
                                      const SizedBox(height: 32),
                                      _buildRegisterButton(theme),
                                      const SizedBox(height: 12),
                                      _buildDemoRegisterButton(theme),
                                      const SizedBox(height: 24),
                                      _buildDivider(theme),
                                      const SizedBox(height: 24),
                                      _buildSocialButtons(),
                                      const SizedBox(height: 24),
                                      _buildLoginLink(theme),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            
            // Loading overlay
            BlocBuilder<AuthBloc, AuthenticationState>(
              buildWhen: (previous, current) => current.isLoading != previous.isLoading,
              builder: (context, state) {
                if (state.isLoading) {
                  return Container(
                    color: Colors.black.withValues(alpha: 0.5),
                    child: const Center(
                      child: Card(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Creating your account...'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.secondary,
              ],
            ),
          ),
          child: Icon(
            Icons.rocket_launch,
            size: 40,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Join Quester',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.invitationToken != null
              ? 'Complete your account setup'
              : 'Create your account to start your quest journey',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildFirstNameField(theme),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildLastNameField(theme),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildEmailField(theme),
      ],
    );
  }

  Widget _buildFirstNameField(ThemeData theme) {
    return TextFormField(
      controller: _firstNameController,
      focusNode: _firstNameFocusNode,
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
      decoration: InputDecoration(
        labelText: 'First Name',
        hintText: 'Enter your first name',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'First name is required';
        }
        if (value.trim().length < 2) {
          return 'First name must be at least 2 characters';
        }
        return null;
      },
      onFieldSubmitted: (_) => _lastNameFocusNode.requestFocus(),
    );
  }

  Widget _buildLastNameField(ThemeData theme) {
    return TextFormField(
      controller: _lastNameController,
      focusNode: _lastNameFocusNode,
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
      decoration: InputDecoration(
        labelText: 'Last Name',
        hintText: 'Enter your last name',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Last name is required';
        }
        if (value.trim().length < 2) {
          return 'Last name must be at least 2 characters';
        }
        return null;
      },
      onFieldSubmitted: (_) => _emailFocusNode.requestFocus(),
    );
  }

  Widget _buildEmailField(ThemeData theme) {
    return TextFormField(
      controller: _emailController,
      focusNode: _emailFocusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: _isCreatingOrganization ? TextInputAction.next : TextInputAction.next,
      decoration: InputDecoration(
        labelText: 'Email Address',
        hintText: 'Enter your email address',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Email is required';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
          return 'Please enter a valid email address';
        }
        return null;
      },
      onFieldSubmitted: (_) {
        if (_isCreatingOrganization) {
          _organizationNameFocusNode.requestFocus();
        } else {
          _passwordFocusNode.requestFocus();
        }
      },
    );
  }

  Widget _buildOrganizationSection(ThemeData theme) {
    if (widget.invitationToken != null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.business,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Organization',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Create your own organization or join an existing one later',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 16),
        CheckboxListTile(
          title: const Text('Create a new organization'),
          subtitle: Text(
            'You\'ll be the owner and can invite team members',
            style: theme.textTheme.bodySmall,
          ),
          value: _isCreatingOrganization,
          onChanged: (value) {
            setState(() {
              _isCreatingOrganization = value ?? false;
              if (!_isCreatingOrganization) {
                _organizationNameController.clear();
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        if (_isCreatingOrganization) ...[
          const SizedBox(height: 16),
          _buildOrganizationNameField(theme),
        ],
      ],
    );
  }

  Widget _buildOrganizationNameField(ThemeData theme) {
    return TextFormField(
      controller: _organizationNameController,
      focusNode: _organizationNameFocusNode,
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
      decoration: InputDecoration(
        labelText: 'Organization Name',
        hintText: 'Enter your organization name',
        prefixIcon: const Icon(Icons.domain),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: _isCreatingOrganization
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Organization name is required';
              }
              if (value.trim().length < 3) {
                return 'Organization name must be at least 3 characters';
              }
              return null;
            }
          : null,
      onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
    );
  }

  Widget _buildPasswordSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Security',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),
        _buildPasswordField(theme),
        const SizedBox(height: 8),
        _buildPasswordStrengthIndicator(theme),
        const SizedBox(height: 16),
        _buildConfirmPasswordField(theme),
      ],
    );
  }

  Widget _buildPasswordField(ThemeData theme) {
    return TextFormField(
      controller: _passwordController,
      focusNode: _passwordFocusNode,
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Create a strong password',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password is required';
        }
        if (value.length < 8) {
          return 'Password must be at least 8 characters';
        }
        if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
          return 'Password must contain uppercase, lowercase, and numbers';
        }
        return null;
      },
      onFieldSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
    );
  }

  Widget _buildPasswordStrengthIndicator(ThemeData theme) {
    return AnimatedBuilder(
      animation: _strengthAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: _passwordStrength * _strengthAnimation.value,
                    backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.3),
                    valueColor: AlwaysStoppedAnimation<Color>(_passwordStrengthColor),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _passwordStrengthText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _passwordStrengthColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (_passwordController.text.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Use 8+ characters with uppercase, lowercase, numbers, and symbols',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildConfirmPasswordField(ThemeData theme) {
    return TextFormField(
      controller: _confirmPasswordController,
      focusNode: _confirmPasswordFocusNode,
      obscureText: _obscureConfirmPassword,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        labelText: 'Confirm Password',
        hintText: 'Confirm your password',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please confirm your password';
        }
        if (value != _passwordController.text) {
          return 'Passwords do not match';
        }
        return null;
      },
      onFieldSubmitted: (_) => _handleRegister(),
    );
  }

  Widget _buildTermsAndNewsletter(ThemeData theme) {
    return Column(
      children: [
        CheckboxListTile(
          title: RichText(
            text: TextSpan(
              style: theme.textTheme.bodyMedium,
              children: [
                const TextSpan(text: 'I agree to the '),
                TextSpan(
                  text: 'Terms of Service',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
                const TextSpan(text: ' and '),
                TextSpan(
                  text: 'Privacy Policy',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
            ),
          ),
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          dense: true,
        ),
        CheckboxListTile(
          title: const Text('Subscribe to product updates and tips'),
          subtitle: const Text('We\'ll send you occasional emails about new features'),
          value: _subscribeToNewsletter,
          onChanged: (value) {
            setState(() {
              _subscribeToNewsletter = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          dense: true,
        ),
      ],
    );
  }

  Widget _buildRegisterButton(ThemeData theme) {
    return BlocBuilder<AuthBloc, AuthenticationState>(
      buildWhen: (previous, current) => current.isLoading != previous.isLoading,
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: state.isLoading || !_acceptTerms ? null : _handleRegister,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: state.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    'Create Account',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildDemoRegisterButton(ThemeData theme) {
    return BlocBuilder<AuthBloc, AuthenticationState>(
      buildWhen: (previous, current) => current.isLoading != previous.isLoading,
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: state.isLoading ? null : _handleDemoRegister,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              side: BorderSide(
                color: theme.colorScheme.primary.withValues(alpha: 0.7),
                width: 1.5,
              ),
            ),
            icon: Icon(
              Icons.psychology,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            label: Text(
              'Try Demo Registration',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Divider(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'or continue with',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Divider(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
        ),
      ],
    );
  }

  Widget _buildSocialButtons() {
    return Column(
      children: [
        _buildSocialButton(
          icon: Icons.login,
          label: 'Continue with Google',
          onPressed: _handleGoogleSignUp,
        ),
        const SizedBox(height: 8),
        _buildSocialButton(
          icon: Icons.business,
          label: 'Continue with Microsoft',
          onPressed: _handleMicrosoftSignUp,
        ),
        const SizedBox(height: 8),
        _buildSocialButton(
          icon: Icons.code,
          label: 'Continue with GitHub',
          onPressed: _handleGitHubSignUp,
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        TextButton(
          onPressed: _navigateToLogin,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: const Text(
            'Sign In',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  void _evaluatePasswordStrength() {
    final password = _passwordController.text;
    double strength = 0.0;
    String strengthText = '';
    Color strengthColor = Colors.grey;

    if (password.isEmpty) {
      strength = 0.0;
      strengthText = '';
      strengthColor = Colors.grey;
    } else if (password.length < 6) {
      strength = 0.2;
      strengthText = 'Weak';
      strengthColor = Colors.red;
    } else if (password.length < 8) {
      strength = 0.4;
      strengthText = 'Fair';
      strengthColor = Colors.orange;
    } else {
      // Check for different character types
      bool hasLower = password.contains(RegExp(r'[a-z]'));
      bool hasUpper = password.contains(RegExp(r'[A-Z]'));
      bool hasDigit = password.contains(RegExp(r'\d'));
      bool hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

      int complexity = 0;
      if (hasLower) complexity++;
      if (hasUpper) complexity++;
      if (hasDigit) complexity++;
      if (hasSpecial) complexity++;

      if (complexity == 1) {
        strength = 0.4;
        strengthText = 'Fair';
        strengthColor = Colors.orange;
      } else if (complexity == 2) {
        strength = 0.6;
        strengthText = 'Good';
        strengthColor = Colors.blue;
      } else if (complexity == 3) {
        strength = 0.8;
        strengthText = 'Strong';
        strengthColor = Colors.green;
      } else if (complexity == 4 && password.length >= 12) {
        strength = 1.0;
        strengthText = 'Very Strong';
        strengthColor = Colors.green.shade700;
      } else {
        strength = 0.8;
        strengthText = 'Strong';
        strengthColor = Colors.green;
      }
    }

    setState(() {
      _passwordStrength = strength;
      _passwordStrengthText = strengthText;
      _passwordStrengthColor = strengthColor;
    });

    if (strength > 0) {
      _strengthController.forward();
    } else {
      _strengthController.reverse();
    }
  }

  void _handleAuthenticationStateChange(BuildContext context, AuthenticationState state) {
    if (state is AuthError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      state.message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              if (state.passwordPolicy != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Password requirements: ${state.passwordPolicy}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onError.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 6),
        ),
      );
    } else if (state is AuthRegistrationSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  state.requiresEmailVerification
                      ? 'Account created! Please check your email to verify your account.'
                      : 'Account created successfully!',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
        ),
      );

      // Navigate back to login
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _navigateToLogin();
        }
      });
    }
  }

  void _handleRegister() {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please accept the Terms of Service and Privacy Policy'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      FocusScope.of(context).unfocus();

      // Create display name from first and last name
      final displayName = '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}';

      context.read<AuthBloc>().add(AuthRegisterRequested(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        displayName: displayName,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        organizationName: _isCreatingOrganization
            ? _organizationNameController.text.trim()
            : null,
        invitationToken: widget.invitationToken,
        acceptTerms: _acceptTerms,
        subscribeToNewsletter: _subscribeToNewsletter,
      ));
    }
  }

  void _handleDemoRegister() {
    FocusScope.of(context).unfocus();
    
    // Pre-fill the form with demo data for user to see
    _firstNameController.text = 'Demo';
    _lastNameController.text = 'User';
    _emailController.text = '<EMAIL>';
    _passwordController.text = 'demo123';
    _confirmPasswordController.text = 'demo123';
    _acceptTerms = true;
    _subscribeToNewsletter = false;
    _isCreatingOrganization = true;
    _organizationNameController.text = 'Demo Organization';
    
    // Update the UI to show the changes
    setState(() {});
    
    // Trigger the demo registration
    context.read<AuthBloc>().add(const AuthDemoRegisterRequested());
  }

  void _navigateToLogin() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => LoginScreen(
          initialEmail: _emailController.text.trim(),
          organizationId: widget.organizationId,
        ),
      ),
    );
  }

  void _handleGoogleSignUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Google sign-up coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleMicrosoftSignUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Microsoft sign-up coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleGitHubSignUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('GitHub sign-up coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}