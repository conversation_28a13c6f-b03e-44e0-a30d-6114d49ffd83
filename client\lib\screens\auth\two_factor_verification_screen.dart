import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Two-factor verification screen
class TwoFactorVerificationScreen extends StatefulWidget {
  final String sessionToken;
  final List<String> availableMethods;

  const TwoFactorVerificationScreen({
    super.key,
    required this.sessionToken,
    this.availableMethods = const ['totp'],
  });

  @override
  State<TwoFactorVerificationScreen> createState() => _TwoFactorVerificationScreenState();
}

class _TwoFactorVerificationScreenState extends State<TwoFactorVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  String _selectedMethod = 'totp';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.availableMethods.isNotEmpty) {
      _selectedMethod = widget.availableMethods.first;
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Two-Factor Authentication'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 32),
                
                Icon(
                  Icons.security,
                  size: 64,
                  color: theme.primaryColor,
                ),
                
                const SizedBox(height: 24),
                
                Text(
                  'Verify your identity',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  _getMethodDescription(_selectedMethod),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                // Method Selection (if multiple methods available)
                if (widget.availableMethods.length > 1) ...[
                  Text(
                    'Verification Method',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedMethod,
                    decoration: const InputDecoration(
                      prefixIcon: Icon(Icons.security),
                    ),
                    items: widget.availableMethods.map((method) {
                      return DropdownMenuItem(
                        value: method,
                        child: Text(_getMethodName(method)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedMethod = value;
                          _codeController.clear();
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],
                
                // Verification Code Input
                TextFormField(
                  controller: _codeController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  decoration: InputDecoration(
                    labelText: 'Verification Code',
                    prefixIcon: const Icon(Icons.password),
                    hintText: _getCodeHint(_selectedMethod),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the verification code';
                    }
                    if (value.length < 6) {
                      return 'Verification code must be 6 digits';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 24),
                
                // Verify Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleVerification,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Verify',
                        style: TextStyle(fontSize: 16),
                      ),
                ),
                
                const SizedBox(height: 16),
                
                // Alternative Actions
                if (_selectedMethod == 'totp') ...[
                  TextButton(
                    onPressed: _handleResendCode,
                    child: const Text('Didn\'t receive a code? Try SMS'),
                  ),
                ] else ...[
                  TextButton(
                    onPressed: _handleResendCode,
                    child: const Text('Resend verification code'),
                  ),
                ],
                
                const SizedBox(height: 8),
                
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                
                const Spacer(),
                
                // Help Text
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.help_outline,
                        color: theme.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Having trouble? Contact support for assistance with two-factor authentication.',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getMethodName(String method) {
    switch (method.toLowerCase()) {
      case 'totp':
        return 'Authenticator App';
      case 'sms':
        return 'SMS';
      case 'email':
        return 'Email';
      default:
        return method.toUpperCase();
    }
  }

  String _getMethodDescription(String method) {
    switch (method.toLowerCase()) {
      case 'totp':
        return 'Enter the 6-digit code from your authenticator app';
      case 'sms':
        return 'Enter the 6-digit code sent to your phone';
      case 'email':
        return 'Enter the 6-digit code sent to your email';
      default:
        return 'Enter your verification code';
    }
  }

  String _getCodeHint(String method) {
    switch (method.toLowerCase()) {
      case 'totp':
        return '123456';
      case 'sms':
        return 'SMS code';
      case 'email':
        return 'Email code';
      default:
        return '123456';
    }
  }

  Future<void> _handleVerification() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Implement 2FA verification logic
        final code = _codeController.text.trim();

        // Validate code format
        if (code.length != 6 || !RegExp(r'^\d+$').hasMatch(code)) {
          throw Exception('Please enter a valid 6-digit code');
        }

        // Mock API call for verification
        await Future.delayed(const Duration(seconds: 2));

        // Mock verification - accept any 6-digit code for demo
        final isValid = code.length == 6;

        if (!isValid) {
          throw Exception('Invalid verification code');
        }

        if (mounted) {
          // Navigate to home screen after successful verification
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Verification successful!'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to home
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Verification failed: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _handleResendCode() async {
    try {
      // Mock API call to resend code
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Verification code resent!'),
            backgroundColor: Colors.green,
          ),
        );

        // Clear the current code input
        _codeController.clear();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resend code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}