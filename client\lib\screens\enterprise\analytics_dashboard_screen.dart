import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/charts/analytics_charts.dart';

/// Advanced analytics dashboard for enterprise users
class AnalyticsDashboardScreen extends StatefulWidget {
  final String organizationId;

  const AnalyticsDashboardScreen({
    super.key,
    required this.organizationId,
  });

  @override
  State<AnalyticsDashboardScreen> createState() => _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
  String _selectedTimePeriod = 'week';
  String _selectedMetric = 'userEngagement';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedTimePeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'hour', child: Text('Last 24 Hours')),
              const PopupMenuItem(value: 'day', child: Text('Last 7 Days')),
              const PopupMenuItem(value: 'week', child: Text('Last 4 Weeks')),
              const PopupMenuItem(value: 'month', child: Text('Last 12 Months')),
            ],
          ),
          IconButton(
            onPressed: () => _exportReport(),
            icon: const Icon(Icons.download),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMetricSelector(),
            _buildKeyMetrics(),
            _buildEngagementChart(),
            _buildDetailedMetrics(),
            _buildTeamPerformance(),
            _buildGamificationInsights(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Primary Metric',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildMetricChip('User Engagement', 'userEngagement'),
                _buildMetricChip('Quest Completion', 'questCompletion'),
                _buildMetricChip('Team Performance', 'teamPerformance'),
                _buildMetricChip('Gamification', 'gamificationEngagement'),
                _buildMetricChip('System Usage', 'systemUsage'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricChip(String label, String value) {
    final isSelected = _selectedMetric == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedMetric = value;
          });
        },
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
      ),
    );
  }

  Widget _buildKeyMetrics() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Key Metrics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.8,
            children: [
              _buildMetricCard(
                'Daily Active Users',
                '87',
                '+5.2%',
                Icons.people,
                Colors.blue,
                true,
              ),
              _buildMetricCard(
                'Quest Completion Rate',
                '81.3%',
                '+2.1%',
                Icons.task_alt,
                Colors.green,
                true,
              ),
              _buildMetricCard(
                'Average Session Time',
                '31m',
                '-1.5%',
                Icons.access_time,
                Colors.orange,
                false,
              ),
              _buildMetricCard(
                'Team Collaboration',
                '92.1%',
                '+7.8%',
                Icons.groups,
                Colors.purple,
                true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String change,
    IconData icon,
    Color color,
    bool isPositive,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                Icon(icon, color: color, size: 20),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Row(
              children: [
                Icon(
                  isPositive ? Icons.trending_up : Icons.trending_down,
                  size: 16,
                  color: isPositive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  change,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isPositive ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEngagementChart() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Line chart for engagement trends
          AnalyticsLineChart(
            dataPoints: _generateEngagementData(),
            title: 'User Engagement Trends - ${_getTimePeriodLabel()}',
            primaryColor: Colors.blue,
            yAxisLabel: 'Active Users',
            xAxisLabel: 'Time',
            maxY: 120,
            minY: 0,
          ),
          const SizedBox(height: 16),
          // Bar chart for daily metrics
          AnalyticsBarChart(
            barGroups: _generateDailyMetrics(),
            title: 'Daily Activity Breakdown',
            primaryColor: Colors.green,
            maxY: 100,
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          ),
          const SizedBox(height: 16),
          // Pie chart for team distribution
          AnalyticsPieChart(
            sections: _generateTeamDistribution(),
            title: 'Team Activity Distribution',
            radius: 100,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedMetrics() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detailed Analytics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                _buildDetailedMetricRow('Total Users', '156', '12 new this week'),
                const Divider(height: 1),
                _buildDetailedMetricRow('Active Users (7d)', '142', '91% retention rate'),
                const Divider(height: 1),
                _buildDetailedMetricRow('Quests Created', '89', '23 pending approval'),
                const Divider(height: 1),
                _buildDetailedMetricRow('Quests Completed', '278', '81.3% completion rate'),
                const Divider(height: 1),
                _buildDetailedMetricRow('Points Awarded', '45,670', 'Avg 292 per user'),
                const Divider(height: 1),
                _buildDetailedMetricRow('Achievements Unlocked', '234', '1.5 per user'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedMetricRow(String title, String value, String subtitle) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Text(
        value,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTeamPerformance() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Performing Teams',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 5,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final teams = [
                  {'name': 'Backend Development', 'score': 95, 'members': 8, 'quests': 23},
                  {'name': 'Frontend Team', 'score': 92, 'members': 6, 'quests': 19},
                  {'name': 'DevOps Engineers', 'score': 89, 'members': 4, 'quests': 15},
                  {'name': 'QA Team', 'score': 87, 'members': 5, 'quests': 18},
                  {'name': 'Design Team', 'score': 84, 'members': 3, 'quests': 12},
                ];
                
                final team = teams[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getTeamColor(index),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(team['name'] as String),
                  subtitle: Text('${team['members']} members • ${team['quests']} quests'),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getTeamColor(index).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${team['score']}%',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getTeamColor(index),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGamificationInsights() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gamification Insights',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildGamificationCard(
                'Leaderboard\nParticipation',
                '89.7%',
                Icons.leaderboard,
                Colors.blue,
              ),
              _buildGamificationCard(
                'Rewards\nClaimed',
                '145',
                Icons.redeem,
                Colors.green,
              ),
              _buildGamificationCard(
                'Average\nStreak',
                '12 days',
                Icons.local_fire_department,
                Colors.orange,
              ),
              _buildGamificationCard(
                'Achievement\nRate',
                '76.3%',
                Icons.emoji_events,
                Colors.purple,
              ),
            ],
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildGamificationCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTeamColor(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];
    return colors[index % colors.length];
  }

  String _getTimePeriodLabel() {
    switch (_selectedTimePeriod) {
      case 'hour':
        return 'Last 24 Hours';
      case 'day':
        return 'Last 7 Days';
      case 'week':
        return 'Last 4 Weeks';
      case 'month':
        return 'Last 12 Months';
      default:
        return 'Last 7 Days';
    }
  }

  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Report'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose export format:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('PDF Report'),
              onTap: () {
                Navigator.of(context).pop();
                _showExportProgress('PDF');
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Excel Spreadsheet'),
              onTap: () {
                Navigator.of(context).pop();
                _showExportProgress('Excel');
              },
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('JSON Data'),
              onTap: () {
                Navigator.of(context).pop();
                _showExportProgress('JSON');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showExportProgress(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Exporting $format report...'),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            // Open exported file (mock implementation)
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Opening exported $format file...'),
                backgroundColor: Colors.blue,
              ),
            );
          },
        ),
      ),
    );
  }

  // Chart data generation methods
  List<FlSpot> _generateEngagementData() {
    return [
      const FlSpot(0, 45),
      const FlSpot(1, 67),
      const FlSpot(2, 89),
      const FlSpot(3, 78),
      const FlSpot(4, 95),
      const FlSpot(5, 87),
      const FlSpot(6, 92),
    ];
  }

  List<BarChartGroupData> _generateDailyMetrics() {
    return [
      BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: 75, color: Colors.green)]),
      BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: 82, color: Colors.green)]),
      BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: 95, color: Colors.green)]),
      BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: 89, color: Colors.green)]),
      BarChartGroupData(x: 4, barRods: [BarChartRodData(toY: 91, color: Colors.green)]),
      BarChartGroupData(x: 5, barRods: [BarChartRodData(toY: 68, color: Colors.green)]),
      BarChartGroupData(x: 6, barRods: [BarChartRodData(toY: 72, color: Colors.green)]),
    ];
  }

  List<PieChartSectionData> _generateTeamDistribution() {
    return [
      PieChartSectionData(
        color: Colors.blue,
        value: 35,
        title: '35%',
        radius: 100,
      ),
      PieChartSectionData(
        color: Colors.green,
        value: 28,
        title: '28%',
        radius: 100,
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: 22,
        title: '22%',
        radius: 100,
      ),
      PieChartSectionData(
        color: Colors.red,
        value: 15,
        title: '15%',
        radius: 100,
      ),
    ];
  }
}
