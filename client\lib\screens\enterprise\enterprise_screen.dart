import 'package:flutter/material.dart';
import 'analytics_dashboard_screen.dart';
import 'organization_management_screen.dart';

/// Main enterprise features screen
class EnterpriseScreen extends StatefulWidget {
  const EnterpriseScreen({super.key});

  @override
  State<EnterpriseScreen> createState() => _EnterpriseScreenState();
}

class _EnterpriseScreenState extends State<EnterpriseScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enterprise Features'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enterprise Dashboard',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Advanced features for enterprise organizations',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 32),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildFeatureCard(
                    'Organization Management',
                    'Manage users, roles, and permissions',
                    Icons.business,
                    Colors.blue,
                    () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const OrganizationManagementScreen(
                          organizationId: 'demo-org',
                        ),
                      ),
                    ),
                  ),
                  _buildFeatureCard(
                    'Analytics Dashboard',
                    'Advanced analytics and reporting',
                    Icons.analytics,
                    Colors.green,
                    () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AnalyticsDashboardScreen(
                          organizationId: 'demo-org',
                        ),
                      ),
                    ),
                  ),
                  _buildFeatureCard(
                    'User Management',
                    'Bulk user operations and SSO',
                    Icons.people,
                    Colors.orange,
                    () => _showComingSoon('User Management'),
                  ),
                  _buildFeatureCard(
                    'Compliance & Security',
                    'Security settings and compliance',
                    Icons.security,
                    Colors.red,
                    () => _showComingSoon('Compliance & Security'),
                  ),
                  _buildFeatureCard(
                    'API Management',
                    'API keys and rate limiting',
                    Icons.api,
                    Colors.purple,
                    () => _showComingSoon('API Management'),
                  ),
                  _buildFeatureCard(
                    'Custom Integrations',
                    'Third-party integrations',
                    Icons.extension,
                    Colors.teal,
                    () => _showComingSoon('Custom Integrations'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon in future updates'),
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {},
        ),
      ),
    );
  }
}
