import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/api_service.dart';

/// SSO Configuration Screen for Enterprise Organizations
/// Allows administrators to configure Single Sign-On with SAML 2.0 and OAuth 2.0
class SSOConfigurationScreen extends StatefulWidget {
  const SSOConfigurationScreen({super.key});

  @override
  State<SSOConfigurationScreen> createState() => _SSOConfigurationScreenState();
}

class _SSOConfigurationScreenState extends State<SSOConfigurationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _samlFormKey = GlobalKey<FormState>();
  final _oauthFormKey = GlobalKey<FormState>();
  
  // SAML Configuration Controllers
  final _samlEntityIdController = TextEditingController();
  final _samlSsoUrlController = TextEditingController();
  final _samlX509CertController = TextEditingController();
  final _samlAttributeMappingController = TextEditingController();
  
  // OAuth Configuration Controllers
  final _oauthClientIdController = TextEditingController();
  final _oauthClientSecretController = TextEditingController();
  final _oauthAuthUrlController = TextEditingController();
  final _oauthTokenUrlController = TextEditingController();
  final _oauthUserInfoUrlController = TextEditingController();
  final _oauthScopesController = TextEditingController();
  
  bool _samlEnabled = false;
  bool _oauthEnabled = false;
  bool _autoProvisionUsers = true;
  bool _requireSSOForDomain = false;
  String _defaultRole = 'member';
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCurrentConfiguration();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _samlEntityIdController.dispose();
    _samlSsoUrlController.dispose();
    _samlX509CertController.dispose();
    _samlAttributeMappingController.dispose();
    _oauthClientIdController.dispose();
    _oauthClientSecretController.dispose();
    _oauthAuthUrlController.dispose();
    _oauthTokenUrlController.dispose();
    _oauthUserInfoUrlController.dispose();
    _oauthScopesController.dispose();
    super.dispose();
  }

  void _loadCurrentConfiguration() async {
    try {
      // Load current SSO configuration from API
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Mock API response - in real app, this would be an HTTP call
      final mockConfig = {
        'saml': {
          'entityId': 'https://app.quester.com/saml/metadata',
          'ssoUrl': 'https://idp.company.com/sso/saml',
          'certificate': '',
        },
        'oauth': {
          'clientId': 'your_oauth_client_id',
          'authUrl': 'https://oauth.provider.com/oauth2/auth',
          'tokenUrl': 'https://oauth.provider.com/oauth2/token',
          'scopes': 'openid profile email',
        },
      };

      // Set values from API response
      final samlConfig = mockConfig['saml'] as Map<String, dynamic>;
      final oauthConfig = mockConfig['oauth'] as Map<String, dynamic>;

      _samlEntityIdController.text = samlConfig['entityId'] as String;
      _samlSsoUrlController.text = samlConfig['ssoUrl'] as String;
      _samlX509CertController.text = samlConfig['certificate'] as String;
      _oauthClientIdController.text = oauthConfig['clientId'] as String;
      _oauthAuthUrlController.text = oauthConfig['authUrl'] as String;
      _oauthTokenUrlController.text = oauthConfig['tokenUrl'] as String;
      _oauthScopesController.text = oauthConfig['scopes'] as String;
    } catch (e) {
      // Handle error loading configuration
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load SSO configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    _oauthTokenUrlController.text = 'https://oauth.provider.com/oauth2/token';
    _oauthUserInfoUrlController.text = 'https://oauth.provider.com/userinfo';
    _oauthScopesController.text = 'openid profile email';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SSO Configuration'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveConfiguration,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white),
            ),
          ),
          const SizedBox(width: 16),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.security), text: 'SAML 2.0'),
            Tab(icon: Icon(Icons.key), text: 'OAuth 2.0'),
            Tab(icon: Icon(Icons.settings), text: 'Advanced'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSAMLTab(),
          _buildOAuthTab(),
          _buildAdvancedTab(),
        ],
      ),
    );
  }

  Widget _buildSAMLTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _samlFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enable SAML Switch
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Enable SAML 2.0',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Allow users to sign in with SAML 2.0 identity providers',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _samlEnabled,
                      onChanged: (value) {
                        setState(() {
                          _samlEnabled = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // SAML Configuration
            Text(
              'SAML Identity Provider Configuration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // Entity ID
            TextFormField(
              controller: _samlEntityIdController,
              decoration: const InputDecoration(
                labelText: 'Entity ID *',
                hintText: 'https://app.quester.com/saml/metadata',
                helperText: 'Unique identifier for your SAML service provider',
              ),
              enabled: _samlEnabled,
              validator: _samlEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Entity ID is required';
                }
                if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                  return 'Please enter a valid URL';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // SSO URL
            TextFormField(
              controller: _samlSsoUrlController,
              decoration: const InputDecoration(
                labelText: 'SSO URL *',
                hintText: 'https://idp.company.com/sso/saml',
                helperText: 'SAML single sign-on endpoint URL',
              ),
              enabled: _samlEnabled,
              validator: _samlEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'SSO URL is required';
                }
                if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                  return 'Please enter a valid URL';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // X.509 Certificate
            TextFormField(
              controller: _samlX509CertController,
              decoration: const InputDecoration(
                labelText: 'X.509 Certificate *',
                hintText: 'Paste the X.509 certificate here...',
                helperText: 'Public certificate from your identity provider',
              ),
              maxLines: 6,
              enabled: _samlEnabled,
              validator: _samlEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'X.509 Certificate is required';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // Attribute Mapping
            TextFormField(
              controller: _samlAttributeMappingController,
              decoration: const InputDecoration(
                labelText: 'Attribute Mapping (JSON)',
                hintText: '{"email": "EmailAddress", "firstName": "FirstName", "lastName": "LastName"}',
                helperText: 'Map SAML attributes to user fields',
              ),
              maxLines: 4,
              enabled: _samlEnabled,
            ),
            const SizedBox(height: 24),
            
            // Test Connection Button
            if (_samlEnabled) ...[
              ElevatedButton.icon(
                onPressed: _testSAMLConnection,
                icon: const Icon(Icons.science),
                label: const Text('Test SAML Connection'),
              ),
              const SizedBox(height: 16),
              
              // Metadata Download
              OutlinedButton.icon(
                onPressed: _downloadSAMLMetadata,
                icon: const Icon(Icons.download),
                label: const Text('Download SP Metadata'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOAuthTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _oauthFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enable OAuth Switch
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Enable OAuth 2.0',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Allow users to sign in with OAuth 2.0 providers',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _oauthEnabled,
                      onChanged: (value) {
                        setState(() {
                          _oauthEnabled = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // OAuth Configuration
            Text(
              'OAuth 2.0 Provider Configuration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // Client ID
            TextFormField(
              controller: _oauthClientIdController,
              decoration: const InputDecoration(
                labelText: 'Client ID *',
                hintText: 'your-oauth-client-id',
                helperText: 'OAuth application client ID',
              ),
              enabled: _oauthEnabled,
              validator: _oauthEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Client ID is required';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // Client Secret
            TextFormField(
              controller: _oauthClientSecretController,
              decoration: const InputDecoration(
                labelText: 'Client Secret *',
                hintText: 'your-oauth-client-secret',
                helperText: 'OAuth application client secret',
              ),
              obscureText: true,
              enabled: _oauthEnabled,
              validator: _oauthEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Client Secret is required';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // Authorization URL
            TextFormField(
              controller: _oauthAuthUrlController,
              decoration: const InputDecoration(
                labelText: 'Authorization URL *',
                hintText: 'https://oauth.provider.com/oauth2/auth',
                helperText: 'OAuth authorization endpoint',
              ),
              enabled: _oauthEnabled,
              validator: _oauthEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Authorization URL is required';
                }
                if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                  return 'Please enter a valid URL';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // Token URL
            TextFormField(
              controller: _oauthTokenUrlController,
              decoration: const InputDecoration(
                labelText: 'Token URL *',
                hintText: 'https://oauth.provider.com/oauth2/token',
                helperText: 'OAuth token endpoint',
              ),
              enabled: _oauthEnabled,
              validator: _oauthEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Token URL is required';
                }
                if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                  return 'Please enter a valid URL';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // User Info URL
            TextFormField(
              controller: _oauthUserInfoUrlController,
              decoration: const InputDecoration(
                labelText: 'User Info URL *',
                hintText: 'https://oauth.provider.com/userinfo',
                helperText: 'OAuth user information endpoint',
              ),
              enabled: _oauthEnabled,
              validator: _oauthEnabled ? (value) {
                if (value == null || value.isEmpty) {
                  return 'User Info URL is required';
                }
                if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                  return 'Please enter a valid URL';
                }
                return null;
              } : null,
            ),
            const SizedBox(height: 16),
            
            // Scopes
            TextFormField(
              controller: _oauthScopesController,
              decoration: const InputDecoration(
                labelText: 'Scopes',
                hintText: 'openid profile email',
                helperText: 'Space-separated list of OAuth scopes',
              ),
              enabled: _oauthEnabled,
            ),
            const SizedBox(height: 24),
            
            // Test Connection Button
            if (_oauthEnabled) ...[
              ElevatedButton.icon(
                onPressed: _testOAuthConnection,
                icon: const Icon(Icons.science),
                label: const Text('Test OAuth Connection'),
              ),
              const SizedBox(height: 16),
              
              // Redirect URI Info
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Redirect URI Configuration',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add this redirect URI to your OAuth provider:',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                'https://app.quester.com/auth/oauth/callback',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontFamily: 'monospace',
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () => _copyToClipboard('https://app.quester.com/auth/oauth/callback'),
                              tooltip: 'Copy to clipboard',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Advanced SSO Settings',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          
          // Auto Provision Users
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Auto-provision Users',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Automatically create user accounts when they sign in via SSO',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _autoProvisionUsers,
                    onChanged: (value) {
                      setState(() {
                        _autoProvisionUsers = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Require SSO for Domain
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Require SSO for Domain',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Force users with company email domains to use SSO',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _requireSSOForDomain,
                    onChanged: (value) {
                      setState(() {
                        _requireSSOForDomain = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Default Role
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Default Role for New Users',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _defaultRole,
                    decoration: const InputDecoration(
                      labelText: 'Role',
                      helperText: 'Role assigned to new users created via SSO',
                    ),
                    items: const [
                      DropdownMenuItem(value: 'viewer', child: Text('Viewer')),
                      DropdownMenuItem(value: 'member', child: Text('Member')),
                      DropdownMenuItem(value: 'admin', child: Text('Admin')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _defaultRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Domain Configuration
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Domain Configuration',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Configure which email domains should use SSO authentication',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Allowed Domains',
                      hintText: 'company.com, subsidiary.com',
                      helperText: 'Comma-separated list of email domains',
                    ),
                    initialValue: 'company.com',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Security Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Security Settings',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  
                  // Session Timeout
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Session Timeout (minutes)',
                      helperText: 'Automatically log out users after inactivity',
                    ),
                    initialValue: '480',
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  
                  // Force Re-authentication
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Force Re-authentication (hours)',
                      helperText: 'Require users to re-authenticate periodically',
                    ),
                    initialValue: '24',
                    keyboardType: TextInputType.number,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveConfiguration() async {
    bool isValid = true;
    
    if (_samlEnabled && !_samlFormKey.currentState!.validate()) {
      isValid = false;
      _tabController.animateTo(0);
    }
    
    if (_oauthEnabled && !_oauthFormKey.currentState!.validate()) {
      isValid = false;
      _tabController.animateTo(1);
    }
    
    if (!isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors before saving'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    // Implement save configuration API call
    try {
      final configData = {
        'saml': {
          'enabled': _samlEnabled,
          'entityId': _samlEntityIdController.text,
          'ssoUrl': _samlSsoUrlController.text,
          'x509Certificate': _samlX509CertController.text,
          'attributeMapping': _samlAttributeMappingController.text,
        },
        'oauth': {
          'enabled': _oauthEnabled,
          'clientId': _oauthClientIdController.text,
          'clientSecret': _oauthClientSecretController.text,
          'authUrl': _oauthAuthUrlController.text,
          'tokenUrl': _oauthTokenUrlController.text,
          'userInfoUrl': _oauthUserInfoUrlController.text,
          'scopes': _oauthScopesController.text,
        },
        'settings': {
          'autoProvisionUsers': _autoProvisionUsers,
          'requireSSOForDomain': _requireSSOForDomain,
          'defaultRole': _defaultRole,
        },
      };

      // Send configData to backend API
      final apiService = context.read<ApiService>();
      final response = await apiService.saveSSOConfiguration(configData);

      if (response.success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('SSO configuration saved successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(response.error ?? 'Failed to save SSO configuration');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _testSAMLConnection() async {
    if (!_samlFormKey.currentState!.validate()) {
      return;
    }

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('SAML Connection Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('Testing SAML connection...'),
            const SizedBox(height: 8),
            Text(
              'Validating configuration with identity provider:\n${_samlSsoUrlController.text}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );

    try {
      // Test SAML connection using API service
      final apiService = context.read<ApiService>();
      final testConfig = {
        'type': 'saml',
        'entityId': _samlEntityIdController.text,
        'ssoUrl': _samlSsoUrlController.text,
        'x509Certificate': _samlX509CertController.text,
        'attributeMapping': _samlAttributeMappingController.text,
      };

      final response = await apiService.testSSOConfiguration(testConfig);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show result dialog based on response
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('SAML Connection Test'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  response.success ? Icons.check_circle : Icons.error,
                  color: response.success ? Colors.green : Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(response.success
                  ? 'SAML connection test successful!'
                  : 'SAML connection test failed'),
                const SizedBox(height: 8),
                Text(
                  response.success
                    ? 'Your SAML configuration is valid and can communicate with the identity provider.'
                    : response.error ?? 'Unable to connect to the identity provider. Please check your configuration.',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('SAML Connection Test'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                const Text('Connection test failed'),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    }
  }

  void _testOAuthConnection() {
    if (!_oauthFormKey.currentState!.validate()) {
      return;
    }
    
    // Implement OAuth connection test (similar to SAML)
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('OAuth Connection Test'),
        content: const Text('Testing OAuth connection...\\n\\nThis would normally validate the configuration with your OAuth provider.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _downloadSAMLMetadata() {
    // Implement SAML metadata download
    // In a real app, this would generate and download the metadata file
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('SAML metadata download started (mock)'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _copyToClipboard(String text) {
    // Implement clipboard copy (mock implementation)
    // In a real app, use Clipboard.setData(ClipboardData(text: text))
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
