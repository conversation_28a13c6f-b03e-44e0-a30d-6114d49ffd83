import 'package:flutter/material.dart';

/// Activity Timeline Widget for displaying recent organization activities
/// Shows a chronological list of important events and actions
class ActivityTimelineWidget extends StatelessWidget {
  final List<dynamic> activities;
  final String organizationId;

  const ActivityTimelineWidget({
    super.key,
    required this.activities,
    required this.organizationId,
  });

  @override
  Widget build(BuildContext context) {
    if (activities.isEmpty) {
      return _buildEmptyState(context);
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton(
                  onPressed: () => _viewAllActivities(context),
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length.clamp(0, 10), // Show max 10 activities
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return _buildActivityItem(context, activity);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, Map<String, dynamic> activity) {
    final theme = Theme.of(context);
    final activityType = activity['type'] as String? ?? 'unknown';
    final activityData = _getActivityData(activityType);
    final timestamp = DateTime.tryParse(activity['timestamp'] as String? ?? '') ?? DateTime.now();
    final userInfo = activity['user'] as Map<String, dynamic>? ?? {};
    final details = activity['details'] as Map<String, dynamic>? ?? {};

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: CircleAvatar(
        backgroundColor: activityData['color'].withValues(alpha: 0.1),
        child: Icon(
          activityData['icon'],
          color: activityData['color'],
          size: 20,
        ),
      ),
      title: Text(
        _buildActivityTitle(activity),
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (userInfo.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'by ${userInfo['display_name'] ?? userInfo['email'] ?? 'Unknown User'}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
          if (details.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              _buildActivityDescription(details),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            _formatTimestamp(timestamp),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          if (activity['priority'] == 'high') ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'HIGH',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.timeline,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Recent Activity',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Activity will appear here as your team uses the platform',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getActivityData(String activityType) {
    switch (activityType) {
      case 'user_joined':
        return {'icon': Icons.person_add, 'color': Colors.green};
      case 'user_left':
        return {'icon': Icons.person_remove, 'color': Colors.red};
      case 'quest_created':
        return {'icon': Icons.add_task, 'color': Colors.blue};
      case 'quest_completed':
        return {'icon': Icons.task_alt, 'color': Colors.green};
      case 'achievement_unlocked':
        return {'icon': Icons.emoji_events, 'color': Colors.amber};
      case 'role_changed':
        return {'icon': Icons.admin_panel_settings, 'color': Colors.purple};
      case 'settings_updated':
        return {'icon': Icons.settings, 'color': Colors.grey};
      case 'integration_added':
        return {'icon': Icons.integration_instructions, 'color': Colors.teal};
      case 'security_event':
        return {'icon': Icons.security, 'color': Colors.red};
      case 'data_export':
        return {'icon': Icons.download, 'color': Colors.orange};
      default:
        return {'icon': Icons.circle, 'color': Colors.grey};
    }
  }

  String _buildActivityTitle(Map<String, dynamic> activity) {
    final activityType = activity['type'] as String? ?? 'unknown';
    final details = activity['details'] as Map<String, dynamic>? ?? {};
    
    switch (activityType) {
      case 'user_joined':
        return 'New user joined the organization';
      case 'user_left':
        return 'User left the organization';
      case 'quest_created':
        return 'New quest created: ${details['quest_name'] ?? 'Untitled Quest'}';
      case 'quest_completed':
        return 'Quest completed: ${details['quest_name'] ?? 'Unknown Quest'}';
      case 'achievement_unlocked':
        return 'Achievement unlocked: ${details['achievement_name'] ?? 'Unknown Achievement'}';
      case 'role_changed':
        return 'User role updated';
      case 'settings_updated':
        return 'Organization settings updated';
      case 'integration_added':
        return 'New integration added: ${details['integration_name'] ?? 'Unknown Integration'}';
      case 'security_event':
        return 'Security event detected';
      case 'data_export':
        return 'Data export requested';
      default:
        return activity['title'] as String? ?? 'Unknown activity';
    }
  }

  String _buildActivityDescription(Map<String, dynamic> details) {
    final description = details['description'] as String?;
    if (description != null && description.isNotEmpty) {
      return description;
    }

    // Build description from available details
    final parts = <String>[];
    details.forEach((key, value) {
      if (key != 'description' && value != null) {
        parts.add('$key: $value');
      }
    });

    return parts.isNotEmpty ? parts.join(' • ') : '';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _viewAllActivities(BuildContext context) {
    try {
      Navigator.of(context).pushNamed('/activity');
    } catch (e) {
      // Fallback if route doesn't exist
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Activity log page is not available yet'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
