import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// Analytics Chart Widget for displaying various chart types
/// Supports line charts, bar charts, pie charts, and more
class AnalyticsChartWidget extends StatelessWidget {
  final ChartType chartType;
  final List<dynamic> data;
  final String timeRange;
  final bool isCompact;

  const AnalyticsChartWidget({
    super.key,
    required this.chartType,
    required this.data,
    required this.timeRange,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    switch (chartType) {
      case ChartType.line:
        return _buildLineChart(context);
      case ChartType.bar:
        return _buildBarChart(context);
      case ChartType.pie:
        return _buildPie<PERSON>hart(context);
      case ChartType.radar:
        return _buildRadarChart(context);
      case ChartType.heatmap:
        return _buildHeatmapChart(context);
      case ChartType.combo:
        return _buildComboChart(context);
    }
  }

  Widget _buildLineChart(BuildContext context) {
    final theme = Theme.of(context);
    
    if (data.isEmpty) {
      return _buildEmptyChart(context);
    }

    final spots = data.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final value = (entry.value is Map) 
          ? (entry.value['value'] ?? 0).toDouble()
          : entry.value.toDouble();
      return FlSpot(index, value);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: !isCompact,
          drawVerticalLine: !isCompact,
          horizontalInterval: isCompact ? null : 1,
          verticalInterval: isCompact ? null : 1,
        ),
        titlesData: FlTitlesData(
          show: !isCompact,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: !isCompact,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: !isCompact,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  final item = data[index];
                  final label = item is Map ? item['label'] ?? '' : '';
                  return Text(
                    _formatLabel(label),
                    style: theme.textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: !isCompact,
          border: Border.all(color: theme.dividerColor),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: theme.primaryColor,
            barWidth: isCompact ? 2 : 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: !isCompact),
            belowBarData: BarAreaData(
              show: true,
              color: theme.primaryColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(BuildContext context) {
    final theme = Theme.of(context);
    
    if (data.isEmpty) {
      return _buildEmptyChart(context);
    }

    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final value = (entry.value is Map) 
          ? (entry.value['value'] ?? 0).toDouble()
          : entry.value.toDouble();
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: value,
            color: theme.primaryColor,
            width: isCompact ? 8 : 16,
            borderRadius: BorderRadius.circular(isCompact ? 2 : 4),
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxValue() * 1.2,
        gridData: FlGridData(
          show: !isCompact,
          drawVerticalLine: false,
        ),
        titlesData: FlTitlesData(
          show: !isCompact,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: !isCompact,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: !isCompact,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  final item = data[index];
                  final label = item is Map ? item['label'] ?? '' : '';
                  return Text(
                    _formatLabel(label),
                    style: theme.textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: !isCompact,
          border: Border.all(color: theme.dividerColor),
        ),
        barGroups: barGroups,
      ),
    );
  }

  Widget _buildPieChart(BuildContext context) {
    final theme = Theme.of(context);
    
    if (data.isEmpty) {
      return _buildEmptyChart(context);
    }

    final sections = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final value = (item is Map ? item['value'] ?? 0 : item).toDouble();
      // final label = item is Map ? item['label'] ?? '' : ''; // Will be used for tooltips in future
      
      final colors = [
        theme.primaryColor,
        theme.colorScheme.secondary,
        theme.colorScheme.tertiary,
        Colors.orange,
        Colors.green,
        Colors.purple,
      ];

      return PieChartSectionData(
        color: colors[index % colors.length],
        value: value,
        title: isCompact ? '' : '$value%',
        radius: isCompact ? 40 : 80,
        titleStyle: theme.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      );
    }).toList();

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: isCompact ? 20 : 40,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildRadarChart(BuildContext context) {
    // Simplified radar chart using container - fl_chart doesn't have built-in radar
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('Radar Chart\n(Coming Soon)'),
      ),
    );
  }

  Widget _buildHeatmapChart(BuildContext context) {
    // Simplified heatmap representation
    final theme = Theme.of(context);
    
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        final value = (item is Map ? item['value'] ?? 0 : item).toDouble();
        final intensity = (value / _getMaxValue()).clamp(0.1, 1.0);
        
        return Container(
          decoration: BoxDecoration(
            color: theme.primaryColor.withValues(alpha: intensity),
            borderRadius: BorderRadius.circular(2),
          ),
          child: isCompact ? null : Center(
            child: Text(
              value.toInt().toString(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: intensity > 0.5 ? Colors.white : Colors.black,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildComboChart(BuildContext context) {
    final theme = Theme.of(context);
    
    // Combination of line and bar chart
    if (data.isEmpty) {
      return _buildEmptyChart(context);
    }

    final lineSpots = data.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final item = entry.value;
      final value = (item is Map ? item['secondary'] ?? 0 : 0).toDouble();
      return FlSpot(index, value);
    }).toList();

    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final value = (item is Map ? item['primary'] ?? 0 : entry.value).toDouble();
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: value,
            color: theme.colorScheme.secondary.withValues(alpha: 0.7),
            width: isCompact ? 8 : 16,
            borderRadius: BorderRadius.circular(isCompact ? 2 : 4),
          ),
        ],
      );
    }).toList();

    return Stack(
      children: [
        BarChart(
          BarChartData(
            alignment: BarChartAlignment.spaceAround,
            maxY: _getMaxValue() * 1.2,
            gridData: FlGridData(show: !isCompact),
            titlesData: FlTitlesData(show: !isCompact),
            borderData: FlBorderData(show: !isCompact),
            barGroups: barGroups,
          ),
        ),
        LineChart(
          LineChartData(
            gridData: const FlGridData(show: false),
            titlesData: const FlTitlesData(show: false),
            borderData: FlBorderData(show: false),
            lineBarsData: [
              LineChartBarData(
                spots: lineSpots,
                isCurved: true,
                color: theme.primaryColor,
                barWidth: 3,
                dotData: const FlDotData(show: false),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyChart(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('No data available'),
      ),
    );
  }

  double _getMaxValue() {
    if (data.isEmpty) return 100;
    
    return data.map((item) {
      if (item is Map) {
        final primary = (item['value'] ?? item['primary'] ?? 0).toDouble();
        final secondary = (item['secondary'] ?? 0).toDouble();
        return primary > secondary ? primary : secondary;
      }
      return item.toDouble();
    }).reduce((a, b) => a > b ? a : b);
  }

  String _formatLabel(String label) {
    if (isCompact && label.length > 3) {
      return '${label.substring(0, 3)}...';
    }
    return label;
  }
}

/// Chart types supported by the analytics widget
enum ChartType { line, bar, pie, radar, heatmap, combo }
