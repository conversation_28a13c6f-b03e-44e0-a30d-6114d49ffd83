import 'package:flutter/material.dart';

/// KPI Metrics Widget for displaying key performance indicators
/// Shows important business metrics in a grid layout with trend indicators
class KPIMetricsWidget extends StatelessWidget {
  final Map<String, dynamic> metrics;
  final String timeRange;

  const KPIMetricsWidget({
    super.key,
    required this.metrics,
    required this.timeRange,
  });

  @override
  Widget build(BuildContext context) {
    final kpiData = _prepareKPIData();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: kpiData.length,
      itemBuilder: (context, index) {
        final kpi = kpiData[index];
        return _buildKPICard(context, kpi);
      },
    );
  }

  List<Map<String, dynamic>> _prepareKPIData() {
    return [
      {
        'title': 'Total Users',
        'value': metrics['totalUsers'] ?? 0,
        'change': metrics['totalUsersChange'] ?? 0.0,
        'icon': Icons.people,
        'color': Colors.blue,
        'prefix': '',
        'suffix': '',
      },
      {
        'title': 'Active Sessions',
        'value': metrics['activeSessions'] ?? 0,
        'change': metrics['activeSessionsChange'] ?? 0.0,
        'icon': Icons.timeline,
        'color': Colors.green,
        'prefix': '',
        'suffix': '',
      },
      {
        'title': 'Quest Completion',
        'value': metrics['questCompletionRate'] ?? 0.0,
        'change': metrics['questCompletionChange'] ?? 0.0,
        'icon': Icons.task_alt,
        'color': Colors.orange,
        'prefix': '',
        'suffix': '%',
      },
      {
        'title': 'Avg. Session Time',
        'value': metrics['avgSessionTime'] ?? 0,
        'change': metrics['avgSessionTimeChange'] ?? 0.0,
        'icon': Icons.timer,
        'color': Colors.purple,
        'prefix': '',
        'suffix': 'min',
      },
      {
        'title': 'Points Earned',
        'value': metrics['totalPoints'] ?? 0,
        'change': metrics['totalPointsChange'] ?? 0.0,
        'icon': Icons.emoji_events,
        'color': Colors.amber,
        'prefix': '',
        'suffix': '',
      },
      {
        'title': 'Achievements',
        'value': metrics['totalAchievements'] ?? 0,
        'change': metrics['totalAchievementsChange'] ?? 0.0,
        'icon': Icons.military_tech,
        'color': Colors.red,
        'prefix': '',
        'suffix': '',
      },
    ];
  }

  Widget _buildKPICard(BuildContext context, Map<String, dynamic> kpi) {
    final theme = Theme.of(context);
    final value = kpi['value'];
    final change = (kpi['change'] as num).toDouble();
    final isPositive = change >= 0;
    final color = kpi['color'] as Color;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and trend
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  kpi['icon'] as IconData,
                  color: color,
                  size: 28,
                ),
                _buildTrendIndicator(change, isPositive),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Value
            Text(
              '${kpi['prefix']}${_formatValue(value)}${kpi['suffix']}',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            
            const SizedBox(height: 4),
            
            // Title
            Text(
              kpi['title'] as String,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            
            const Spacer(),
            
            // Change indicator
            if (change != 0)
              Row(
                children: [
                  Icon(
                    isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                    color: isPositive ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${change.abs().toStringAsFixed(1)}% vs ${_getTimeRangeLabel()}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isPositive ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendIndicator(double change, bool isPositive) {
    if (change == 0) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'Stable',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: (isPositive ? Colors.green : Colors.red).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isPositive ? 'Rising' : 'Falling',
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: isPositive ? Colors.green : Colors.red,
        ),
      ),
    );
  }

  String _formatValue(dynamic value) {
    if (value is double) {
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      } else {
        return value.toStringAsFixed(value % 1 == 0 ? 0 : 1);
      }
    } else if (value is int) {
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      } else {
        return value.toString();
      }
    }
    return value.toString();
  }

  String _getTimeRangeLabel() {
    switch (timeRange) {
      case '1d':
        return 'yesterday';
      case '7d':
        return 'last week';
      case '30d':
        return 'last month';
      case '90d':
        return 'last quarter';
      case '365d':
        return 'last year';
      default:
        return 'previous period';
    }
  }
}
