import 'package:flutter/material.dart';

/// Password strength levels
enum PasswordStrength {
  weak,
  fair,
  good,
  strong,
  veryStrong,
}

/// Password strength indicator widget
class PasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final Map<String, dynamic>? passwordPolicy;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
    this.passwordPolicy,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final strength = _calculateStrength(password);
    final requirements = _getRequirements(password);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Strength bar
        Row(
          children: [
            Text(
              'Password Strength: ',
              style: theme.textTheme.bodySmall,
            ),
            Text(
              _getStrengthText(strength),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStrengthColor(strength),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Strength indicator bars
        Row(
          children: List.generate(5, (index) {
            final isActive = index < strength.index + 1;
            return Expanded(
              child: Container(
                height: 4,
                margin: EdgeInsets.only(
                  right: index < 4 ? 4 : 0,
                ),
                decoration: BoxDecoration(
                  color: isActive 
                    ? _getStrengthColor(strength)
                    : theme.colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            );
          }),
        ),
        
        if (password.isNotEmpty) ...[
          const SizedBox(height: 12),
          
          // Requirements checklist
          ...requirements.entries.map((entry) {
            final isMet = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    isMet ? Icons.check_circle : Icons.circle_outlined,
                    size: 16,
                    color: isMet 
                      ? Colors.green 
                      : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    entry.key,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isMet 
                        ? Colors.green 
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ],
    );
  }

  PasswordStrength _calculateStrength(String password) {
    if (password.isEmpty) return PasswordStrength.weak;
    
    int score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character type checks
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;
    
    // Complexity checks
    if (password.length >= 16) score++;
    if (RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])').hasMatch(password)) score++;
    
    // Convert score to strength
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.fair;
    if (score <= 6) return PasswordStrength.good;
    if (score <= 7) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  Map<String, bool> _getRequirements(String password) {
    final minLength = passwordPolicy?['minLength'] ?? 8;
    
    return {
      'At least $minLength characters': password.length >= minLength,
      'At least one lowercase letter': password.contains(RegExp(r'[a-z]')),
      'At least one uppercase letter': password.contains(RegExp(r'[A-Z]')),
      'At least one number': password.contains(RegExp(r'[0-9]')),
      'At least one special character': password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
    };
  }

  String _getStrengthText(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.fair:
        return 'Fair';
      case PasswordStrength.good:
        return 'Good';
      case PasswordStrength.strong:
        return 'Strong';
      case PasswordStrength.veryStrong:
        return 'Very Strong';
    }
  }

  Color _getStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.fair:
        return Colors.orange;
      case PasswordStrength.good:
        return Colors.yellow.shade700;
      case PasswordStrength.strong:
        return Colors.lightGreen;
      case PasswordStrength.veryStrong:
        return Colors.green;
    }
  }
}