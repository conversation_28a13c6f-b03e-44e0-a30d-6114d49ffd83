import 'package:flutter/material.dart';

/// Social login buttons widget
class SocialLoginButtons extends StatelessWidget {
  final VoidCallback? onGoogleLogin;
  final VoidCallback? onMicrosoftLogin;
  final VoidCallback? onGitHubLogin;
  final VoidCallback? onAppleLogin;
  final bool isLoading;

  const SocialLoginButtons({
    super.key,
    this.onGoogleLogin,
    this.onMicrosoftLogin,
    this.onGitHubLogin,
    this.onAppleLogin,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Social login divider
        Row(
          children: [
            Expanded(
              child: Divider(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Or continue with',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Social login buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (onGoogleLogin != null)
              _SocialLoginButton(
                onPressed: isLoading ? null : onGoogleLogin,
                icon: Icons.g_mobiledata, // Placeholder icon
                label: 'Google',
                backgroundColor: Colors.white,
                borderColor: Colors.grey.shade300,
                textColor: Colors.black87,
              ),
              
            if (onMicrosoftLogin != null)
              _SocialLoginButton(
                onPressed: isLoading ? null : onMicrosoftLogin,
                icon: Icons.business, // Placeholder icon
                label: 'Microsoft',
                backgroundColor: const Color(0xFF0078D4),
                textColor: Colors.white,
              ),
              
            if (onGitHubLogin != null)
              _SocialLoginButton(
                onPressed: isLoading ? null : onGitHubLogin,
                icon: Icons.code, // Placeholder icon
                label: 'GitHub',
                backgroundColor: const Color(0xFF181717),
                textColor: Colors.white,
              ),
              
            if (onAppleLogin != null)
              _SocialLoginButton(
                onPressed: isLoading ? null : onAppleLogin,
                icon: Icons.apple, // Placeholder icon
                label: 'Apple',
                backgroundColor: Colors.black,
                textColor: Colors.white,
              ),
          ],
        ),
      ],
    );
  }
}

class _SocialLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String label;
  final Color backgroundColor;
  final Color? borderColor;
  final Color textColor;

  const _SocialLoginButton({
    required this.onPressed,
    required this.icon,
    required this.label,
    required this.backgroundColor,
    this.borderColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: OutlinedButton.icon(
          onPressed: onPressed,
          icon: Icon(
            icon,
            size: 20,
            color: textColor,
          ),
          label: Text(
            label,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          style: OutlinedButton.styleFrom(
            backgroundColor: backgroundColor,
            side: BorderSide(
              color: borderColor ?? backgroundColor,
              width: 1,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}

/// Individual social login button
class SocialLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String provider;
  final String? iconAsset;
  final IconData? icon;
  final String? customLabel;
  final bool isLoading;

  const SocialLoginButton({
    super.key,
    required this.onPressed,
    required this.provider,
    this.iconAsset,
    this.icon,
    this.customLabel,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getProviderConfig(provider);
    
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading 
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  config['textColor'] as Color,
                ),
              ),
            )
          : iconAsset != null
            ? Image.asset(
                iconAsset!,
                width: 20,
                height: 20,
              )
            : Icon(
                icon ?? config['icon'] as IconData,
                size: 20,
                color: config['textColor'] as Color,
              ),
        label: Text(
          customLabel ?? 'Continue with ${config['name']}',
          style: TextStyle(
            color: config['textColor'] as Color,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          backgroundColor: config['backgroundColor'] as Color,
          side: BorderSide(
            color: config['borderColor'] as Color,
            width: 1,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getProviderConfig(String provider) {
    switch (provider.toLowerCase()) {
      case 'google':
        return {
          'name': 'Google',
          'icon': Icons.g_mobiledata,
          'backgroundColor': Colors.white,
          'borderColor': Colors.grey.shade300,
          'textColor': Colors.black87,
        };
      case 'microsoft':
        return {
          'name': 'Microsoft',
          'icon': Icons.business,
          'backgroundColor': const Color(0xFF0078D4),
          'borderColor': const Color(0xFF0078D4),
          'textColor': Colors.white,
        };
      case 'github':
        return {
          'name': 'GitHub',
          'icon': Icons.code,
          'backgroundColor': const Color(0xFF181717),
          'borderColor': const Color(0xFF181717),
          'textColor': Colors.white,
        };
      case 'apple':
        return {
          'name': 'Apple',
          'icon': Icons.apple,
          'backgroundColor': Colors.black,
          'borderColor': Colors.black,
          'textColor': Colors.white,
        };
      default:
        return {
          'name': provider,
          'icon': Icons.login,
          'backgroundColor': Colors.white,
          'borderColor': Colors.grey.shade300,
          'textColor': Colors.black87,
        };
    }
  }
}