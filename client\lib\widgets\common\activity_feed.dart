import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import '../../features/gamification/bloc/gamification_bloc.dart';
import '../../features/gamification/bloc/gamification_state.dart';

/// Activity feed widget that shows recent gamification and enterprise events
class ActivityFeed extends StatelessWidget {
  final int maxItems;
  final bool showHeader;
  final EdgeInsets? padding;

  const ActivityFeed({
    super.key,
    this.maxItems = 10,
    this.showHeader = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<GamificationBloc, GamificationState>(
      builder: (context, state) {
        final activities = <ActivityItem>[];

        // Add gamification activities if available
        if (state is GamificationLoaded) {
          // Recent achievements
          if (state.userAchievements.isNotEmpty) {
            for (final achievement in state.userAchievements.take(3)) {
              activities.add(ActivityItem(
                icon: Icons.emoji_events,
                iconColor: _getAchievementColor(achievement.rarity),
                title: 'Achievement Unlocked!',
                subtitle: achievement.name,
                description: achievement.description,
                timestamp: DateTime.now().subtract(const Duration(hours: 3)), // Mock timestamp
                type: ActivityType.achievement,
              ));
            }
          }

          // Points activities (mock data for demo)
          activities.add(ActivityItem(
            icon: Icons.star,
            iconColor: Colors.amber,
            title: 'Points Earned',
            subtitle: '+50 points for task completion',
            description: 'Great work on completing the security audit!',
            timestamp: DateTime.now().subtract(const Duration(hours: 2)),
            type: ActivityType.points,
          ));

          // Leaderboard updates
          activities.add(ActivityItem(
            icon: Icons.leaderboard,
            iconColor: Colors.green,
            title: 'Leaderboard Position',
            subtitle: 'Moved up to rank #5',
            description: 'You\'re climbing the monthly leaderboard!',
            timestamp: DateTime.now().subtract(const Duration(hours: 6)),
            type: ActivityType.leaderboard,
          ));
        }

        // Add enterprise activities (mock data)
        activities.addAll([
          ActivityItem(
            icon: Icons.security,
            iconColor: Colors.red,
            title: 'Security Alert',
            subtitle: 'Unusual login activity detected',
            description: 'Login from new device in San Francisco',
            timestamp: DateTime.now().subtract(const Duration(hours: 1)),
            type: ActivityType.security,
          ),
          ActivityItem(
            icon: Icons.business,
            iconColor: Colors.blue,
            title: 'Organization Update',
            subtitle: 'New member added to team',
            description: 'John Smith joined as Developer',
            timestamp: DateTime.now().subtract(const Duration(hours: 4)),
            type: ActivityType.organization,
          ),
          ActivityItem(
            icon: Icons.analytics,
            iconColor: Colors.purple,
            title: 'Analytics Report',
            subtitle: 'Weekly performance summary ready',
            description: 'Team productivity increased by 15%',
            timestamp: DateTime.now().subtract(const Duration(days: 1)),
            type: ActivityType.analytics,
          ),
        ]);

        // Sort by timestamp and limit
        activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        final displayActivities = activities.take(maxItems).toList();

        return Container(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showHeader) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Recent Activity',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pushNamed('/activity');
                      },
                      child: const Text('View All'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              
              if (displayActivities.isEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.timeline,
                          size: 48,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'No recent activity',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                ...displayActivities.map((activity) => _ActivityTile(activity: activity)),
            ],
          ),
        );
      },
    );
  }

  Color _getAchievementColor(AchievementRarity rarity) {
    switch (rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.uncommon:
        return Colors.green;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.orange;
    }
  }
}

/// Individual activity item tile
class _ActivityTile extends StatelessWidget {
  final ActivityItem activity;

  const _ActivityTile({required this.activity});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: activity.iconColor.withValues(alpha: 0.1),
          child: Icon(
            activity.icon,
            color: activity.iconColor,
            size: 20,
          ),
        ),
        title: Text(
          activity.title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(activity.subtitle),
            if (activity.description != null) ...[
              const SizedBox(height: 2),
              Text(
                activity.description!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
        trailing: Text(
          _formatTimestamp(activity.timestamp),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        onTap: () {
          // Navigate to activity details based on activity type
          final activityType = activity.type;

          switch (activityType) {
            case ActivityType.achievement:
              Navigator.of(context).pushNamed('/achievements');
              break;
            case ActivityType.points:
              Navigator.of(context).pushNamed('/gamification');
              break;
            case ActivityType.leaderboard:
              Navigator.of(context).pushNamed('/leaderboard');
              break;
            case ActivityType.security:
              Navigator.of(context).pushNamed('/security');
              break;
            case ActivityType.organization:
              Navigator.of(context).pushNamed('/organization');
              break;
            case ActivityType.analytics:
              Navigator.of(context).pushNamed('/analytics');
              break;
          }
        },
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);

    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}h ago';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
}

/// Activity item data model
class ActivityItem {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final String? description;
  final DateTime timestamp;
  final ActivityType type;

  ActivityItem({
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    this.description,
    required this.timestamp,
    required this.type,
  });
}

/// Activity type enumeration
enum ActivityType {
  achievement,
  points,
  leaderboard,
  security,
  organization,
  analytics,
}