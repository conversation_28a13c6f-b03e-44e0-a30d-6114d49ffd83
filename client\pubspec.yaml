name: quester_client
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  
  # Shared models, DTOs, and utilities
  shared:
    path: ../shared
  
  # State management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  formz: ^0.7.0
  
  # UUID generation
  uuid: ^4.5.1
  
  # HTTP client
  http: ^1.1.0
  
  # WebSocket client
  web_socket_channel: ^3.0.1
  
  # Local storage
  shared_preferences: ^2.2.2

  # Network connectivity
  connectivity_plus: ^6.0.5

  # Charts and data visualization
  fl_chart: ^0.68.0
  
  # Authentication and SSO
  oauth2: ^2.0.2
  flutter_secure_storage: ^9.2.2
  
  # Web and API management
  dio: ^5.4.0
  pretty_dio_logger: ^1.3.1
  
  # JSON handling
  json_annotation: ^4.9.0
  
  # Routing
  go_router: ^14.2.3
  
  # UI utilities
  flutter_svg: ^2.0.9
  
  # Authentication UI dependencies
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.0.0
  pin_code_fields: ^8.0.1
  flutter_otp_text_field: ^1.1.3+2
  
  # Additional dependencies for authentication
  crypto: ^3.0.3
  convert: ^3.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

  # Testing utilities
  bloc_test: ^9.1.7
  mocktail: ^1.0.4

  # Code generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
