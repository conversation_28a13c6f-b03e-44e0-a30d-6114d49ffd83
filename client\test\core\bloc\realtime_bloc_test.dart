import 'package:flutter_test/flutter_test.dart';
import 'package:quester_client/core/bloc/realtime/realtime_bloc.dart';

void main() {
  group('RealtimeBloc Tests', () {
    late RealtimeBloc realtimeBloc;

    setUp(() {
      realtimeBloc = RealtimeBloc();
    });

    tearDown(() {
      realtimeBloc.close();
    });

    test('initial state is RealtimeInitial', () {
      expect(realtimeBloc.state, isA<RealtimeInitial>());
    });

    test('ConnectWebSocket event can be created', () {
      const event = ConnectWebSocket('ws://localhost:8080');
      expect(event.serverUrl, equals('ws://localhost:8080'));
      expect(event.userId, isNull);
    });

    test('ConnectWebSocket event with userId can be created', () {
      const event = ConnectWebSocket('ws://localhost:8080', userId: 'test-user');
      expect(event.serverUrl, equals('ws://localhost:8080'));
      expect(event.userId, equals('test-user'));
    });

    test('SubscribeToEvents event can be created', () {
      const event = SubscribeToEvents(['quest-updates', 'notifications']);
      expect(event.subscriptionTypes, equals(['quest-updates', 'notifications']));
    });

    test('DisconnectWebSocket event can be created', () {
      final event = DisconnectWebSocket();
      expect(event, isA<DisconnectWebSocket>());
    });
  });

  group('RealtimeState Tests', () {
    test('RealtimeConnected can be created with required parameters', () {
      final now = DateTime.now();
      final state = RealtimeConnected(
        connectionId: 'test-connection',
        subscriptions: ['quest-updates'],
        connectedAt: now,
      );
      
      expect(state.connectionId, equals('test-connection'));
      expect(state.subscriptions, equals(['quest-updates']));
      expect(state.connectedAt, equals(now));
    });

    test('RealtimeConnected equality works correctly', () {
      final now = DateTime.now();
      final state1 = RealtimeConnected(
        connectionId: 'test',
        subscriptions: ['quest-updates'],
        connectedAt: now,
      );
      final state2 = RealtimeConnected(
        connectionId: 'test',
        subscriptions: ['quest-updates'],
        connectedAt: now,
      );
      
      expect(state1, equals(state2));
    });

    test('RealtimeConnected copyWith works correctly', () {
      final now = DateTime.now();
      final original = RealtimeConnected(
        connectionId: 'test',
        subscriptions: ['quest-updates'],
        connectedAt: now,
      );
      
      final updated = original.copyWith(
        subscriptions: ['quest-updates', 'notifications'],
      );
      
      expect(updated.connectionId, equals('test'));
      expect(updated.subscriptions, equals(['quest-updates', 'notifications']));
      expect(updated.connectedAt, equals(now));
    });

    test('RealtimeError can be created', () {
      const state = RealtimeError('Connection failed');
      expect(state.message, equals('Connection failed'));
    });

    test('RealtimeDisconnected can be created', () {
      const state = RealtimeDisconnected(reason: 'User initiated');
      expect(state.reason, equals('User initiated'));
    });

    test('RealtimeReconnecting can be created', () {
      const state = RealtimeReconnecting(3);
      expect(state.attemptNumber, equals(3));
    });
  });

  group('Event Props Tests', () {
    test('ConnectWebSocket props include serverUrl and userId', () {
      const event = ConnectWebSocket('ws://test', userId: 'user123');
      expect(event.props, equals(['ws://test', 'user123']));
    });

    test('SubscribeToEvents props include subscriptionTypes', () {
      const event = SubscribeToEvents(['type1', 'type2']);
      expect(event.props, equals([['type1', 'type2']]));
    });

    test('DisconnectWebSocket props are empty', () {
      final event = DisconnectWebSocket();
      expect(event.props, isEmpty);
    });
  });
}
