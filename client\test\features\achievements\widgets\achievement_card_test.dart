import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quester_client/features/gamification/widgets/achievement_badge.dart';
import 'package:shared/shared.dart';
import '../../../test_helpers/widget_test_helpers.dart';

void main() {
  group('AchievementBadge Widget Tests', () {
    late Achievement mockAchievement;

    setUp(() {
      mockAchievement = MockDataFactory.createMockAchievement(
        id: 'achievement_123',
        name: 'Test Achievement',
        description: 'This is a test achievement description',
        type: AchievementType.progress,
        rarity: AchievementRarity.common,
        pointsAwarded: 100,
      );
    });

    group('Basic Display Tests', () {
      testWidgets('displays achievement information correctly', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Verify achievement title is displayed
        expect(find.text('Test Achievement'), findsOneWidget);
        
        // Verify achievement description is displayed
        expect(find.text('This is a test achievement description'), findsOneWidget);
        
        // Verify achievement points are displayed
        expect(find.textContaining('100'), findsOneWidget);
      });

      testWidgets('displays achievement category correctly', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Verify category is displayed
        expect(find.textContaining('Completion'), findsOneWidget);
      });

      testWidgets('displays achievement rarity correctly', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Verify rarity is displayed
        expect(find.textContaining('Common'), findsOneWidget);
      });
    });

    group('Rarity Display Tests', () {
      testWidgets('displays common rarity with correct styling', (WidgetTester tester) async {
        final commonAchievement = MockDataFactory.createMockAchievement(
          rarity: AchievementRarity.common,
          name: 'Common Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: commonAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Common Achievement'), findsOneWidget);
        expect(find.textContaining('Common'), findsOneWidget);
      });

      testWidgets('displays rare rarity with correct styling', (WidgetTester tester) async {
        final rareAchievement = MockDataFactory.createMockAchievement(
          rarity: AchievementRarity.rare,
          name: 'Rare Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: rareAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Rare Achievement'), findsOneWidget);
        expect(find.textContaining('Rare'), findsOneWidget);
      });

      testWidgets('displays legendary rarity with correct styling', (WidgetTester tester) async {
        final legendaryAchievement = MockDataFactory.createMockAchievement(
          rarity: AchievementRarity.legendary,
          name: 'Legendary Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: legendaryAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Legendary Achievement'), findsOneWidget);
        expect(find.textContaining('Legendary'), findsOneWidget);
      });
    });

    group('Category Display Tests', () {
      testWidgets('displays completion category correctly', (WidgetTester tester) async {
        final completionAchievement = MockDataFactory.createMockAchievement(
          type: AchievementType.progress,
          name: 'Completion Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: completionAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Completion Achievement'), findsOneWidget);
        expect(find.textContaining('Completion'), findsOneWidget);
      });

      testWidgets('displays streak category correctly', (WidgetTester tester) async {
        final streakAchievement = MockDataFactory.createMockAchievement(
          type: AchievementType.consistency,
          name: 'Streak Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: streakAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Streak Achievement'), findsOneWidget);
        expect(find.textContaining('Streak'), findsOneWidget);
      });

      testWidgets('displays collaboration category correctly', (WidgetTester tester) async {
        final collaborationAchievement = MockDataFactory.createMockAchievement(
          type: AchievementType.collaboration,
          name: 'Collaboration Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: collaborationAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Collaboration Achievement'), findsOneWidget);
        expect(find.textContaining('Collaboration'), findsOneWidget);
      });
    });

    group('Interaction Tests', () {
      testWidgets('handles tap events correctly', (WidgetTester tester) async {
        bool tapped = false;
        
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(
            achievement: mockAchievement,
            onTap: () => tapped = true,
          ),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Tap the achievement card
        await WidgetTestHelpers.simulateUserTap(tester, find.byType(AchievementBadge));

        // Verify tap was handled
        expect(tapped, isTrue);
      });

      testWidgets('handles long press events', (WidgetTester tester) async {
        bool longPressed = false;
        
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(
            achievement: mockAchievement,
            onTap: () => longPressed = true,
          ),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Long press the achievement card
        await tester.longPress(find.byType(AchievementBadge));
        await tester.pump();

        // Verify long press was handled
        expect(longPressed, isTrue);
      });
    });

    group('Accessibility Tests', () {
      testWidgets('has proper accessibility labels', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);
        await WidgetTestHelpers.verifyAccessibility(tester);

        // Verify semantic labels exist
        expect(find.bySemanticsLabel(RegExp('Test Achievement')), findsOneWidget);
      });

      testWidgets('provides proper semantic information', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Verify semantic information is available
        final cardFinder = find.byType(AchievementBadge);
        expect(cardFinder, findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles very long achievement titles', (WidgetTester tester) async {
        final longTitleAchievement = MockDataFactory.createMockAchievement(
          name: 'This is a very long achievement title that should be handled gracefully by the widget without causing overflow or layout issues',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: longTitleAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Should not throw overflow errors
        expect(find.textContaining('This is a very long achievement title'), findsOneWidget);
      });

      testWidgets('handles very long descriptions', (WidgetTester tester) async {
        final longDescAchievement = MockDataFactory.createMockAchievement(
          description: 'This is an extremely long achievement description that contains a lot of text and should be handled properly by the widget layout system without causing any overflow issues or breaking the UI design patterns.',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: longDescAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Should not throw overflow errors
        expect(find.textContaining('This is an extremely long achievement description'), findsOneWidget);
      });

      testWidgets('handles zero points achievement', (WidgetTester tester) async {
        final zeroPointsAchievement = MockDataFactory.createMockAchievement(
          pointsAwarded: 0,
          name: 'Zero Points Achievement',
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: zeroPointsAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        expect(find.text('Zero Points Achievement'), findsOneWidget);
        expect(find.textContaining('0'), findsOneWidget);
      });
    });

    group('Performance Tests', () {
      testWidgets('builds efficiently', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        final buildTime = await PerformanceTestHelpers.measureBuildTime(tester, widget);
        
        // Widget should build quickly (under 100ms)
        expect(buildTime.inMilliseconds, lessThan(100));
      });

      testWidgets('handles multiple achievements efficiently', (WidgetTester tester) async {
        final achievements = List.generate(10, (index) => 
          MockDataFactory.createMockAchievement(
            name: 'Achievement $index',
          )
        );

        final widget = WidgetTestHelpers.createTestApp(
          child: Column(
            children: achievements.map((achievement) => 
              AchievementBadge(achievement: achievement)
            ).toList(),
          ),
        );

        final buildTime = await PerformanceTestHelpers.measureBuildTime(tester, widget);
        
        // Multiple widgets should still build reasonably quickly
        expect(buildTime.inMilliseconds, lessThan(500));
      });
    });

    group('Responsive Design Tests', () {
      testWidgets('adapts to different screen sizes', (WidgetTester tester) async {
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.testResponsiveWidget(
          tester,
          widget,
          WidgetTestHelpers.commonTestSizes,
          (size) {
            // Verify widget renders correctly at different sizes
            expect(find.byType(AchievementBadge), findsOneWidget);
            expect(find.text('Test Achievement'), findsOneWidget);
          },
        );
      });

      testWidgets('maintains readability on small screens', (WidgetTester tester) async {
        await tester.binding.setSurfaceSize(const Size(320, 568)); // iPhone SE
        
        final widget = WidgetTestHelpers.createTestApp(
          child: AchievementBadge(achievement: mockAchievement),
        );

        await WidgetTestHelpers.pumpTestWidget(tester, widget);

        // Verify content is still visible and accessible
        expect(find.text('Test Achievement'), findsOneWidget);
        expect(find.textContaining('100'), findsOneWidget);
      });
    });
  });
}
