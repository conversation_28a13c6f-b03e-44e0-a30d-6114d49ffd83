import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quester_client/features/analytics/screens/analytics_dashboard_screen.dart';
import 'package:quester_client/features/analytics/bloc/analytics_bloc.dart';
import 'package:quester_client/core/services/api_service.dart';

// Mock API service for testing
class MockApiService extends ApiService {
  @override
  Future<ApiResponse> get(String endpoint, {Map<String, String>? queryParameters}) async {
    return const ApiResponse(
      data: {'message': 'test response'},
      statusCode: 200,
      success: true,
    );
  }

  @override
  Future<ApiResponse> post(String endpoint, {Map<String, dynamic>? body}) async {
    return const ApiResponse(
      data: {'message': 'test response'},
      statusCode: 200,
      success: true,
    );
  }
}

void main() {
  group('AdvancedAnalyticsDashboard Widget Tests', () {
    late AnalyticsBloc analyticsBloc;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      analyticsBloc = AnalyticsBloc(apiService: mockApiService);
    });

    tearDown(() {
      analyticsBloc.close();
    });

    testWidgets('should display app bar with correct title', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => analyticsBloc,
            child: const AdvancedAnalyticsDashboard(),
          ),
        ),
      );

      expect(find.text('Advanced Analytics'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should display tab bar with correct tabs', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => analyticsBloc,
            child: const AdvancedAnalyticsDashboard(),
          ),
        ),
      );

      expect(find.text('Overview'), findsOneWidget);
      expect(find.text('Quests'), findsOneWidget);
      expect(find.text('Tasks'), findsOneWidget);
      expect(find.text('Users'), findsOneWidget);
      expect(find.text('Gamification'), findsOneWidget);
      expect(find.text('Performance'), findsOneWidget);
    });

    testWidgets('should display date range header', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => analyticsBloc,
            child: const AdvancedAnalyticsDashboard(),
          ),
        ),
      );

      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
    });

    testWidgets('should handle tab navigation', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => analyticsBloc,
            child: const AdvancedAnalyticsDashboard(),
          ),
        ),
      );

      // Tap on Quests tab
      await tester.tap(find.text('Quests'));
      await tester.pumpAndSettle();

      // Should navigate to quests tab
      expect(find.text('Quest Analytics'), findsOneWidget);
    });

    testWidgets('should display all required tabs', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => analyticsBloc,
            child: const AdvancedAnalyticsDashboard(),
          ),
        ),
      );

      // Check all 6 tabs are present
      expect(find.byType(Tab), findsNWidgets(6));
    });
  });

  group('Analytics Dashboard Integration Tests', () {
    test('should create dashboard with proper initial state', () {
      const dashboard = AdvancedAnalyticsDashboard();
      expect(dashboard.organizationId, isNull);
      expect(dashboard.userId, isNull);
    });

    test('should create dashboard with organizationId', () {
      const dashboard = AdvancedAnalyticsDashboard(organizationId: 'org-123');
      expect(dashboard.organizationId, equals('org-123'));
      expect(dashboard.userId, isNull);
    });

    test('should create dashboard with userId', () {
      const dashboard = AdvancedAnalyticsDashboard(userId: 'user-456');
      expect(dashboard.organizationId, isNull);
      expect(dashboard.userId, equals('user-456'));
    });

    test('should create dashboard with both parameters', () {
      const dashboard = AdvancedAnalyticsDashboard(
        organizationId: 'org-123',
        userId: 'user-456',
      );
      expect(dashboard.organizationId, equals('org-123'));
      expect(dashboard.userId, equals('user-456'));
    });

    testWidgets('should work with parameters', (tester) async {
      final apiService = MockApiService();
      final bloc = AnalyticsBloc(apiService: apiService);

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AnalyticsBloc>(
            create: (context) => bloc,
            child: const AdvancedAnalyticsDashboard(
              organizationId: 'test-org-123',
              userId: 'test-user-456',
            ),
          ),
        ),
      );

      expect(find.byType(AdvancedAnalyticsDashboard), findsOneWidget);
      
      await bloc.close();
    });
  });
}
