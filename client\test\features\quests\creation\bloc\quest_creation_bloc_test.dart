import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:quester_client/features/quests/creation/bloc/quest_creation_bloc.dart';
import 'package:quester_client/features/quests/creation/bloc/quest_creation_event.dart';
import 'package:quester_client/features/quests/creation/bloc/quest_creation_state.dart';
import 'package:quester_client/features/quests/creation/services/quest_validation_service.dart';
import 'package:quester_client/core/services/api_service.dart';
import 'package:shared/shared.dart';

// Mock classes
class MockApiService extends Mock implements ApiService {}
class MockQuestValidationService extends Mock implements QuestValidationService {}

// Register fallback values for mocktail
class FakeQuestCreationInProgress extends Fake implements QuestCreationInProgress {}

void main() {
  group('QuestCreationBloc', () {
    late QuestCreationBloc questCreationBloc;
    late MockApiService mockApiService;
    late MockQuestValidationService mockValidationService;

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(FakeQuestCreationInProgress());
    });

    setUp(() {
      mockApiService = MockApiService();
      mockValidationService = MockQuestValidationService();

      // Set up default mock responses
      when(() => mockValidationService.validateQuest(any())).thenReturn(
        const QuestValidationResult(
          isValid: true,
          fieldErrors: {},
          generalErrors: [],
        ),
      );

      questCreationBloc = QuestCreationBloc(
        apiService: mockApiService,
        validationService: mockValidationService,
      );
    });

    tearDown(() {
      questCreationBloc.close();
    });

    test('initial state is QuestCreationInitial', () {
      expect(questCreationBloc.state, isA<QuestCreationInitial>());
    });

    group('UpdateTitle', () {
      blocTest<QuestCreationBloc, QuestCreationState>(
        'emits updated state with new title',
        build: () => questCreationBloc,
        seed: () => const QuestCreationInProgress(),
        act: (bloc) => bloc.add(const UpdateTitle('New Quest Title')),
        expect: () => [
          isA<QuestCreationInProgress>()
              .having((state) => state.title, 'title', 'New Quest Title')
              .having((state) => state.hasUnsavedChanges, 'hasUnsavedChanges', true),
        ],
      );
    });

    group('UpdateDescription', () {
      blocTest<QuestCreationBloc, QuestCreationState>(
        'emits updated state with new description',
        build: () => questCreationBloc,
        seed: () => const QuestCreationInProgress(),
        act: (bloc) => bloc.add(const UpdateDescription('New quest description')),
        expect: () => [
          isA<QuestCreationInProgress>()
              .having((state) => state.description, 'description', 'New quest description')
              .having((state) => state.hasUnsavedChanges, 'hasUnsavedChanges', true),
        ],
      );
    });

    group('UpdateCategory', () {
      blocTest<QuestCreationBloc, QuestCreationState>(
        'emits updated state with new category',
        build: () => questCreationBloc,
        seed: () => const QuestCreationInProgress(),
        act: (bloc) => bloc.add(const UpdateCategory(QuestCategory.learning)),
        expect: () => [
          isA<QuestCreationInProgress>()
              .having((state) => state.category, 'category', QuestCategory.learning)
              .having((state) => state.hasUnsavedChanges, 'hasUnsavedChanges', true),
        ],
      );
    });
  });
}
