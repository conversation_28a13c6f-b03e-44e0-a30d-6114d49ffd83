import 'dart:io';
import 'dart:convert';
import 'dart:math';

import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:uuid/uuid.dart';
import 'package:server/services/database_service.dart';
import 'package:server/services/websocket_service.dart';
import 'package:server/routes/auth_routes.dart';
import 'package:server/routes/analytics_routes.dart';
import 'package:server/services/cache_service.dart';
import 'package:server/middleware/performance_middleware.dart';
import 'package:server/routes/monitoring_routes.dart';
import 'package:server/services/gamification_service.dart';
import 'package:server/services/collaboration_service.dart';
import 'package:server/routes/gamification_routes.dart';
import 'package:server/routes/collaboration_routes.dart';
import 'package:server/routes/enterprise_routes.dart';
import 'package:server/routes/freelancing_routes.dart';
import 'package:server/routes/learning_routes.dart';
import 'package:server/services/freelancing_service.dart';
import 'package:server/services/learning_service.dart';

/// Sophisticated Dart HTTP server with comprehensive gamification APIs
/// Matching Phase 2 requirements from CLAUDE.md with PostgreSQL database integration
class QuesterServer {
  static const String version = '2.1.0';
  static const String apiVersion = 'v1';
  
  // Database service for real data operations
  static late DatabaseService _dbService;
  static late GamificationService _gamificationService;
  static late CollaborationService _collaborationService;
  static late FreelancingService _freelancingService;
  static late LearningService _learningService;
  
  // Mock data storage - fallback when database unavailable
  static final Map<String, dynamic> _mockData = <String, dynamic>{};
  static final Random _random = Random();
  static final Uuid _uuid = const Uuid();
  
  static bool _useMockData = false;
  
  static Future<void> main(List<String> args) async {
    print('🚀 Starting Quester Server v$version...');
    
    // Initialize database service
    _dbService = DatabaseService();
    try {
      await _dbService.initialize();
      print('✅ Database connection established - using PostgreSQL');
      _useMockData = false;
    } catch (e) {
      print('⚠️  Database connection failed, falling back to mock data: $e');
      _useMockData = true;
      await _initializeMockData();
    }
    
    // Initialize gamification and collaboration services
    _gamificationService = GamificationService(_dbService);
    _collaborationService = CollaborationService(_dbService, _gamificationService);
    _freelancingService = FreelancingService(_dbService);
    _learningService = LearningService(_dbService);
    print('✅ All services initialized (gamification, freelancing, learning)');
    
    // Initialize performance services for Phase 5 completion
    final cacheService = CacheService();
    try {
      await cacheService.initialize();
      print('✅ Cache service initialized - Redis connected');
    } catch (e) {
      print('⚠️  Cache service initialization failed: $e');
    }
    
    // Initialize WebSocket service for real-time features
    WebSocketService.initialize();
    
    // Initialize Gamification Routes
    try {
      GamificationRoutes.initialize(_gamificationService);
      print('✅ Gamification routes initialized');
    } catch (e) {
      print('⚠️  Gamification routes initialization failed: $e');
    }
    
    // Initialize Collaboration Routes  
    try {
      CollaborationRoutes.initialize(_collaborationService);
      print('✅ Collaboration routes initialized');
    } catch (e) {
      print('⚠️  Collaboration routes initialization failed: $e');
    }
    
    // Initialize Enterprise Routes
    try {
      EnterpriseRoutes.initialize();
      print('✅ Enterprise routes initialized');  
    } catch (e) {
      print('⚠️  Enterprise routes initialization failed: $e');
    }
    
    // Initialize Freelancing Routes
    try {
      FreelancingRoutes.initialize(_freelancingService);
      print('✅ Freelancing routes initialized');
    } catch (e) {
      print('⚠️  Freelancing routes initialization failed: $e');
    }
    
    // Initialize Learning Routes
    try {
      LearningRoutes.initialize(_learningService);
      print('✅ Learning routes initialized');
    } catch (e) {
      print('⚠️  Learning routes initialization failed: $e');
    }
    
    // Initialize Analytics Service for advanced data tracking
    try {
      await AnalyticsRoutes.initialize();
      print('✅ Analytics service initialized - ready for event tracking');
    } catch (e) {
      print('⚠️  Analytics service initialization failed: $e');
    }
    
    // Initialize Authentication Service
    try {
      await AuthRoutes.initialize();
      print('✅ Auth service initialized - ready for authentication');
    } catch (e) {
      print('⚠️  Auth service initialization failed: $e');
    }
    
    // Initialize Route Services
    GamificationRoutes.initialize(_gamificationService);
    CollaborationRoutes.initialize(_collaborationService);
    print('✅ Route services initialized');
    
    // Enhanced router with comprehensive gamification endpoints
    final router = Router()
      // Health and system endpoints
      ..get('/health', _healthHandler)
      ..get('/api/health', _apiHealthHandler)
      ..get('/api/status', _statusHandler)
      ..get('/api/version', _versionHandler)
      
      // Performance monitoring endpoints (Phase 5 completion)
      ..mount('/monitoring', MonitoringRoutes.createRouter().call)
      
      // Authentication routes
      ..mount('/auth', AuthRoutes.router.call)
      
      // Advanced Analytics routes
      ..mount('/analytics', AnalyticsRoutes.router.call)
      
      // Gamification API routes
      ..mount('/api/v1/gamification', GamificationRoutes.createRouter().call)
      
      // Collaboration API routes
      ..mount('/api/v1/collaboration', CollaborationRoutes.createRouter().call)
      
      // Enterprise API routes
      ..mount('/api/v1/enterprise', EnterpriseRoutes.createRouter().call)
      
      // Freelancing API routes
      ..mount('/api/v1/freelancing', FreelancingRoutes.createRouter().call)
      
      // Learning Management System API routes
      ..mount('/api/v1/learning', LearningRoutes.createRouter().call)
      
      // Real-time WebSocket endpoints
      ..get('/ws', WebSocketService.getWebSocketHandler())
      ..get('/api/v1/gamification/ws', WebSocketService.getWebSocketHandler())
      ..get('/api/v1/gamification/ws/stats', _webSocketStatsHandler);

    // Create server with CORS and performance optimization middleware
    final handler = Pipeline()
        .addMiddleware(_corsMiddleware())
        .addMiddleware(logRequests())
        .addMiddleware(PerformanceMiddleware.performanceHandler())
        .addMiddleware(PerformanceMiddleware.responseTimeHandler())
        .addMiddleware(PerformanceMiddleware.compressionHandler())
        .addMiddleware(PerformanceMiddleware.cachingHandler())
        .addMiddleware(PerformanceMiddleware.rateLimitingHandler(requestsPerMinute: 300, burstLimit: 30))
        .addHandler(router.call);

        // Start the server
    final server = await serve(handler, InternetAddress.anyIPv4, 8080);
    
    print('🚀 Quester Gamification Server v$version started');
    print('📡 Server listening on ${server.address.host}:${server.port}');
    print('🎯 API Base URL: http://${server.address.host}:${server.port}/gamification');
    print('🏥 Health Check: http://${server.address.host}:${server.port}/health');
    print('📊 API Status: http://${server.address.host}:${server.port}/api/status');

    // Graceful shutdown
    ProcessSignal.sigint.watch().listen((_) async {
      print('🛑 Shutting down server...');
      await server.close(force: true);
      if (!_useMockData) {
        await _dbService.close();
      }
      exit(0);
    });
  }

  /// WebSocket statistics endpoint
  static Future<Response> _webSocketStatsHandler(Request request) async {
    try {
      final stats = WebSocketService.getStats();
      return Response.ok(jsonEncode(stats), headers: {'content-type': 'application/json'});
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': e.toString()}),
        headers: {'content-type': 'application/json'},
      );
    }
  }


  /// Initialize comprehensive mock data system
  static Future<void> _initializeMockData() async {
    print('🔧 Initializing intelligent mock data system...');
    
    // Initialize users data
    _mockData['users'] = List.generate(1000, (index) {
      final userId = _uuid.v4();
      final roles = ['novice', 'explorer', 'adventurer', 'hero', 'legend', 'mythic'];
      final role = roles[_random.nextInt(roles.length)];
      final points = _random.nextInt(30000);
      
      return {
        'id': userId,
        'username': 'user_${index.toString().padLeft(4, '0')}',
        'email': 'user_$<EMAIL>',
        'display_name': 'User ${index + 1}',
        'role': role,
        'total_points': points,
        'available_points': points,
        'level': _calculateLevel(points),
        'current_streak': _random.nextInt(30),
        'longest_streak': _random.nextInt(100),
        'created_at': DateTime.now().subtract(Duration(days: _random.nextInt(365))).toIso8601String(),
      };
    });

    // Initialize achievements data
    _mockData['achievements'] = [
      {
        'id': _uuid.v4(),
        'name': 'First Steps',
        'description': 'Complete your first task',
        'category': 'quest_completion',
        'rarity': 'common',
        'points_reward': 50,
        'icon_url': 'achievements/first_steps.png',
        'requirements': {'tasks_completed': 1},
      },
      {
        'id': _uuid.v4(),
        'name': 'Team Player',
        'description': 'Help 5 team members with their tasks',
        'category': 'collaboration',
        'rarity': 'uncommon',
        'points_reward': 150,
        'icon_url': 'achievements/team_player.png',
        'requirements': {'collaborations': 5},
      },
      // Add more achievements...
    ];

    // Initialize rewards data
    _mockData['rewards'] = [
      {
        'id': _uuid.v4(),
        'name': 'Custom Avatar',
        'description': 'Unlock custom avatar selection',
        'type': 'cosmetic',
        'point_cost': 500,
        'requirements': {},
        'icon_url': 'rewards/custom_avatar.png',
        'is_available': true,
      },
      // Add more rewards...
    ];

    // Initialize user achievements
    _mockData['user_achievements'] = <String, List<Map<String, dynamic>>>{};
    
    print('✅ Mock data system initialized with comprehensive gamification data');
  }

  /// CORS middleware
  static Middleware _corsMiddleware() {
    return (handler) {
      return (request) async {
        if (request.method == 'OPTIONS') {
          return Response.ok(null, headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization, X-Requested-With',
            'Access-Control-Max-Age': '3600',
          });
        }

        final response = await handler(request);
        return response.change(headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization, X-Requested-With',
        });
      };
    };
  }


  /// Calculate user level based on points
  static int _calculateLevel(int points) {
    if (points < 500) return 1;
    if (points < 2000) return 2;
    if (points < 5000) return 3;
    if (points < 10000) return 4;
    if (points < 25000) return 5;
    return 6 + ((points - 25000) ~/ 10000);
  }

  // Health check handlers
  static Response _healthHandler(Request request) {
    final uptime = DateTime.now().millisecondsSinceEpoch;
    return Response.ok(jsonEncode({
      'status': 'healthy',
      'timestamp': DateTime.now().toIso8601String(),
      'uptime_ms': uptime,
      'version': version,
    }), headers: {'content-type': 'application/json'});
  }

  static Response _apiHealthHandler(Request request) {
    return Response.ok(jsonEncode({
      'status': 'operational',
      'timestamp': DateTime.now().toIso8601String(),
      'uptime_seconds': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'version': version,
    }), headers: {'content-type': 'application/json'});
  }

  static Response _statusHandler(Request request) {
    return Response.ok(jsonEncode({
      'status': 'operational',
      'timestamp': DateTime.now().toIso8601String(),
      'uptime_seconds': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'version': version,
    }), headers: {'content-type': 'application/json'});
  }

  static Response _versionHandler(Request request) {
    return Response.ok(jsonEncode({
      'version': version,
      'api_version': apiVersion,
      'timestamp': DateTime.now().toIso8601String(),
    }), headers: {'content-type': 'application/json'});
  }






























  
  
  
  
  
  











}

// Execute main function
void main(List<String> args) async {
  await QuesterServer.main(args);
}