import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shared/shared.dart';

import '../services/analytics_service.dart';

/// Analytics API routes for comprehensive data tracking and analysis
/// 
/// Provides high-performance endpoints for:
/// - Event tracking and batch ingestion
/// - Real-time metrics and dashboard data
/// - User behavior analysis
/// - Historical analytics and trends
/// - Insights and recommendations
class AnalyticsRoutes {
  static final AnalyticsService _analyticsService = AnalyticsService();

  /// Initialize the analytics service
  static Future<void> initialize() async {
    await _analyticsService.initialize();
  }

  /// Get the router with all analytics routes
  static Router get router {
    final router = Router();

    // Health and service status
    router.get('/health', _health);
    router.get('/metrics/performance', _performanceMetrics);

    // Event tracking endpoints
    router.post('/events', _trackEvent);
    router.post('/events/batch', _trackEventsBatch);

    // Dashboard and real-time metrics
    router.get('/dashboard/<organizationId>', _getDashboardMetrics);
    router.get('/metrics/<organizationId>', _getMetrics);
    router.get('/metrics/<organizationId>/historical', _getHistoricalMetrics);

    // User behavior and engagement
    router.get('/engagement/<organizationId>', _getEngagementMetrics);
    router.get('/behavior/<userId>', _getUserBehavior);
    router.post('/behavior/analyze', _analyzeBehaviorBatch);

    // Insights and recommendations
    router.get('/insights/<organizationId>', _getInsights);
    router.post('/insights/generate', _generateInsights);

    // Analytics query endpoints
    router.post('/query', _executeQuery);
    router.get('/query/templates', _getQueryTemplates);

    return router;
  }

  /// Health check endpoint
  static Future<Response> _health(Request request) async {
    try {
      final performanceMetrics = _analyticsService.getPerformanceMetrics();
      
      return Response.ok(
        jsonEncode({
          'status': 'healthy',
          'service': 'analytics',
          'version': '1.0.0',
          'timestamp': DateTime.now().toUtc().toIso8601String(),
          'performance': performanceMetrics,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'status': 'unhealthy',
          'error': e.toString(),
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get detailed performance metrics
  static Future<Response> _performanceMetrics(Request request) async {
    try {
      final metrics = _analyticsService.getPerformanceMetrics();
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': metrics,
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get performance metrics: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Track a single analytics event
  static Future<Response> _trackEvent(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Extract user info from headers (simplified for demo)
      final userAgent = request.headers['user-agent'];
      final ipAddress = _getClientIP(request);

      final eventRequest = AnalyticsEventRequest.fromJson(data);

      final eventId = await _analyticsService.trackEvent(
        organizationId: eventRequest.organizationId,
        userId: eventRequest.userId,
        sessionId: eventRequest.sessionId,
        eventType: eventRequest.eventType,
        eventName: eventRequest.eventName,
        eventData: eventRequest.eventData,
        eventProperties: eventRequest.eventProperties,
        userAgent: userAgent,
        ipAddress: ipAddress,
        referrer: eventRequest.referrer,
        pageUrl: eventRequest.pageUrl,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'event_id': eventId},
          'message': 'Event tracked successfully',
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to track event: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Track multiple events in a batch
  static Future<Response> _trackEventsBatch(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final batch = AnalyticsEventsBatch.fromJson(data);

      if (!batch.isValid) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Invalid batch: must contain 1-100 events with matching organization IDs',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final eventIds = await _analyticsService.trackEventsBatch(
        organizationId: batch.organizationId,
        events: batch.events,
        clientInfo: batch.clientInfo,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'event_ids': eventIds,
            'processed_count': eventIds.length,
          },
          'message': 'Events batch processed successfully',
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to process events batch: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get dashboard metrics for an organization
  static Future<Response> _getDashboardMetrics(Request request) async {
    try {
      final organizationId = request.params['organizationId']!;
      
      // Parse query parameters for time range
      final timeRange = _parseTimeRange(request);

      final metrics = await _analyticsService.calculateRealTimeMetrics(
        organizationId: organizationId,
        timeRange: timeRange,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': metrics.toJson(),
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get dashboard metrics: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get general metrics for an organization
  static Future<Response> _getMetrics(Request request) async {
    try {
      final organizationId = request.params['organizationId']!;
      final timeRange = _parseTimeRange(request);

      // For now, delegate to dashboard metrics
      // In full implementation, this could return different metric sets
      final metrics = await _analyticsService.calculateRealTimeMetrics(
        organizationId: organizationId,
        timeRange: timeRange,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'time_range': timeRange.toJson(),
            'metrics': metrics.toJson(),
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get metrics: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get historical metrics for time-series analysis
  static Future<Response> _getHistoricalMetrics(Request request) async {
    try {
      final organizationId = request.params['organizationId']!;
      final queryParams = request.url.queryParameters;
      
      // Parse time range
      final startDate = DateTime.parse(queryParams['start_date'] ?? 
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String());
      final endDate = DateTime.parse(queryParams['end_date'] ?? 
          DateTime.now().toIso8601String());
      
      // Parse period
      final periodStr = queryParams['period'] ?? 'daily';
      final period = TimePeriod.values.firstWhere(
        (p) => p.name == periodStr,
        orElse: () => TimePeriod.daily,
      );

      final metricCategory = queryParams['category'];

      final metrics = await _analyticsService.getHistoricalMetrics(
        organizationId: organizationId,
        startDate: startDate,
        endDate: endDate,
        period: period,
        metricCategory: metricCategory,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'period': period.name,
            'start_date': startDate.toIso8601String(),
            'end_date': endDate.toIso8601String(),
            'category': metricCategory,
            'metrics': metrics.map((m) => m.toJson()).toList(),
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get historical metrics: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get engagement metrics for an organization
  static Future<Response> _getEngagementMetrics(Request request) async {
    try {
      final organizationId = request.params['organizationId']!;
      final timeRange = _parseTimeRange(request);

      // Use dashboard metrics which include engagement data
      final metrics = await _analyticsService.calculateRealTimeMetrics(
        organizationId: organizationId,
        timeRange: timeRange,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'time_range': timeRange.toJson(),
            'engagement_metrics': {
              'avg_engagement_score': metrics.avgEngagementScore,
              'active_users': metrics.activeUsers,
              'total_actions': metrics.totalEvents,
              'collaboration_rate': metrics.collaborationActions / 
                  (metrics.totalEvents > 0 ? metrics.totalEvents : 1),
              'achievement_rate': metrics.achievementUnlocks / 
                  (metrics.activeUsers > 0 ? metrics.activeUsers : 1),
            },
            'top_users': metrics.topUsers.map((u) => u.toJson()).toList(),
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get engagement metrics: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get user behavior analysis
  static Future<Response> _getUserBehavior(Request request) async {
    try {
      final userId = request.params['userId']!;
      final queryParams = request.url.queryParameters;
      
      final organizationId = queryParams['organization_id'];
      if (organizationId == null) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'organization_id query parameter is required',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final analysisDateStr = queryParams['date'] ?? DateTime.now().toIso8601String();
      final analysisDate = DateTime.parse(analysisDateStr);

      final behavior = await _analyticsService.analyzeUserBehavior(
        userId: userId,
        organizationId: organizationId,
        analysisDate: analysisDate,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': behavior.toJson(),
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get user behavior: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Analyze behavior for multiple users (batch operation)
  static Future<Response> _analyzeBehaviorBatch(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final organizationId = data['organization_id'] as String?;
      final userIds = (data['user_ids'] as List<dynamic>?)?.cast<String>() ?? [];
      final analysisDateStr = data['date'] as String?;

      if (organizationId == null || userIds.isEmpty) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'organization_id and user_ids are required',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      if (userIds.length > 50) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Maximum 50 users per batch request',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final analysisDate = analysisDateStr != null 
          ? DateTime.parse(analysisDateStr) 
          : DateTime.now();

      final results = <String, Map<String, dynamic>>{};

      // Process each user
      for (final userId in userIds) {
        try {
          final behavior = await _analyticsService.analyzeUserBehavior(
            userId: userId,
            organizationId: organizationId,
            analysisDate: analysisDate,
          );
          results[userId] = behavior.toJson();
        } catch (e) {
          results[userId] = {
            'error': 'Failed to analyze user: ${e.toString()}',
          };
        }
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'analysis_date': analysisDate.toIso8601String(),
            'results': results,
            'processed_count': userIds.length,
            'success_count': results.values.where((v) => !v.containsKey('error')).length,
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to analyze behavior batch: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get insights for an organization
  static Future<Response> _getInsights(Request request) async {
    try {
      final organizationId = request.params['organizationId']!;
      final queryParams = request.url.queryParameters;
      final limit = int.tryParse(queryParams['limit'] ?? '10') ?? 10;

      final insights = await _analyticsService.generateInsights(
        organizationId: organizationId,
        limit: limit,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'insights': insights.map((i) => i.toJson()).toList(),
            'count': insights.length,
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get insights: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Generate new insights (force refresh)
  static Future<Response> _generateInsights(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final organizationId = data['organization_id'] as String?;
      final limit = data['limit'] as int? ?? 10;

      if (organizationId == null) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'organization_id is required',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final insights = await _analyticsService.generateInsights(
        organizationId: organizationId,
        limit: limit,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'organization_id': organizationId,
            'insights': insights.map((i) => i.toJson()).toList(),
            'generated_at': DateTime.now().toUtc().toIso8601String(),
          },
          'message': 'Insights generated successfully',
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to generate insights: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Execute a custom analytics query (simplified implementation)
  static Future<Response> _executeQuery(Request request) async {
    try {
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'message': 'Custom query execution not yet implemented',
            'status': 'coming_soon',
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to execute query: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get available query templates
  static Future<Response> _getQueryTemplates(Request request) async {
    try {
      final templates = [
        {
          'id': 'daily_active_users',
          'name': 'Daily Active Users',
          'description': 'Track daily active users over time',
          'category': 'engagement',
        },
        {
          'id': 'feature_adoption',
          'name': 'Feature Adoption',
          'description': 'Monitor adoption of new features',
          'category': 'product',
        },
        {
          'id': 'user_retention',
          'name': 'User Retention',
          'description': 'Analyze user retention cohorts',
          'category': 'retention',
        },
      ];

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'templates': templates,
            'count': templates.length,
          },
          'timestamp': DateTime.now().toUtc().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Failed to get query templates: ${e.toString()}',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Helper methods

  /// Parse time range from request query parameters
  static DateTimeRange _parseTimeRange(Request request) {
    final queryParams = request.url.queryParameters;
    
    final startStr = queryParams['start_date'];
    final endStr = queryParams['end_date'];
    final rangeStr = queryParams['range'];

    if (startStr != null && endStr != null) {
      return DateTimeRange(
        start: DateTime.parse(startStr),
        end: DateTime.parse(endStr),
      );
    }

    // Handle predefined ranges
    final now = DateTime.now().toUtc();
    switch (rangeStr) {
      case '7d':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 7)),
          end: now,
        );
      case '30d':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 30)),
          end: now,
        );
      case '90d':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 90)),
          end: now,
        );
      default:
        // Default to last 7 days
        return DateTimeRange(
          start: now.subtract(const Duration(days: 7)),
          end: now,
        );
    }
  }

  /// Extract client IP address from request
  static String? _getClientIP(Request request) {
    // Check common headers for client IP
    return request.headers['x-forwarded-for'] ??
           request.headers['x-real-ip'] ??
           request.headers['remote-addr'];
  }
}