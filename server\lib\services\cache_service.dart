import 'dart:convert';
import 'dart:io';
import 'package:redis/redis.dart';

/// Redis caching service for Phase 5 performance optimization
/// 
/// Implements tiered caching strategy with TTL policies:
/// - Leaderboard data: 5-minute TTL
/// - User stats: 2-minute TTL for active users
/// - Achievement data: 30-minute TTL
/// - Session state: Sliding expiration (30 minutes)
/// Target: >95% cache hit ratio
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();
  
  RedisConnection? _connection;
  Command? _redis;
  bool _isConnected = false;
  
  // Cache configuration based on technical specification
  static const Duration _leaderboardTtl = Duration(minutes: 5);
  static const Duration _userStatsTtl = Duration(minutes: 2);
  static const Duration _achievementTtl = Duration(minutes: 30);
  static const Duration _sessionTtl = Duration(minutes: 30);
  static const Duration _organizationTtl = Duration(minutes: 15);
  
  // Performance metrics
  int _totalRequests = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;

  /// Get cache hit rate as a percentage
  double get hitRate {
    if (_totalRequests == 0) return 0.0;
    return (_cacheHits / _totalRequests) * 100.0;
  }
  
  // Connection configuration
  static String get _host => Platform.environment['REDIS_HOST'] ?? 'redis';
  static int get _port => int.parse(Platform.environment['REDIS_PORT'] ?? '6379');
  static String? get _password => Platform.environment['REDIS_PASSWORD'];
  
  /// Initialize Redis connection with proper configuration
  Future<void> initialize() async {
    try {
      print('🔗 Connecting to Redis at $_host:$_port...');
      
      _connection = RedisConnection();
      _redis = await _connection!.connect(_host, _port);
      
      // Authenticate if password is provided
      if (_password != null && _password!.isNotEmpty) {
        await _redis!.send_object(['AUTH', _password]);
      }
      
      // Select database 0 for caching (use db 1 for sessions if needed)
      await _redis!.send_object(['SELECT', '0']);
      
      _isConnected = true;
      print('✅ Redis connection established successfully');
      
      // Initialize cache warming for frequently accessed data
      await _warmCache();
      
    } catch (e) {
      print('❌ Redis connection failed: $e');
      _isConnected = false;
      rethrow;
    }
  }
  
  /// Check if Redis connection is healthy
  Future<bool> isHealthy() async {
    if (!_isConnected || _redis == null) return false;
    
    try {
      final response = await _redis!.send_object(['PING']);
      return response == 'PONG';
    } catch (e) {
      print('⚠️ Redis health check failed: $e');
      return false;
    }
  }
  
  /// Get cache performance statistics
  Map<String, dynamic> getCacheStatistics() {
    final hitRatio = _totalRequests > 0 ? _cacheHits / _totalRequests : 0.0;
    
    return {
      'total_requests': _totalRequests,
      'cache_hits': _cacheHits,
      'cache_misses': _cacheMisses,
      'hit_ratio': hitRatio,
      'hit_ratio_percentage': (hitRatio * 100).toStringAsFixed(2),
      'target_hit_ratio': 0.95,
      'performance_status': hitRatio >= 0.95 ? 'excellent' : hitRatio >= 0.90 ? 'good' : 'needs_improvement',
    };
  }
  
  // Leaderboard Caching Methods
  
  /// Cache leaderboard data with 5-minute TTL
  Future<void> cacheLeaderboard(String leaderboardType, List<Map<String, dynamic>> data) async {
    if (!_isConnected) return;
    
    try {
      final key = 'leaderboard:$leaderboardType';
      final jsonData = json.encode(data);
      
      await _redis!.send_object(['SETEX', key, _leaderboardTtl.inSeconds, jsonData]);
      
      // Cache leaderboard metadata
      final metadataKey = 'leaderboard:meta:$leaderboardType';
      final metadata = {
        'type': leaderboardType,
        'count': data.length,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
        'ttl_seconds': _leaderboardTtl.inSeconds,
      };
      await _redis!.send_object(['SETEX', metadataKey, _leaderboardTtl.inSeconds, json.encode(metadata)]);
      
      print('✅ Cached leaderboard: $leaderboardType (${data.length} entries)');
    } catch (e) {
      print('❌ Failed to cache leaderboard $leaderboardType: $e');
    }
  }
  
  /// Get cached leaderboard data
  Future<List<Map<String, dynamic>>?> getCachedLeaderboard(String leaderboardType) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'leaderboard:$leaderboardType';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        final List<dynamic> data = json.decode(cachedData);
        return data.cast<Map<String, dynamic>>();
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached leaderboard $leaderboardType: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  /// Cache leaderboard rankings for quick rank lookups
  Future<void> cacheUserRanking(String leaderboardType, String userId, int rank, double score) async {
    if (!_isConnected) return;
    
    try {
      final key = 'ranking:$leaderboardType:$userId';
      final rankingData = {
        'rank': rank,
        'score': score,
        'leaderboard_type': leaderboardType,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };
      
      await _redis!.send_object(['SETEX', key, _leaderboardTtl.inSeconds, json.encode(rankingData)]);
    } catch (e) {
      print('❌ Failed to cache user ranking: $e');
    }
  }
  
  /// Get cached user ranking
  Future<Map<String, dynamic>?> getCachedUserRanking(String leaderboardType, String userId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'ranking:$leaderboardType:$userId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        return json.decode(cachedData);
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached user ranking: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  // User Stats Caching Methods
  
  /// Cache user statistics with 2-minute TTL for active users
  Future<void> cacheUserStats(String userId, Map<String, dynamic> stats) async {
    if (!_isConnected) return;
    
    try {
      final key = 'user:stats:$userId';
      final statsWithMetadata = {
        ...stats,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
        'cache_version': 'v1',
      };
      
      await _redis!.send_object(['SETEX', key, _userStatsTtl.inSeconds, json.encode(statsWithMetadata)]);
      
      // Track active users for cache warming
      await _redis!.send_object(['SADD', 'active_users', userId]);
      await _redis!.send_object(['EXPIRE', 'active_users', _userStatsTtl.inSeconds * 10]); // Keep active list longer
      
    } catch (e) {
      print('❌ Failed to cache user stats for $userId: $e');
    }
  }
  
  /// Get cached user statistics
  Future<Map<String, dynamic>?> getCachedUserStats(String userId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'user:stats:$userId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        final stats = json.decode(cachedData);
        
        // Remove metadata before returning
        stats.remove('cached_at');
        stats.remove('cache_version');
        
        return stats;
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached user stats for $userId: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  /// Cache user points and level information
  Future<void> cacheUserPoints(String userId, Map<String, dynamic> pointsData) async {
    if (!_isConnected) return;
    
    try {
      final key = 'user:points:$userId';
      await _redis!.send_object(['SETEX', key, _userStatsTtl.inSeconds, json.encode(pointsData)]);
    } catch (e) {
      print('❌ Failed to cache user points for $userId: $e');
    }
  }
  
  /// Get cached user points
  Future<Map<String, dynamic>?> getCachedUserPoints(String userId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'user:points:$userId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        return json.decode(cachedData);
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached user points for $userId: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  // Achievement Caching Methods
  
  /// Cache achievement data with 30-minute TTL
  Future<void> cacheAchievements(List<Map<String, dynamic>> achievements) async {
    if (!_isConnected) return;
    
    try {
      const key = 'achievements:all';
      await _redis!.send_object(['SETEX', key, _achievementTtl.inSeconds, json.encode(achievements)]);
      
      // Cache individual achievements by ID for quick lookup
      for (final achievement in achievements) {
        final achievementId = achievement['id'].toString();
        final achievementKey = 'achievement:$achievementId';
        await _redis!.send_object(['SETEX', achievementKey, _achievementTtl.inSeconds, json.encode(achievement)]);
      }
      
      print('✅ Cached ${achievements.length} achievements');
    } catch (e) {
      print('❌ Failed to cache achievements: $e');
    }
  }
  
  /// Get cached achievements
  Future<List<Map<String, dynamic>>?> getCachedAchievements() async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      const key = 'achievements:all';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        final List<dynamic> data = json.decode(cachedData);
        return data.cast<Map<String, dynamic>>();
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached achievements: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  /// Cache user achievements with progress
  Future<void> cacheUserAchievements(String userId, List<Map<String, dynamic>> userAchievements) async {
    if (!_isConnected) return;
    
    try {
      final key = 'user:achievements:$userId';
      await _redis!.send_object(['SETEX', key, _achievementTtl.inSeconds, json.encode(userAchievements)]);
    } catch (e) {
      print('❌ Failed to cache user achievements for $userId: $e');
    }
  }
  
  /// Get cached user achievements
  Future<List<Map<String, dynamic>>?> getCachedUserAchievements(String userId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'user:achievements:$userId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        final List<dynamic> data = json.decode(cachedData);
        return data.cast<Map<String, dynamic>>();
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached user achievements for $userId: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  // Session State Caching Methods
  
  /// Cache user session with sliding expiration
  Future<void> cacheUserSession(String sessionId, Map<String, dynamic> sessionData) async {
    if (!_isConnected) return;
    
    try {
      final key = 'session:$sessionId';
      await _redis!.send_object(['SETEX', key, _sessionTtl.inSeconds, json.encode(sessionData)]);
      
      // Track session for cleanup
      await _redis!.send_object(['SADD', 'active_sessions', sessionId]);
    } catch (e) {
      print('❌ Failed to cache session $sessionId: $e');
    }
  }
  
  /// Get cached session and refresh TTL (sliding expiration)
  Future<Map<String, dynamic>?> getCachedSession(String sessionId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'session:$sessionId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        
        // Refresh TTL for sliding expiration
        await _redis!.send_object(['EXPIRE', key, _sessionTtl.inSeconds]);
        
        return json.decode(cachedData);
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached session $sessionId: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  /// Remove session from cache
  Future<void> invalidateSession(String sessionId) async {
    if (!_isConnected) return;
    
    try {
      final key = 'session:$sessionId';
      await _redis!.send_object(['DEL', key]);
      await _redis!.send_object(['SREM', 'active_sessions', sessionId]);
    } catch (e) {
      print('❌ Failed to invalidate session $sessionId: $e');
    }
  }
  
  // Enterprise Organization Caching
  
  /// Cache organization data
  Future<void> cacheOrganization(String orgId, Map<String, dynamic> orgData) async {
    if (!_isConnected) return;
    
    try {
      final key = 'org:$orgId';
      await _redis!.send_object(['SETEX', key, _organizationTtl.inSeconds, json.encode(orgData)]);
    } catch (e) {
      print('❌ Failed to cache organization $orgId: $e');
    }
  }
  
  /// Get cached organization data
  Future<Map<String, dynamic>?> getCachedOrganization(String orgId) async {
    if (!_isConnected) return null;
    
    try {
      _totalRequests++;
      final key = 'org:$orgId';
      final cachedData = await _redis!.send_object(['GET', key]);
      
      if (cachedData != null) {
        _cacheHits++;
        return json.decode(cachedData);
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached organization $orgId: $e');
      _cacheMisses++;
      return null;
    }
  }
  
  // Generic Caching Methods for Analytics

  /// Cache generic data with custom TTL
  Future<void> cacheData(String key, Map<String, dynamic> data, Duration ttl) async {
    if (!_isConnected) return;

    try {
      await _redis!.send_object(['SETEX', key, ttl.inSeconds, json.encode(data)]);
    } catch (e) {
      print('❌ Failed to cache data for key $key: $e');
    }
  }

  /// Get cached generic data
  Future<Map<String, dynamic>?> getCachedData(String key) async {
    if (!_isConnected) return null;

    try {
      _totalRequests++;
      final cachedData = await _redis!.send_object(['GET', key]);

      if (cachedData != null) {
        _cacheHits++;
        return json.decode(cachedData);
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached data for key $key: $e');
      _cacheMisses++;
      return null;
    }
  }

  /// Cache list data with custom TTL
  Future<void> cacheListData(String key, List<Map<String, dynamic>> data, Duration ttl) async {
    if (!_isConnected) return;

    try {
      await _redis!.send_object(['SETEX', key, ttl.inSeconds, json.encode(data)]);
    } catch (e) {
      print('❌ Failed to cache list data for key $key: $e');
    }
  }

  /// Get cached list data
  Future<List<Map<String, dynamic>>?> getCachedListData(String key) async {
    if (!_isConnected) return null;

    try {
      _totalRequests++;
      final cachedData = await _redis!.send_object(['GET', key]);

      if (cachedData != null) {
        _cacheHits++;
        final List<dynamic> data = json.decode(cachedData);
        return data.cast<Map<String, dynamic>>();
      } else {
        _cacheMisses++;
        return null;
      }
    } catch (e) {
      print('❌ Failed to get cached list data for key $key: $e');
      _cacheMisses++;
      return null;
    }
  }

  /// Invalidate cache by key pattern
  Future<void> invalidateCachePattern(String pattern) async {
    if (!_isConnected) return;

    try {
      final keys = await _redis!.send_object(['KEYS', pattern]);
      if (keys is List && keys.isNotEmpty) {
        await _redis!.send_object(['DEL', ...keys]);
        print('✅ Invalidated ${keys.length} cache entries matching pattern: $pattern');
      }
    } catch (e) {
      print('❌ Failed to invalidate cache pattern $pattern: $e');
    }
  }

  // Cache Invalidation Methods
  
  /// Invalidate user-related caches when user data changes
  Future<void> invalidateUserCache(String userId) async {
    if (!_isConnected) return;
    
    try {
      final keysToDelete = [
        'user:stats:$userId',
        'user:points:$userId',
        'user:achievements:$userId',
      ];
      
      for (final key in keysToDelete) {
        await _redis!.send_object(['DEL', key]);
      }
      
      // Also invalidate any ranking caches for this user
      final rankingPattern = 'ranking:*:$userId';
      final rankingKeys = await _redis!.send_object(['KEYS', rankingPattern]);
      if (rankingKeys is List && rankingKeys.isNotEmpty) {
        await _redis!.send_object(['DEL', ...rankingKeys]);
      }
      
      print('✅ Invalidated cache for user: $userId');
    } catch (e) {
      print('❌ Failed to invalidate user cache for $userId: $e');
    }
  }
  
  /// Invalidate leaderboard caches when rankings change
  Future<void> invalidateLeaderboardCache(String leaderboardType) async {
    if (!_isConnected) return;
    
    try {
      final keysToDelete = [
        'leaderboard:$leaderboardType',
        'leaderboard:meta:$leaderboardType',
      ];
      
      await _redis!.send_object(['DEL', ...keysToDelete]);
      
      // Invalidate all ranking caches for this leaderboard
      final rankingPattern = 'ranking:$leaderboardType:*';
      final rankingKeys = await _redis!.send_object(['KEYS', rankingPattern]);
      if (rankingKeys is List && rankingKeys.isNotEmpty) {
        await _redis!.send_object(['DEL', ...rankingKeys]);
      }
      
      print('✅ Invalidated leaderboard cache: $leaderboardType');
    } catch (e) {
      print('❌ Failed to invalidate leaderboard cache for $leaderboardType: $e');
    }
  }
  
  /// Clear all achievement caches
  Future<void> invalidateAchievementCache() async {
    if (!_isConnected) return;
    
    try {
      await _redis!.send_object(['DEL', 'achievements:all']);
      
      // Clear individual achievement caches
      final achievementKeys = await _redis!.send_object(['KEYS', 'achievement:*']);
      if (achievementKeys is List && achievementKeys.isNotEmpty) {
        await _redis!.send_object(['DEL', ...achievementKeys]);
      }
      
      print('✅ Invalidated achievement cache');
    } catch (e) {
      print('❌ Failed to invalidate achievement cache: $e');
    }
  }
  
  // Cache Warming Methods
  
  /// Warm cache with frequently accessed data
  Future<void> _warmCache() async {
    try {
      print('🔥 Starting cache warming...');
      
      // This would typically load frequently accessed data from the database
      // For now, we'll simulate this process
      
      // Warm achievement cache (static data, rarely changes)
      // await _warmAchievementCache();
      
      // Warm top leaderboard entries
      // await _warmLeaderboardCache();
      
      print('✅ Cache warming completed');
    } catch (e) {
      print('⚠️ Cache warming partially failed: $e');
    }
  }
  
  /// Monitor and optimize cache performance
  Future<Map<String, dynamic>> getCacheAnalytics() async {
    if (!_isConnected) {
      return {'status': 'disconnected', 'analytics': null};
    }
    
    try {
      // Get Redis info
      final info = await _redis!.send_object(['INFO', 'memory']);
      final dbsize = await _redis!.send_object(['DBSIZE']);
      
      // Get key statistics
      final leaderboardKeys = await _redis!.send_object(['KEYS', 'leaderboard:*']);
      final userStatsKeys = await _redis!.send_object(['KEYS', 'user:stats:*']);
      final sessionKeys = await _redis!.send_object(['KEYS', 'session:*']);
      
      final cacheStats = getCacheStatistics();
      
      return {
        'status': 'connected',
        'cache_performance': cacheStats,
        'redis_info': {
          'db_size': dbsize,
          'memory_info': info,
        },
        'cache_distribution': {
          'leaderboard_keys': leaderboardKeys is List ? leaderboardKeys.length : 0,
          'user_stats_keys': userStatsKeys is List ? userStatsKeys.length : 0,
          'session_keys': sessionKeys is List ? sessionKeys.length : 0,
        },
        'performance_targets': {
          'hit_ratio_target': 0.95,
          'current_hit_ratio': cacheStats['hit_ratio'],
          'target_met': cacheStats['hit_ratio'] >= 0.95,
        },
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      print('❌ Failed to get cache analytics: $e');
      return {'status': 'error', 'error': e.toString()};
    }
  }
  
  /// Close Redis connection
  Future<void> close() async {
    if (_isConnected && _connection != null) {
      try {
        await _connection!.close();
        _isConnected = false;
        print('✅ Redis connection closed');
      } catch (e) {
        print('❌ Error closing Redis connection: $e');
      }
    }
  }

  /// Public getter for connection status
  bool get isConnected => _isConnected;
}