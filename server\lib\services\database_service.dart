import 'dart:convert';
import 'dart:io';
import 'package:postgres/postgres.dart';

/// Production database service for Quester gamification system
/// Replaces the mock system with real PostgreSQL operations
class DatabaseService {
  // Database configuration with environment variable support
  static String get _host => Platform.environment['POSTGRES_HOST'] ?? 'postgres';
  static int get _port => int.parse(Platform.environment['POSTGRES_PORT'] ?? '5432');
  static String get _database => Platform.environment['POSTGRES_DB'] ?? 'questerdb';
  static String get _username => Platform.environment['POSTGRES_USER'] ?? 'quester';
  static String get _password => Platform.environment['POSTGRES_PASSWORD'] ?? '802bda462e2b7e9a72ff2f9d';
  
  Connection? _connection;
  bool _isConnected = false;

  /// Get SSL mode based on environment configuration
  SslMode _getSSLMode() {
    final sslMode = Platform.environment['DB_SSL_MODE']?.toLowerCase();
    final isProduction = Platform.environment['NODE_ENV'] == 'production';

    switch (sslMode) {
      case 'disable':
        return SslMode.disable;
      case 'require':
        return SslMode.require;
      default:
        // In production, require SSL by default, otherwise disable for development
        return isProduction ? SslMode.require : SslMode.disable;
    }
  }

  /// Initialize database connection
  Future<void> initialize() async {
    try {
      print('🔗 Attempting database connection to $_host:$_port/$_database as $_username...');
      
      _connection = await Connection.open(
        Endpoint(
          host: _host,
          port: _port,
          database: _database,
          username: _username,
          password: _password,
        ),
        settings: ConnectionSettings(
          sslMode: _getSSLMode(),
          connectTimeout: const Duration(seconds: 10),
          queryTimeout: const Duration(seconds: 30),
          applicationName: 'Quester-Server',
        ),
      );
      _isConnected = true;
      
      // Test connection with a simple query
      final testResult = await _connection!.execute('SELECT current_database(), current_user, version()');
      final row = testResult.first;
      
      print('✅ Database connection established successfully');
      print('📊 Connected to: ${row[0]} as ${row[1]}');
      print('🔧 PostgreSQL Version: ${row[2]}');
      
    } catch (e) {
      print('❌ Database connection failed: $e');
      print('🔧 Connection attempted: $_host:$_port/$_database (user: $_username)');
      _isConnected = false;
      rethrow;
    }
  }
  
  /// Close database connection
  Future<void> close() async {
    if (_connection != null) {
      await _connection!.close();
      _isConnected = false;
      print('🔌 Database connection closed');
    }
  }
  
  /// Check if database is connected and healthy
  Future<bool> isHealthy() async {
    if (!_isConnected || _connection == null) return false;
    
    try {
      final result = await _connection!.execute('SELECT 1');
      return result.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  /// Get user points and role information
  Future<Map<String, dynamic>?> getUserPoints(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      // First check if user exists, if not create them
      await _ensureUserExists(userId);
      
      final result = await _connection!.execute(
        Sql.named('''
          SELECT up.total_points, up.current_level, up.role::text, up.points_to_next_level,
                 s.current_streak, s.longest_streak,
                 u.username, u.display_name
          FROM quester.user_points up
          JOIN quester.users u ON u.id = up.user_id
          LEFT JOIN quester.streaks s ON s.user_id = up.user_id
          WHERE up.user_id = @userId::uuid
        '''),
        parameters: {'userId': userId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'user_id': userId,
        'total_points': row[0] as int,
        'current_level': row[1] as int,
        'role': row[2].toString(), // Convert enum to string
        'points_to_next_level': row[3] as int,
        'current_streak': row[4] ?? 0,
        'longest_streak': row[5] ?? 0,
        'username': row[6].toString(), // Convert to string to handle any type
        'display_name': row[7]?.toString(), // Convert to string to handle any type
      };
    } catch (e) {
      print('❌ Error getting user points for $userId: $e');
      return null;
    }
  }
  
  /// Award points to a user
  Future<Map<String, dynamic>?> awardPoints(String userId, int points, String activityType, String description) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      return await _connection!.runTx((ctx) async {
        // Ensure user exists
        await _ensureUserExistsInTx(ctx, userId);
        
        // Get current user points
        final currentResult = await ctx.execute(
          Sql.named('''
            SELECT total_points, role::text 
            FROM quester.user_points 
            WHERE user_id = @userId::uuid
          '''),
          parameters: {'userId': userId},
        );
        
        if (currentResult.isEmpty) {
          throw Exception('User points not found after creation');
        }
        
        final currentRow = currentResult.first;
        final currentPoints = currentRow[0] as int;
        final currentRole = currentRow[1].toString(); // Convert enum to string
        
        // Calculate role multiplier
        final multiplier = _getRoleMultiplier(currentRole);
        final finalPoints = (points * multiplier).round();
        final newTotalPoints = currentPoints + finalPoints;
        
        // Calculate new level and role
        final newLevel = _calculateLevel(newTotalPoints);
        final newRole = _calculateRole(newTotalPoints);
        final pointsToNext = _getPointsToNextLevel(newTotalPoints, newRole);
        
        // Update user points
        await ctx.execute(
          Sql.named('''
            UPDATE quester.user_points 
            SET total_points = @totalPoints, current_level = @level, role = @role, points_to_next_level = @pointsToNext
            WHERE user_id = @userId::uuid
          '''),
          parameters: {
            'userId': userId,
            'totalPoints': newTotalPoints,
            'level': newLevel,
            'role': newRole,
            'pointsToNext': pointsToNext,
          },
        );
        
        // Log activity
        await ctx.execute(
          Sql.named('''
            INSERT INTO quester.activity_log (user_id, activity_type, points_earned, description)
            VALUES (@userId::uuid, @activityType, @points, @description)
          '''),
          parameters: {
            'userId': userId,
            'activityType': activityType,
            'points': finalPoints,
            'description': description,
          },
        );
        
        // Update streak if it's a daily activity
        if (activityType == 'task_completion' || activityType == 'quest_completion') {
          await _updateStreak(ctx, userId);
        }
        
        // Update leaderboards
        await _updateLeaderboards(ctx, userId, newTotalPoints);
        
        return {
          'user_id': userId,
          'points_awarded': finalPoints,
          'total_points': newTotalPoints,
          'previous_points': currentPoints,
          'current_level': newLevel,
          'role': newRole,
          'points_to_next_level': pointsToNext,
          'multiplier_applied': multiplier,
          'activity_type': activityType,
          'description': description,
        };
      });
    } catch (e) {
      print('❌ Error awarding points: $e');
      return null;
    }
  }
  
  /// Get comprehensive user statistics (PERFORMANCE OPTIMIZED)
  Future<Map<String, dynamic>?> getUserStats(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');

    try {
      await _ensureUserExists(userId);

      // PERFORMANCE OPTIMIZATION: Single query with CTEs to avoid N+1 pattern
      final result = await _connection!.execute(
        Sql.named('''
          WITH user_base AS (
            SELECT u.username, u.display_name, up.total_points, up.current_level, up.role::text,
                   s.current_streak, s.longest_streak
            FROM quester.users u
            JOIN quester.user_points up ON u.id = up.user_id
            LEFT JOIN quester.streaks s ON s.user_id = u.id
            WHERE u.id = @userId::uuid
          ),
          achievement_count AS (
            SELECT COUNT(*) as total_achievements
            FROM quester.user_achievements
            WHERE user_id = @userId::uuid
          ),
          leaderboard_positions AS (
            SELECT json_agg(
              json_build_object(
                'type', leaderboard_type,
                'rank', rank,
                'score', score
              ) ORDER BY leaderboard_type
            ) as positions
            FROM quester.leaderboards
            WHERE user_id = @userId::uuid
          ),
          recent_activity AS (
            SELECT json_agg(
              json_build_object(
                'type', activity_type,
                'points', points_earned,
                'description', description,
                'created_at', created_at
              ) ORDER BY created_at DESC
            ) as activities
            FROM (
              SELECT activity_type, points_earned, description, created_at
              FROM quester.activity_log
              WHERE user_id = @userId::uuid
              ORDER BY created_at DESC
              LIMIT 10
            ) recent
          )
          SELECT ub.*, ac.total_achievements, lp.positions, ra.activities
          FROM user_base ub
          CROSS JOIN achievement_count ac
          CROSS JOIN leaderboard_positions lp
          CROSS JOIN recent_activity ra
        '''),
        parameters: {'userId': userId},
      );

      if (result.isEmpty) return null;

      final row = result.first;

      // Parse JSON aggregated data from the optimized query
      final leaderboardPositions = row[8] != null
        ? (jsonDecode(row[8].toString()) as List<dynamic>?)
            ?.cast<Map<String, dynamic>>()
            .fold<Map<String, Map<String, dynamic>>>(
              {},
              (map, pos) => map..[pos['type']] = {
                'rank': pos['rank'],
                'score': pos['score'],
              }
            ) ?? <String, Map<String, dynamic>>{}
        : <String, Map<String, dynamic>>{};

      final recentActivity = row[9] != null
        ? (jsonDecode(row[9].toString()) as List<dynamic>?)
            ?.cast<Map<String, dynamic>>()
            .map((activity) => {
              'activity_type': activity['type'],
              'points_earned': activity['points'],
              'description': activity['description'],
              'created_at': activity['created_at'],
            }).toList() ?? <Map<String, dynamic>>[]
        : <Map<String, dynamic>>[];

      return {
        'user_id': userId,
        'username': row[0].toString(),
        'display_name': row[1]?.toString(),
        'total_points': row[2] as int,
        'current_level': row[3] as int,
        'role': row[4].toString(),
        'current_streak': row[5] ?? 0,
        'longest_streak': row[6] ?? 0,
        'achievement_count': row[7] as int,
        'leaderboard_positions': leaderboardPositions,
        'recent_activity': recentActivity,
      };
    } catch (e) {
      print('❌ Error getting user stats: $e');
      return null;
    }
  }
  
  /// Get all available achievements
  Future<List<Map<String, dynamic>>> getAllAchievements() async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute('''
        SELECT id, name, description, category::text, rarity::text, points_reward, icon_url, requirements
        FROM quester.achievements
        ORDER BY category, rarity, points_reward
      ''');
      
      return result.map((row) => {
        'id': row[0].toString(),
        'name': row[1].toString(),
        'description': row[2].toString(),
        'category': row[3].toString(), // Convert enum to string
        'rarity': row[4].toString(), // Convert enum to string
        'points_reward': row[5] as int,
        'icon_url': row[6]?.toString(),
        'requirements': row[7] != null ? _parseJsonSafe(row[7].toString()) : {},
      }).toList();
    } catch (e) {
      print('❌ Error getting achievements: $e');
      return [];
    }
  }
  
  /// Get user's earned achievements
  Future<List<Map<String, dynamic>>> getUserAchievements(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      await _ensureUserExists(userId);
      
      final result = await _connection!.execute(
        Sql.named('''
          SELECT a.id, a.name, a.description, a.category::text, a.rarity::text, 
                 ua.points_awarded, ua.earned_date
          FROM quester.user_achievements ua
          JOIN quester.achievements a ON a.id = ua.achievement_id
          WHERE ua.user_id = @userId::uuid
          ORDER BY ua.earned_date DESC
        '''),
        parameters: {'userId': userId},
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'name': row[1].toString(),
        'description': row[2].toString(),
        'category': row[3].toString(), // Convert enum to string
        'rarity': row[4].toString(), // Convert enum to string
        'points_awarded': row[5] as int,
        'earned_date': row[6].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting user achievements: $e');
      return [];
    }
  }
  
  /// Get leaderboard data
  Future<Map<String, dynamic>> getLeaderboard({String type = 'global_points', int limit = 50}) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT l.rank, l.score, u.username, u.display_name, up.role::text
          FROM quester.leaderboards l
          JOIN quester.users u ON u.id = l.user_id
          JOIN quester.user_points up ON up.user_id = l.user_id
          WHERE l.leaderboard_type = @type::quester.leaderboard_type
          ORDER BY l.rank
          LIMIT @limit
        '''),
        parameters: {'type': type, 'limit': limit},
      );
      
      final entries = result.map((row) => {
        'rank': row[0] as int,
        'score': row[1] as int,
        'username': row[2].toString(),
        'display_name': row[3]?.toString(),
        'role': row[4].toString(), // Convert enum to string
      }).toList();
      
      return {
        'leaderboard_type': type,
        'total_entries': entries.length,
        'entries': entries,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Error getting leaderboard: $e');
      return {
        'leaderboard_type': type,
        'total_entries': 0,
        'entries': <Map<String, dynamic>>[],
        'last_updated': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }
  
  /// Get all available rewards
  Future<List<Map<String, dynamic>>> getAllRewards() async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute('''
        SELECT id, name, description, type, point_cost, requirements, icon_url, is_available
        FROM quester.rewards
        WHERE is_available = true
        ORDER BY point_cost
      ''');
      
      return result.map((row) => {
        'id': row[0].toString(),
        'name': row[1].toString(),
        'description': row[2].toString(),
        'type': row[3].toString(), // Convert enum to string
        'point_cost': row[4] as int,
        'requirements': row[5] != null ? _parseJsonSafe(row[5].toString()) : {},
        'icon_url': row[6]?.toString(),
        'is_available': row[7] as bool,
      }).toList();
    } catch (e) {
      print('❌ Error getting rewards: $e');
      return [];
    }
  }
  
  /// Execute a raw SQL query with parameters
  Future<List<Map<String, dynamic>>> query(String sql, Map<String, dynamic> parameters) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named(sql),
        parameters: parameters,
      );
      
      return result.map((row) {
        final map = <String, dynamic>{};
        for (int i = 0; i < row.length; i++) {
          map['column_$i'] = row[i];
        }
        return map;
      }).toList();
    } catch (e) {
      print('❌ Error executing query: $e');
      rethrow;
    }
  }
  
  /// Insert data into a table
  Future<void> insert(String table, Map<String, dynamic> data) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final columns = data.keys.join(', ');
      final placeholders = data.keys.map((key) => '@$key').join(', ');
      final sql = 'INSERT INTO quester.$table ($columns) VALUES ($placeholders)';
      
      await _connection!.execute(
        Sql.named(sql),
        parameters: data,
      );
    } catch (e) {
      print('❌ Error inserting into $table: $e');
      rethrow;
    }
  }
  
  /// Delete data from a table
  Future<void> delete(String table, Map<String, dynamic> whereClause) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final whereConditions = whereClause.keys.map((key) => '$key = @$key').join(' AND ');
      final sql = 'DELETE FROM quester.$table WHERE $whereConditions';
      
      await _connection!.execute(
        Sql.named(sql),
        parameters: whereClause,
      );
    } catch (e) {
      print('❌ Error deleting from $table: $e');
      rethrow;
    }
  }

  /// Private helper methods
  
  Future<void> _ensureUserExists(String userId) async {
    await _connection!.runTx((ctx) async {
      await _ensureUserExistsInTx(ctx, userId);
    });
  }
  
  Future<void> _ensureUserExistsInTx(TxSession ctx, String userId) async {
    // Check if user exists
    final userResult = await ctx.execute(
      Sql.named('SELECT id FROM quester.users WHERE id = @userId::uuid'),
      parameters: {'userId': userId},
    );
    
    if (userResult.isEmpty) {
      // Create user with basic info
      await ctx.execute(
        Sql.named('''
          INSERT INTO quester.users (id, username, email, display_name)
          VALUES (@userId::uuid, @username, @email, @displayName)
        '''),
        parameters: {
          'userId': userId,
          'username': 'user_${userId.substring(0, 8)}',
          'email': '${userId.substring(0, 8)}@example.com',
          'displayName': 'User ${userId.substring(0, 8)}',
        },
      );
      
      // Create user points record
      await ctx.execute(
        Sql.named('''
          INSERT INTO quester.user_points (user_id, total_points, current_level, role, points_to_next_level)
          VALUES (@userId::uuid, 0, 1, 'novice', 500)
        '''),
        parameters: {'userId': userId},
      );
      
      // Create streak record
      await ctx.execute(
        Sql.named('''
          INSERT INTO quester.streaks (user_id, current_streak, longest_streak)
          VALUES (@userId::uuid, 0, 0)
        '''),
        parameters: {'userId': userId},
      );
      
      // Initialize leaderboard entries
      final leaderboardTypes = ['global_points', 'monthly_points', 'quest_completion', 'collaboration', 'streak_days'];
      for (final type in leaderboardTypes) {
        await ctx.execute(
          Sql.named('''
            INSERT INTO quester.leaderboards (user_id, leaderboard_type, score, rank)
            VALUES (@userId::uuid, @type, 0, 999999)
          '''),
          parameters: {'userId': userId, 'type': type},
        );
      }
    }
  }
  
  double _getRoleMultiplier(String role) {
    switch (role) {
      case 'novice': return 1.0;
      case 'explorer': return 1.2;
      case 'adventurer': return 1.4;
      case 'hero': return 1.6;
      case 'legend': return 1.8;
      case 'mythic': return 2.0;
      default: return 1.0;
    }
  }
  
  int _calculateLevel(int totalPoints) {
    if (totalPoints < 500) return 1;
    if (totalPoints < 2000) return 2;
    if (totalPoints < 5000) return 3;
    if (totalPoints < 10000) return 4;
    if (totalPoints < 25000) return 5;
    return 6 + ((totalPoints - 25000) ~/ 10000);
  }
  
  String _calculateRole(int totalPoints) {
    if (totalPoints < 500) return 'novice';
    if (totalPoints < 2000) return 'explorer';
    if (totalPoints < 5000) return 'adventurer';
    if (totalPoints < 10000) return 'hero';
    if (totalPoints < 25000) return 'legend';
    return 'mythic';
  }
  
  int _getPointsToNextLevel(int totalPoints, String role) {
    switch (role) {
      case 'novice': return 500 - totalPoints;
      case 'explorer': return 2000 - totalPoints;
      case 'adventurer': return 5000 - totalPoints;
      case 'hero': return 10000 - totalPoints;
      case 'legend': return 25000 - totalPoints;
      case 'mythic': return 0; // Max level
      default: return 0;
    }
  }
  
  Future<void> _updateStreak(TxSession ctx, String userId) async {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));
    
    final result = await ctx.execute(
      Sql.named('''
        SELECT current_streak, longest_streak, last_activity_date
        FROM quester.streaks
        WHERE user_id = @userId::uuid
      '''),
      parameters: {'userId': userId},
    );
    
    if (result.isNotEmpty) {
      final row = result.first;
      final currentStreak = row[0] as int;
      final longestStreak = row[1] as int;
      final lastActivity = DateTime.parse(row[2].toString());
      
      int newStreak = currentStreak;
      
      // Check if activity is within 24 hours
      if (lastActivity.isBefore(yesterday)) {
        // Streak broken, reset to 1
        newStreak = 1;
      } else if (lastActivity.day != now.day) {
        // New day, increment streak
        newStreak = currentStreak + 1;
      }
      
      final newLongest = newStreak > longestStreak ? newStreak : longestStreak;
      
      await ctx.execute(
        Sql.named('''
          UPDATE quester.streaks
          SET current_streak = @newStreak, longest_streak = @newLongest, last_activity_date = @now
          WHERE user_id = @userId::uuid
        '''),
        parameters: {
          'userId': userId,
          'newStreak': newStreak,
          'newLongest': newLongest,
          'now': now,
        },
      );
    }
  }
  
  Future<void> _updateLeaderboards(TxSession ctx, String userId, int newPoints) async {
    // Update global points leaderboard
    await ctx.execute(
      Sql.named('''
        UPDATE quester.leaderboards
        SET score = @newPoints, last_updated = CURRENT_TIMESTAMP
        WHERE user_id = @userId::uuid AND leaderboard_type = 'global_points'
      '''),
      parameters: {'userId': userId, 'newPoints': newPoints},
    );
    
    // Recalculate ranks (simplified - in production you'd want more efficient ranking)
    await ctx.execute('''
      UPDATE quester.leaderboards 
      SET rank = ranked.new_rank
      FROM (
        SELECT user_id, ROW_NUMBER() OVER (ORDER BY score DESC) as new_rank
        FROM quester.leaderboards
        WHERE leaderboard_type = 'global_points'
      ) ranked
      WHERE quester.leaderboards.user_id = ranked.user_id 
      AND quester.leaderboards.leaderboard_type = 'global_points'
    ''');
  }
  
  // =============================================================================
  // SECURITY OPERATIONS
  // =============================================================================

  /// Get SSO providers for an organization
  Future<List<Map<String, dynamic>>> getSSOProviders(String organizationId, {bool activeOnly = true}) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final whereClause = activeOnly ? 'AND is_active = true' : '';
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, organization_id, provider_name, provider_type, provider_config,
                 metadata_url, entity_id, is_active, is_primary, created_at, updated_at
          FROM sso_providers
          WHERE organization_id = @organizationId::uuid $whereClause
          ORDER BY is_primary DESC, provider_name
        '''),
        parameters: {'organizationId': organizationId},
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'organization_id': row[1].toString(),
        'provider_name': row[2].toString(),
        'provider_type': row[3].toString(),
        'provider_config': row[4] != null ? _parseJsonSafe(row[4].toString()) : {},
        'metadata_url': row[5]?.toString(),
        'entity_id': row[6]?.toString(),
        'is_active': row[7] as bool,
        'is_primary': row[8] as bool,
        'created_at': row[9].toString(),
        'updated_at': row[10].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting SSO providers: $e');
      return [];
    }
  }

  /// Create or update SSO provider
  Future<Map<String, dynamic>?> upsertSSOProvider(Map<String, dynamic> provider) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      return await _connection!.runTx((ctx) async {
        // If this provider is marked as primary, unset other primary providers
        if (provider['is_primary'] == true) {
          await ctx.execute(
            Sql.named('''
              UPDATE sso_providers 
              SET is_primary = false 
              WHERE organization_id = @organizationId::uuid AND is_primary = true
            '''),
            parameters: {'organizationId': provider['organization_id']},
          );
        }
        
        final result = await ctx.execute(
          Sql.named('''
            INSERT INTO sso_providers (
              id, organization_id, provider_name, provider_type, provider_config,
              metadata_url, entity_id, certificate, is_active, is_primary, created_by
            ) VALUES (
              COALESCE(@id::uuid, gen_random_uuid()), @organizationId::uuid, @providerName,
              @providerType, @providerConfig::jsonb, @metadataUrl, @entityId, @certificate,
              @isActive, @isPrimary, @createdBy::uuid
            )
            ON CONFLICT (organization_id, provider_name) DO UPDATE SET
              provider_type = EXCLUDED.provider_type,
              provider_config = EXCLUDED.provider_config,
              metadata_url = EXCLUDED.metadata_url,
              entity_id = EXCLUDED.entity_id,
              certificate = EXCLUDED.certificate,
              is_active = EXCLUDED.is_active,
              is_primary = EXCLUDED.is_primary,
              updated_at = NOW()
            RETURNING id, organization_id, provider_name, provider_type, is_active, is_primary
          '''),
          parameters: {
            'id': provider['id'],
            'organizationId': provider['organization_id'],
            'providerName': provider['provider_name'],
            'providerType': provider['provider_type'],
            'providerConfig': jsonEncode(provider['provider_config'] ?? {}),
            'metadataUrl': provider['metadata_url'],
            'entityId': provider['entity_id'],
            'certificate': provider['certificate'],
            'isActive': provider['is_active'] ?? true,
            'isPrimary': provider['is_primary'] ?? false,
            'createdBy': provider['created_by'],
          },
        );
        
        if (result.isNotEmpty) {
          final row = result.first;
          return {
            'id': row[0].toString(),
            'organization_id': row[1].toString(),
            'provider_name': row[2].toString(),
            'provider_type': row[3].toString(),
            'is_active': row[4] as bool,
            'is_primary': row[5] as bool,
            'success': true,
          };
        }
        return null;
      });
    } catch (e) {
      print('❌ Error upserting SSO provider: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get user SSO identity by external ID and provider
  Future<Map<String, dynamic>?> getUserSSOIdentity(String providerId, String externalId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT usi.id, usi.user_id, usi.provider_id, usi.external_id, usi.external_username,
                 usi.external_email, usi.provider_attributes, usi.last_login, usi.is_active,
                 ssp.provider_name, ssp.provider_type
          FROM user_sso_identities usi
          JOIN sso_providers ssp ON ssp.id = usi.provider_id
          WHERE usi.provider_id = @providerId::uuid AND usi.external_id = @externalId
            AND usi.is_active = true AND ssp.is_active = true
        '''),
        parameters: {'providerId': providerId, 'externalId': externalId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0].toString(),
        'user_id': row[1].toString(),
        'provider_id': row[2].toString(),
        'external_id': row[3].toString(),
        'external_username': row[4]?.toString(),
        'external_email': row[5]?.toString(),
        'provider_attributes': row[6] != null ? _parseJsonSafe(row[6].toString()) : {},
        'last_login': row[7]?.toString(),
        'is_active': row[8] as bool,
        'provider_name': row[9].toString(),
        'provider_type': row[10].toString(),
      };
    } catch (e) {
      print('❌ Error getting user SSO identity: $e');
      return null;
    }
  }

  /// Create or update user SSO identity
  Future<Map<String, dynamic>?> upsertUserSSOIdentity(Map<String, dynamic> identity) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO user_sso_identities (
            user_id, provider_id, external_id, external_username, external_email,
            provider_attributes, last_login, is_active
          ) VALUES (
            @userId::uuid, @providerId::uuid, @externalId, @externalUsername, @externalEmail,
            @providerAttributes::jsonb, @lastLogin::timestamp, @isActive
          )
          ON CONFLICT (user_id, provider_id) DO UPDATE SET
            external_id = EXCLUDED.external_id,
            external_username = EXCLUDED.external_username,
            external_email = EXCLUDED.external_email,
            provider_attributes = EXCLUDED.provider_attributes,
            last_login = EXCLUDED.last_login,
            is_active = EXCLUDED.is_active,
            updated_at = NOW()
          RETURNING id, user_id, provider_id, external_id
        '''),
        parameters: {
          'userId': identity['user_id'],
          'providerId': identity['provider_id'],
          'externalId': identity['external_id'],
          'externalUsername': identity['external_username'],
          'externalEmail': identity['external_email'],
          'providerAttributes': jsonEncode(identity['provider_attributes'] ?? {}),
          'lastLogin': identity['last_login'],
          'isActive': identity['is_active'] ?? true,
        },
      );
      
      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0].toString(),
          'user_id': row[1].toString(),
          'provider_id': row[2].toString(),
          'external_id': row[3].toString(),
          'success': true,
        };
      }
      return null;
    } catch (e) {
      print('❌ Error upserting user SSO identity: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get user MFA settings
  Future<Map<String, dynamic>?> getUserMFASettings(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, user_id, is_enabled, primary_method, trusted_devices, settings,
                 last_used, created_at, updated_at
          FROM user_mfa_settings
          WHERE user_id = @userId::uuid
        '''),
        parameters: {'userId': userId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0].toString(),
        'user_id': row[1].toString(),
        'is_enabled': row[2] as bool,
        'primary_method': row[3]?.toString(),
        'trusted_devices': row[4] != null ? _parseJsonSafe(row[4].toString()) : [],
        'settings': row[5] != null ? _parseJsonSafe(row[5].toString()) : {},
        'last_used': row[6]?.toString(),
        'created_at': row[7].toString(),
        'updated_at': row[8].toString(),
      };
    } catch (e) {
      print('❌ Error getting user MFA settings: $e');
      return null;
    }
  }

  /// Update user MFA settings
  Future<Map<String, dynamic>?> updateUserMFASettings(String userId, Map<String, dynamic> mfaSettings) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO user_mfa_settings (
            user_id, is_enabled, primary_method, totp_secret_encrypted, 
            phone_number_encrypted, backup_codes_encrypted, backup_codes_used,
            recovery_email_encrypted, trusted_devices, settings, last_used
          ) VALUES (
            @userId::uuid, @isEnabled, @primaryMethod, @totpSecret, @phoneNumber,
            @backupCodes::text[], @backupCodesUsed::int[], @recoveryEmail,
            @trustedDevices::jsonb, @settings::jsonb, @lastUsed::timestamp
          )
          ON CONFLICT (user_id) DO UPDATE SET
            is_enabled = EXCLUDED.is_enabled,
            primary_method = EXCLUDED.primary_method,
            totp_secret_encrypted = EXCLUDED.totp_secret_encrypted,
            phone_number_encrypted = EXCLUDED.phone_number_encrypted,
            backup_codes_encrypted = EXCLUDED.backup_codes_encrypted,
            backup_codes_used = EXCLUDED.backup_codes_used,
            recovery_email_encrypted = EXCLUDED.recovery_email_encrypted,
            trusted_devices = EXCLUDED.trusted_devices,
            settings = EXCLUDED.settings,
            last_used = EXCLUDED.last_used,
            updated_at = NOW()
          RETURNING id, is_enabled, primary_method
        '''),
        parameters: {
          'userId': userId,
          'isEnabled': mfaSettings['is_enabled'] ?? false,
          'primaryMethod': mfaSettings['primary_method'],
          'totpSecret': mfaSettings['totp_secret_encrypted'],
          'phoneNumber': mfaSettings['phone_number_encrypted'],
          'backupCodes': mfaSettings['backup_codes_encrypted'],
          'backupCodesUsed': mfaSettings['backup_codes_used'] ?? [],
          'recoveryEmail': mfaSettings['recovery_email_encrypted'],
          'trustedDevices': jsonEncode(mfaSettings['trusted_devices'] ?? []),
          'settings': jsonEncode(mfaSettings['settings'] ?? {}),
          'lastUsed': mfaSettings['last_used'],
        },
      );
      
      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0].toString(),
          'is_enabled': row[1] as bool,
          'primary_method': row[2]?.toString(),
          'success': true,
        };
      }
      return null;
    } catch (e) {
      print('❌ Error updating user MFA settings: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get organization security policy
  Future<Map<String, dynamic>?> getOrganizationSecurityPolicy(String organizationId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, organization_id, password_policy, mfa_policy, session_policy,
                 access_policy, audit_policy, compliance_settings, is_active,
                 created_at, updated_at
          FROM organization_security_policies
          WHERE organization_id = @organizationId::uuid AND is_active = true
        '''),
        parameters: {'organizationId': organizationId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0].toString(),
        'organization_id': row[1].toString(),
        'password_policy': row[2] != null ? _parseJsonSafe(row[2].toString()) : {},
        'mfa_policy': row[3] != null ? _parseJsonSafe(row[3].toString()) : {},
        'session_policy': row[4] != null ? _parseJsonSafe(row[4].toString()) : {},
        'access_policy': row[5] != null ? _parseJsonSafe(row[5].toString()) : {},
        'audit_policy': row[6] != null ? _parseJsonSafe(row[6].toString()) : {},
        'compliance_settings': row[7] != null ? _parseJsonSafe(row[7].toString()) : {},
        'is_active': row[8] as bool,
        'created_at': row[9].toString(),
        'updated_at': row[10].toString(),
      };
    } catch (e) {
      print('❌ Error getting organization security policy: $e');
      return null;
    }
  }

  /// Update organization security policy
  Future<Map<String, dynamic>?> updateOrganizationSecurityPolicy(String organizationId, Map<String, dynamic> policy) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO organization_security_policies (
            organization_id, password_policy, mfa_policy, session_policy,
            access_policy, audit_policy, compliance_settings, is_active, created_by
          ) VALUES (
            @organizationId::uuid, @passwordPolicy::jsonb, @mfaPolicy::jsonb,
            @sessionPolicy::jsonb, @accessPolicy::jsonb, @auditPolicy::jsonb,
            @complianceSettings::jsonb, @isActive, @createdBy::uuid
          )
          ON CONFLICT (organization_id) DO UPDATE SET
            password_policy = EXCLUDED.password_policy,
            mfa_policy = EXCLUDED.mfa_policy,
            session_policy = EXCLUDED.session_policy,
            access_policy = EXCLUDED.access_policy,
            audit_policy = EXCLUDED.audit_policy,
            compliance_settings = EXCLUDED.compliance_settings,
            is_active = EXCLUDED.is_active,
            updated_at = NOW()
          RETURNING id, organization_id, is_active
        '''),
        parameters: {
          'organizationId': organizationId,
          'passwordPolicy': jsonEncode(policy['password_policy'] ?? {}),
          'mfaPolicy': jsonEncode(policy['mfa_policy'] ?? {}),
          'sessionPolicy': jsonEncode(policy['session_policy'] ?? {}),
          'accessPolicy': jsonEncode(policy['access_policy'] ?? {}),
          'auditPolicy': jsonEncode(policy['audit_policy'] ?? {}),
          'complianceSettings': jsonEncode(policy['compliance_settings'] ?? {}),
          'isActive': policy['is_active'] ?? true,
          'createdBy': policy['created_by'],
        },
      );
      
      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0].toString(),
          'organization_id': row[1].toString(),
          'is_active': row[2] as bool,
          'success': true,
        };
      }
      return null;
    } catch (e) {
      print('❌ Error updating organization security policy: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Create security audit log entry
  Future<String?> createSecurityAuditLog(Map<String, dynamic> logEntry) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO security_audit_logs (
            organization_id, user_id, event_type, event_category, event_description,
            event_severity, ip_address, user_agent, device_fingerprint, geo_location,
            session_id, request_id, resource_type, resource_id, event_data,
            event_metadata, risk_score, is_anomaly
          ) VALUES (
            @organizationId::uuid, @userId::uuid, @eventType, @eventCategory,
            @eventDescription, @eventSeverity, @ipAddress::inet, @userAgent,
            @deviceFingerprint, @geoLocation::jsonb, @sessionId::uuid, @requestId,
            @resourceType, @resourceId, @eventData::jsonb, @eventMetadata::jsonb,
            @riskScore, @isAnomaly
          )
          RETURNING id
        '''),
        parameters: {
          'organizationId': logEntry['organization_id'],
          'userId': logEntry['user_id'],
          'eventType': logEntry['event_type'],
          'eventCategory': logEntry['event_category'],
          'eventDescription': logEntry['event_description'],
          'eventSeverity': logEntry['event_severity'] ?? 'medium',
          'ipAddress': logEntry['ip_address'],
          'userAgent': logEntry['user_agent'],
          'deviceFingerprint': logEntry['device_fingerprint'],
          'geoLocation': logEntry['geo_location'] != null ? jsonEncode(logEntry['geo_location']) : null,
          'sessionId': logEntry['session_id'],
          'requestId': logEntry['request_id'],
          'resourceType': logEntry['resource_type'],
          'resourceId': logEntry['resource_id'],
          'eventData': logEntry['event_data'] != null ? jsonEncode(logEntry['event_data']) : null,
          'eventMetadata': logEntry['event_metadata'] != null ? jsonEncode(logEntry['event_metadata']) : null,
          'riskScore': logEntry['risk_score'] ?? 0,
          'isAnomaly': logEntry['is_anomaly'] ?? false,
        },
      );
      
      if (result.isNotEmpty) {
        return result.first[0].toString();
      }
      return null;
    } catch (e) {
      print('❌ Error creating security audit log: $e');
      return null;
    }
  }

  /// Get security audit logs with filtering
  Future<List<Map<String, dynamic>>> getSecurityAuditLogs({
    String? organizationId,
    String? userId,
    String? eventCategory,
    String? eventSeverity,
    bool? isAnomaly,
    int? minRiskScore,
    DateTime? fromDate,
    DateTime? toDate,
    int limit = 100,
    int offset = 0,
  }) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      var whereConditions = <String>[];
      var parameters = <String, dynamic>{};
      
      if (organizationId != null) {
        whereConditions.add('organization_id = @organizationId::uuid');
        parameters['organizationId'] = organizationId;
      }
      
      if (userId != null) {
        whereConditions.add('user_id = @userId::uuid');
        parameters['userId'] = userId;
      }
      
      if (eventCategory != null) {
        whereConditions.add('event_category = @eventCategory');
        parameters['eventCategory'] = eventCategory;
      }
      
      if (eventSeverity != null) {
        whereConditions.add('event_severity = @eventSeverity');
        parameters['eventSeverity'] = eventSeverity;
      }
      
      if (isAnomaly != null) {
        whereConditions.add('is_anomaly = @isAnomaly');
        parameters['isAnomaly'] = isAnomaly;
      }
      
      if (minRiskScore != null) {
        whereConditions.add('risk_score >= @minRiskScore');
        parameters['minRiskScore'] = minRiskScore;
      }
      
      if (fromDate != null) {
        whereConditions.add('created_at >= @fromDate::timestamp');
        parameters['fromDate'] = fromDate.toIso8601String();
      }
      
      if (toDate != null) {
        whereConditions.add('created_at <= @toDate::timestamp');
        parameters['toDate'] = toDate.toIso8601String();
      }
      
      final whereClause = whereConditions.isNotEmpty ? 'WHERE ${whereConditions.join(' AND ')}' : '';
      parameters['limit'] = limit;
      parameters['offset'] = offset;
      
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, organization_id, user_id, event_type, event_category, event_description,
                 event_severity, ip_address, user_agent, device_fingerprint, geo_location,
                 session_id, request_id, resource_type, resource_id, event_data,
                 event_metadata, risk_score, is_anomaly, created_at
          FROM security_audit_logs
          $whereClause
          ORDER BY created_at DESC
          LIMIT @limit OFFSET @offset
        '''),
        parameters: parameters,
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'organization_id': row[1]?.toString(),
        'user_id': row[2]?.toString(),
        'event_type': row[3].toString(),
        'event_category': row[4].toString(),
        'event_description': row[5].toString(),
        'event_severity': row[6].toString(),
        'ip_address': row[7]?.toString(),
        'user_agent': row[8]?.toString(),
        'device_fingerprint': row[9]?.toString(),
        'geo_location': row[10] != null ? _parseJsonSafe(row[10].toString()) : null,
        'session_id': row[11]?.toString(),
        'request_id': row[12]?.toString(),
        'resource_type': row[13]?.toString(),
        'resource_id': row[14]?.toString(),
        'event_data': row[15] != null ? _parseJsonSafe(row[15].toString()) : null,
        'event_metadata': row[16] != null ? _parseJsonSafe(row[16].toString()) : null,
        'risk_score': row[17] as int,
        'is_anomaly': row[18] as bool,
        'created_at': row[19].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting security audit logs: $e');
      return [];
    }
  }

  /// Create or update user session
  Future<Map<String, dynamic>?> upsertUserSession(Map<String, dynamic> session) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO user_sessions_enhanced (
            id, user_id, organization_id, session_token_hash, refresh_token_hash,
            device_fingerprint, device_info, ip_address, user_agent, geo_location,
            is_trusted_device, login_method, mfa_verified, last_activity,
            expires_at, absolute_expires_at, is_active
          ) VALUES (
            COALESCE(@id::uuid, gen_random_uuid()), @userId::uuid, @organizationId::uuid,
            @sessionTokenHash, @refreshTokenHash, @deviceFingerprint, @deviceInfo::jsonb,
            @ipAddress::inet, @userAgent, @geoLocation::jsonb, @isTrustedDevice,
            @loginMethod, @mfaVerified, @lastActivity::timestamp, @expiresAt::timestamp,
            @absoluteExpiresAt::timestamp, @isActive
          )
          ON CONFLICT (session_token_hash) DO UPDATE SET
            last_activity = EXCLUDED.last_activity,
            expires_at = EXCLUDED.expires_at,
            is_active = EXCLUDED.is_active,
            updated_at = NOW()
          RETURNING id, user_id, session_token_hash, expires_at, is_active
        '''),
        parameters: {
          'id': session['id'],
          'userId': session['user_id'],
          'organizationId': session['organization_id'],
          'sessionTokenHash': session['session_token_hash'],
          'refreshTokenHash': session['refresh_token_hash'],
          'deviceFingerprint': session['device_fingerprint'],
          'deviceInfo': session['device_info'] != null ? jsonEncode(session['device_info']) : null,
          'ipAddress': session['ip_address'],
          'userAgent': session['user_agent'],
          'geoLocation': session['geo_location'] != null ? jsonEncode(session['geo_location']) : null,
          'isTrustedDevice': session['is_trusted_device'] ?? false,
          'loginMethod': session['login_method'],
          'mfaVerified': session['mfa_verified'] ?? false,
          'lastActivity': session['last_activity'],
          'expiresAt': session['expires_at'],
          'absoluteExpiresAt': session['absolute_expires_at'],
          'isActive': session['is_active'] ?? true,
        },
      );
      
      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0].toString(),
          'user_id': row[1].toString(),
          'session_token_hash': row[2].toString(),
          'expires_at': row[3].toString(),
          'is_active': row[4] as bool,
          'success': true,
        };
      }
      return null;
    } catch (e) {
      print('❌ Error upserting user session: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get active user sessions
  Future<List<Map<String, dynamic>>> getActiveUserSessions(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, session_token_hash, device_fingerprint, device_info, ip_address,
                 user_agent, geo_location, is_trusted_device, login_method, mfa_verified,
                 last_activity, expires_at, absolute_expires_at, created_at
          FROM user_sessions_enhanced
          WHERE user_id = @userId::uuid AND is_active = true 
            AND expires_at > NOW() AND absolute_expires_at > NOW()
          ORDER BY last_activity DESC
        '''),
        parameters: {'userId': userId},
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'session_token_hash': row[1].toString(),
        'device_fingerprint': row[2]?.toString(),
        'device_info': row[3] != null ? _parseJsonSafe(row[3].toString()) : null,
        'ip_address': row[4].toString(),
        'user_agent': row[5]?.toString(),
        'geo_location': row[6] != null ? _parseJsonSafe(row[6].toString()) : null,
        'is_trusted_device': row[7] as bool,
        'login_method': row[8]?.toString(),
        'mfa_verified': row[9] as bool,
        'last_activity': row[10].toString(),
        'expires_at': row[11].toString(),
        'absolute_expires_at': row[12].toString(),
        'created_at': row[13].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting active user sessions: $e');
      return [];
    }
  }

  /// Invalidate user session
  Future<bool> invalidateUserSession(String sessionTokenHash, String reason) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          UPDATE user_sessions_enhanced
          SET is_active = false, logout_reason = @reason, updated_at = NOW()
          WHERE session_token_hash = @sessionTokenHash AND is_active = true
          RETURNING id
        '''),
        parameters: {'sessionTokenHash': sessionTokenHash, 'reason': reason},
      );
      
      return result.isNotEmpty;
    } catch (e) {
      print('❌ Error invalidating user session: $e');
      return false;
    }
  }

  /// Get or create IP access control entry
  Future<Map<String, dynamic>?> upsertIPAccessControl(Map<String, dynamic> ipControl) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO ip_access_control (
            organization_id, user_id, ip_address, ip_range, access_type, rule_type,
            description, geo_location, access_count, is_suspicious, risk_score,
            is_active, created_by
          ) VALUES (
            @organizationId::uuid, @userId::uuid, @ipAddress::inet, @ipRange::cidr,
            @accessType, @ruleType, @description, @geoLocation::jsonb, @accessCount,
            @isSuspicious, @riskScore, @isActive, @createdBy::uuid
          )
          ON CONFLICT (ip_address) DO UPDATE SET
            last_seen = NOW(),
            access_count = ip_access_control.access_count + 1,
            is_suspicious = EXCLUDED.is_suspicious,
            risk_score = EXCLUDED.risk_score,
            geo_location = EXCLUDED.geo_location,
            updated_at = NOW()
          RETURNING id, ip_address, access_type, risk_score, is_suspicious
        '''),
        parameters: {
          'organizationId': ipControl['organization_id'],
          'userId': ipControl['user_id'],
          'ipAddress': ipControl['ip_address'],
          'ipRange': ipControl['ip_range'],
          'accessType': ipControl['access_type'] ?? 'monitor',
          'ruleType': ipControl['rule_type'] ?? 'automatic',
          'description': ipControl['description'],
          'geoLocation': ipControl['geo_location'] != null ? jsonEncode(ipControl['geo_location']) : null,
          'accessCount': ipControl['access_count'] ?? 1,
          'isSuspicious': ipControl['is_suspicious'] ?? false,
          'riskScore': ipControl['risk_score'] ?? 0,
          'isActive': ipControl['is_active'] ?? true,
          'createdBy': ipControl['created_by'],
        },
      );
      
      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0].toString(),
          'ip_address': row[1].toString(),
          'access_type': row[2].toString(),
          'risk_score': row[3] as int,
          'is_suspicious': row[4] as bool,
          'success': true,
        };
      }
      return null;
    } catch (e) {
      print('❌ Error upserting IP access control: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get IP access control rules for organization
  Future<List<Map<String, dynamic>>> getIPAccessControlRules(String organizationId, {String? accessType}) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final whereClause = accessType != null ? 'AND access_type = @accessType' : '';
      final parameters = {'organizationId': organizationId};
      if (accessType != null) parameters['accessType'] = accessType;
      
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, ip_address, ip_range, access_type, rule_type, description,
                 geo_location, first_seen, last_seen, access_count, is_suspicious,
                 risk_score, is_active, created_at
          FROM ip_access_control
          WHERE organization_id = @organizationId::uuid AND is_active = true $whereClause
          ORDER BY risk_score DESC, last_seen DESC
        '''),
        parameters: parameters,
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'ip_address': row[1].toString(),
        'ip_range': row[2]?.toString(),
        'access_type': row[3].toString(),
        'rule_type': row[4].toString(),
        'description': row[5]?.toString(),
        'geo_location': row[6] != null ? _parseJsonSafe(row[6].toString()) : null,
        'first_seen': row[7].toString(),
        'last_seen': row[8].toString(),
        'access_count': row[9] as int,
        'is_suspicious': row[10] as bool,
        'risk_score': row[11] as int,
        'is_active': row[12] as bool,
        'created_at': row[13].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting IP access control rules: $e');
      return [];
    }
  }

  /// Get security dashboard data for organization
  Future<Map<String, dynamic>?> getSecurityDashboard(String organizationId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT organization_id, organization_name, total_users, mfa_enabled_users,
                 sso_providers_count, active_sso_providers, sso_linked_users,
                 active_sessions, recent_suspicious_events, users_with_failed_attempts,
                 currently_locked_users, avg_ip_risk_score, suspicious_ips,
                 last_security_event, critical_events_24h
          FROM security_dashboard_view
          WHERE organization_id = @organizationId::uuid
        '''),
        parameters: {'organizationId': organizationId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'organization_id': row[0].toString(),
        'organization_name': row[1].toString(),
        'total_users': row[2] as int,
        'mfa_enabled_users': row[3] as int,
        'sso_providers_count': row[4] as int,
        'active_sso_providers': row[5] as int,
        'sso_linked_users': row[6] as int,
        'active_sessions': row[7] as int,
        'recent_suspicious_events': row[8] as int,
        'users_with_failed_attempts': row[9] as int,
        'currently_locked_users': row[10] as int,
        'avg_ip_risk_score': row[11] as int,
        'suspicious_ips': row[12] as int,
        'last_security_event': row[13]?.toString(),
        'critical_events_24h': row[14] as int,
        'mfa_adoption_rate': (row[2] as int?) != null && (row[2] as int) > 0 ? ((row[3] as int) / (row[2] as int) * 100).round() : 0,
        'sso_adoption_rate': (row[2] as int?) != null && (row[2] as int) > 0 ? ((row[6] as int) / (row[2] as int) * 100).round() : 0,
      };
    } catch (e) {
      print('❌ Error getting security dashboard: $e');
      return null;
    }
  }

  /// Cleanup expired sessions
  Future<int> cleanupExpiredSessions() async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute('SELECT cleanup_expired_sessions()');
      return result.isNotEmpty ? result.first[0] as int : 0;
    } catch (e) {
      print('❌ Error cleaning up expired sessions: $e');
      return 0;
    }
  }

  /// Execute SQL query with parameters
  Future<Result> execute(String sql, {Map<String, dynamic>? parameters}) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      if (parameters != null && parameters.isNotEmpty) {
        return await _connection!.execute(
          Sql.named(sql), 
          parameters: parameters,
        );
      } else {
        return await _connection!.execute(sql);
      }
    } catch (e) {
      print('❌ Error executing SQL: $e');
      print('🔍 SQL: $sql');
      print('🔍 Parameters: $parameters');
      rethrow;
    }
  }

  /// Create a new user
  Future<String> createUser(Map<String, dynamic> userData) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.users (username, email, display_name, avatar_url)
          VALUES (@username, @email, @displayName, @avatarUrl)
          RETURNING id
        '''),
        parameters: {
          'username': userData['username'] ?? userData['email'],
          'email': userData['email'],
          'displayName': userData['displayName'] ?? userData['username'],
          'avatarUrl': userData['avatarUrl'],
        },
      );
      
      if (result.isEmpty) {
        throw Exception('Failed to create user');
      }
      
      final userId = result.first[0].toString();
      
      // Initialize user points
      await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.user_points (user_id, total_points, current_level, role)
          VALUES (@userId::uuid, 0, 1, 'novice')
        '''),
        parameters: {'userId': userId},
      );
      
      return userId;
    } catch (e) {
      print('❌ Error creating user: $e');
      rethrow;
    }
  }

  /// Update user data
  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final setParts = <String>[];
      final parameters = <String, dynamic>{'userId': userId};
      
      if (updates.containsKey('username')) {
        setParts.add('username = @username');
        parameters['username'] = updates['username'];
      }
      if (updates.containsKey('email')) {
        setParts.add('email = @email');
        parameters['email'] = updates['email'];
      }
      if (updates.containsKey('displayName')) {
        setParts.add('display_name = @displayName');
        parameters['displayName'] = updates['displayName'];
      }
      if (updates.containsKey('avatarUrl')) {
        setParts.add('avatar_url = @avatarUrl');
        parameters['avatarUrl'] = updates['avatarUrl'];
      }
      
      if (setParts.isEmpty) return;
      
      await _connection!.execute(
        Sql.named('''
          UPDATE quester.users 
          SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP
          WHERE id = @userId::uuid
        '''),
        parameters: parameters,
      );
    } catch (e) {
      print('❌ Error updating user: $e');
      rethrow;
    }
  }

  /// Get user by email
  Future<List<Map<String, dynamic>>> getUserByEmail(String email, [String? organizationId]) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, username, email, display_name, avatar_url, created_at, updated_at
          FROM quester.users 
          WHERE email = @email AND is_active = true
        '''),
        parameters: {'email': email},
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'username': row[1].toString(),
        'email': row[2].toString(),
        'display_name': row[3]?.toString(),
        'avatar_url': row[4]?.toString(),
        'created_at': row[5].toString(),
        'updated_at': row[6].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting user by email: $e');
      return [];
    }
  }

  /// Get user by ID
  Future<Map<String, dynamic>?> getUser(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, username, email, display_name, avatar_url, created_at, updated_at
          FROM quester.users 
          WHERE id = @userId::uuid AND is_active = true
        '''),
        parameters: {'userId': userId},
      );
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0].toString(),
        'username': row[1].toString(),
        'email': row[2].toString(),
        'display_name': row[3]?.toString(),
        'avatar_url': row[4]?.toString(),
        'created_at': row[5].toString(),
        'updated_at': row[6].toString(),
      };
    } catch (e) {
      print('❌ Error getting user: $e');
      return null;
    }
  }

  /// Create authentication session
  Future<String> createSession(Map<String, dynamic> sessionData) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.user_sessions_enhanced (
            user_id, session_token, ip_address, user_agent, expires_at
          ) VALUES (
            @userId::uuid, @sessionToken, @ipAddress, @userAgent, @expiresAt::timestamptz
          ) RETURNING id
        '''),
        parameters: {
          'userId': sessionData['userId'],
          'sessionToken': sessionData['sessionToken'],
          'ipAddress': sessionData['ipAddress'],
          'userAgent': sessionData['userAgent'],
          'expiresAt': sessionData['expiresAt'],
        },
      );
      
      return result.first[0].toString();
    } catch (e) {
      print('❌ Error creating session: $e');
      rethrow;
    }
  }

  /// Create SSO identity for user
  Future<String> createUserSSOIdentity(Map<String, dynamic> identityData) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.user_sso_identities (
            user_id, provider_id, provider_user_id, provider_data
          ) VALUES (
            @userId::uuid, @providerId::uuid, @providerUserId, @providerData::jsonb
          ) RETURNING id
        '''),
        parameters: {
          'userId': identityData['userId'],
          'providerId': identityData['providerId'],
          'providerUserId': identityData['providerUserId'],
          'providerData': jsonEncode(identityData['providerData'] ?? {}),
        },
      );
      
      return result.first[0].toString();
    } catch (e) {
      print('❌ Error creating SSO identity: $e');
      rethrow;
    }
  }

  /// Update SSO identity
  Future<void> updateUserSSOIdentity(String identityId, Map<String, dynamic> updates) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final setParts = <String>[];
      final parameters = <String, dynamic>{'identityId': identityId};
      
      if (updates.containsKey('providerUserId')) {
        setParts.add('provider_user_id = @providerUserId');
        parameters['providerUserId'] = updates['providerUserId'];
      }
      if (updates.containsKey('providerData')) {
        setParts.add('provider_data = @providerData::jsonb');
        parameters['providerData'] = jsonEncode(updates['providerData']);
      }
      if (updates.containsKey('lastLoginAt')) {
        setParts.add('last_login_at = @lastLoginAt::timestamptz');
        parameters['lastLoginAt'] = updates['lastLoginAt'];
      }
      
      if (setParts.isEmpty) return;
      
      await _connection!.execute(
        Sql.named('''
          UPDATE quester.user_sso_identities 
          SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP
          WHERE id = @identityId::uuid
        '''),
        parameters: parameters,
      );
    } catch (e) {
      print('❌ Error updating SSO identity: $e');
      rethrow;
    }
  }

  /// Get user SSO identities
  Future<List<Map<String, dynamic>>> getUserSSOIdentities(String userId) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT id, provider_id, provider_user_id, provider_data, last_login_at
          FROM quester.user_sso_identities 
          WHERE user_id = @userId::uuid
        '''),
        parameters: {'userId': userId},
      );
      
      return result.map((row) => {
        'id': row[0].toString(),
        'provider_id': row[1].toString(),
        'provider_user_id': row[2].toString(),
        'provider_data': row[3] != null ? _parseJsonSafe(row[3].toString()) : {},
        'last_login_at': row[4]?.toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting SSO identities: $e');
      return [];
    }
  }

  /// Create MFA settings for user
  Future<String> createUserMFASettings(Map<String, dynamic> mfaData) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          INSERT INTO quester.user_mfa_settings (
            user_id, totp_enabled, phone_enabled, email_enabled, backup_codes_enabled
          ) VALUES (
            @userId::uuid, @totpEnabled, @phoneEnabled, @emailEnabled, @backupCodesEnabled
          ) RETURNING id
        '''),
        parameters: {
          'userId': mfaData['userId'],
          'totpEnabled': mfaData['totpEnabled'] ?? false,
          'phoneEnabled': mfaData['phoneEnabled'] ?? false,
          'emailEnabled': mfaData['emailEnabled'] ?? false,
          'backupCodesEnabled': mfaData['backupCodesEnabled'] ?? false,
        },
      );
      
      return result.first[0].toString();
    } catch (e) {
      print('❌ Error creating MFA settings: $e');
      rethrow;
    }
  }

  /// Get global activity feed (PERFORMANCE OPTIMIZED)
  Future<List<Map<String, dynamic>>> getGlobalActivity({int limit = 50}) async {
    if (!_isConnected) throw Exception('Database not connected');

    try {
      // PERFORMANCE OPTIMIZATION: Use covering index and limit early
      final result = await _connection!.execute(
        Sql.named('''
          SELECT
            al.user_id::text,
            u.username,
            u.display_name,
            al.activity_type::text,
            al.points_earned,
            al.description,
            al.created_at,
            up.current_level,
            up.role::text
          FROM (
            SELECT user_id, activity_type, points_earned, description, created_at
            FROM quester.activity_log
            ORDER BY created_at DESC
            LIMIT @limit
          ) al
          JOIN quester.users u ON u.id = al.user_id AND u.is_active = true
          JOIN quester.user_points up ON up.user_id = al.user_id
          ORDER BY al.created_at DESC
        '''),
        parameters: {'limit': limit},
      );
      
      return result.map((row) => {
        'user_id': row[0].toString(),
        'username': row[1].toString(),
        'display_name': row[2]?.toString(),
        'activity_type': row[3].toString(),
        'points_earned': row[4] as int,
        'description': row[5].toString(),
        'created_at': row[6].toString(),
        'user_level': row[7] as int,
        'user_role': row[8].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting global activity: $e');
      return [];
    }
  }
  
  /// Get user-specific activity feed
  Future<List<Map<String, dynamic>>> getUserActivity(String userId, {int limit = 50}) async {
    if (!_isConnected) throw Exception('Database not connected');
    
    try {
      final result = await _connection!.execute(
        Sql.named('''
          SELECT 
            activity_type::text,
            points_earned,
            description,
            created_at
          FROM quester.activity_log
          WHERE user_id = @userId::uuid
          ORDER BY created_at DESC
          LIMIT @limit
        '''),
        parameters: {
          'userId': userId,
          'limit': limit,
        },
      );
      
      return result.map((row) => {
        'activity_type': row[0].toString(),
        'points_earned': row[1] as int,
        'description': row[2].toString(),
        'created_at': row[3].toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting user activity for $userId: $e');
      return [];
    }
  }

  /// Helper method to safely parse JSON/JSONB from PostgreSQL
  Map<String, dynamic> _parseJsonSafe(String jsonString) {
    try {
      // PostgreSQL JSONB may return without quotes on keys and values, convert to proper JSON
      var cleaned = jsonString;
      
      // First, handle unquoted keys
      cleaned = cleaned.replaceAllMapped(
        RegExp(r'(\w+):'), 
        (match) => '"${match.group(1)}":',
      );
      
      // Handle unquoted string values (but not numbers)
      cleaned = cleaned.replaceAllMapped(
        RegExp(r':\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([,}])'), 
        (match) => ': "${match.group(1)}"${match.group(2)}',
      );
      
      return jsonDecode(cleaned) as Map<String, dynamic>;
    } catch (e) {
      // Silent fallback for malformed JSON
      return <String, dynamic>{};
    }
  }

  /// Public getter for connection status
  bool get isConnected => _isConnected;

  /// Public getter for database connection
  Connection? get connection => _connection;
}
