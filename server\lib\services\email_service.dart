import 'dart:convert';
import 'package:http/http.dart' as http;
import 'logging_service.dart';

/// Email service for sending various types of emails
/// Supports multiple providers (SendGrid, AWS SES, SMTP)
class EmailService {
  final String _provider;
  final Map<String, String> _config;
  
  EmailService({
    String provider = 'mock',
    Map<String, String> config = const {},
  }) : _provider = provider,
       _config = config;

  /// Send verification email
  Future<bool> sendVerificationEmail(String email, String token) async {
    final subject = 'Verify Your Email Address';
    final body = '''
    <html>
      <body>
        <h2>Email Verification</h2>
        <p>Please click the link below to verify your email address:</p>
        <a href="${_config['baseUrl'] ?? 'http://localhost:8080'}/auth/verify-email?token=$token">
          Verify Email
        </a>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email, String token) async {
    final subject = 'Reset Your Password';
    final body = '''
    <html>
      <body>
        <h2>Password Reset</h2>
        <p>You requested a password reset. Click the link below to reset your password:</p>
        <a href="${_config['baseUrl'] ?? 'http://localhost:8080'}/auth/reset-password?token=$token">
          Reset Password
        </a>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this reset, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send MFA code via email
  Future<bool> sendMFACode(String email, String code) async {
    final subject = 'Your Verification Code';
    final body = '''
    <html>
      <body>
        <h2>Verification Code</h2>
        <p>Your verification code is:</p>
        <h1 style="color: #007bff; font-size: 32px; letter-spacing: 4px;">$code</h1>
        <p>This code will expire in 5 minutes.</p>
        <p>If you didn't request this code, please ignore this email.</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Send welcome email
  Future<bool> sendWelcomeEmail(String email, String name) async {
    final subject = 'Welcome to Quester!';
    final body = '''
    <html>
      <body>
        <h2>Welcome to Quester, $name!</h2>
        <p>Thank you for joining our gamified productivity platform.</p>
        <p>Get started by:</p>
        <ul>
          <li>Creating your first quest</li>
          <li>Setting up your profile</li>
          <li>Exploring the leaderboards</li>
        </ul>
        <p>Happy questing!</p>
      </body>
    </html>
    ''';
    
    return await _sendEmail(email, subject, body);
  }

  /// Internal method to send email based on provider
  Future<bool> _sendEmail(String to, String subject, String body) async {
    try {
      switch (_provider.toLowerCase()) {
        case 'sendgrid':
          return await _sendViaSendGrid(to, subject, body);
        case 'ses':
        case 'aws':
          return await _sendViaAWSSES(to, subject, body);
        case 'smtp':
          return await _sendViaSMTP(to, subject, body);
        case 'mock':
          return await _sendViaMock(to, subject, body);
        default:
          LoggingService.error('Unknown email provider: $_provider');
          return false;
      }
    } catch (e) {
      LoggingService.error('Failed to send email to $to: $e');
      return false;
    }
  }

  /// Send email via SendGrid
  Future<bool> _sendViaSendGrid(String to, String subject, String body) async {
    final apiKey = _config['sendgridApiKey'];
    if (apiKey == null) {
      LoggingService.error('SendGrid API key not configured');
      return false;
    }

    final response = await http.post(
      Uri.parse('https://api.sendgrid.com/v3/mail/send'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'personalizations': [
          {
            'to': [{'email': to}],
            'subject': subject,
          }
        ],
        'from': {
          'email': _config['fromEmail'] ?? '<EMAIL>',
          'name': _config['fromName'] ?? 'Quester',
        },
        'content': [
          {
            'type': 'text/html',
            'value': body,
          }
        ],
      }),
    );

    if (response.statusCode == 202) {
      LoggingService.email('Email sent via SendGrid to $to', recipient: to);
      return true;
    } else {
      LoggingService.error('SendGrid API error: ${response.statusCode} - ${response.body}');
      return false;
    }
  }

  /// Send email via AWS SES
  Future<bool> _sendViaAWSSES(String to, String subject, String body) async {
    // TODO: Implement AWS SES integration
    LoggingService.info('AWS SES integration not yet implemented');
    return await _sendViaMock(to, subject, body);
  }

  /// Send email via SMTP
  Future<bool> _sendViaSMTP(String to, String subject, String body) async {
    // TODO: Implement SMTP integration
    LoggingService.info('SMTP integration not yet implemented');
    return await _sendViaMock(to, subject, body);
  }

  /// Mock email sending for development/testing
  Future<bool> _sendViaMock(String to, String subject, String body) async {
    LoggingService.email(
      'Mock email sent - To: $to, Subject: $subject (${body.length} chars)',
      recipient: to,
    );
    
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 100));
    return true;
  }
}
