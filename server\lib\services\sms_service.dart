import 'dart:convert';
import 'package:http/http.dart' as http;
import 'logging_service.dart';

/// SMS service for sending text messages
/// Supports multiple providers (Twilio, AWS SNS, etc.)
class SMSService {
  final String _provider;
  final Map<String, String> _config;
  
  SMSService({
    String provider = 'mock',
    Map<String, String> config = const {},
  }) : _provider = provider,
       _config = config;

  /// Send MFA code via SMS
  Future<bool> sendMFACode(String phoneNumber, String code) async {
    final message = 'Your Quester verification code is: $code. This code expires in 5 minutes.';
    return await _sendSMS(phoneNumber, message);
  }

  /// Send verification code via SMS
  Future<bool> sendVerificationCode(String phoneNumber, String code) async {
    final message = 'Your Quester phone verification code is: $code. Enter this code to verify your phone number.';
    return await _sendSMS(phoneNumber, message);
  }

  /// Send password reset notification via SMS
  Future<bool> sendPasswordResetNotification(String phoneNumber) async {
    final message = 'A password reset was requested for your Quester account. If this wasn\'t you, please contact support.';
    return await _sendSMS(phoneNumber, message);
  }

  /// Send security alert via SMS
  Future<bool> sendSecurityAlert(String phoneNumber, String alertType) async {
    final message = 'Security Alert: $alertType detected on your Quester account. Please check your email for details.';
    return await _sendSMS(phoneNumber, message);
  }

  /// Internal method to send SMS based on provider
  Future<bool> _sendSMS(String phoneNumber, String message) async {
    try {
      switch (_provider.toLowerCase()) {
        case 'twilio':
          return await _sendViaTwilio(phoneNumber, message);
        case 'sns':
        case 'aws':
          return await _sendViaAWSSNS(phoneNumber, message);
        case 'mock':
        default:
          return await _sendViaMock(phoneNumber, message);
      }
    } catch (e) {
      LoggingService.error('Failed to send SMS to $phoneNumber: $e');
      return false;
    }
  }

  /// Send SMS via Twilio
  Future<bool> _sendViaTwilio(String phoneNumber, String message) async {
    final accountSid = _config['twilioAccountSid'];
    final authToken = _config['twilioAuthToken'];
    final fromNumber = _config['twilioFromNumber'];

    if (accountSid == null || authToken == null || fromNumber == null) {
      LoggingService.error('Twilio configuration incomplete');
      return false;
    }

    final credentials = base64Encode(utf8.encode('$accountSid:$authToken'));
    final response = await http.post(
      Uri.parse('https://api.twilio.com/2010-04-01/Accounts/$accountSid/Messages.json'),
      headers: {
        'Authorization': 'Basic $credentials',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: {
        'From': fromNumber,
        'To': phoneNumber,
        'Body': message,
      },
    );

    if (response.statusCode == 201) {
      final responseData = jsonDecode(response.body);
      LoggingService.info('SMS sent via Twilio - SID: ${responseData['sid']}');
      return true;
    } else {
      LoggingService.error('Twilio API error: ${response.statusCode} - ${response.body}');
      return false;
    }
  }

  /// Send SMS via AWS SNS
  Future<bool> _sendViaAWSSNS(String phoneNumber, String message) async {
    // TODO: Implement AWS SNS integration
    LoggingService.info('AWS SNS integration not yet implemented');
    return await _sendViaMock(phoneNumber, message);
  }

  /// Mock SMS sending for development/testing
  Future<bool> _sendViaMock(String phoneNumber, String message) async {
    LoggingService.info(
      'Mock SMS sent - To: $phoneNumber, Message: ${message.substring(0, message.length > 50 ? 50 : message.length)}... (${message.length} chars)',
    );
    
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 150));
    return true;
  }

  /// Validate phone number format
  bool isValidPhoneNumber(String phoneNumber) {
    // Basic E.164 format validation
    final regex = RegExp(r'^\+[1-9]\d{1,14}$');
    return regex.hasMatch(phoneNumber);
  }

  /// Format phone number to E.164 format
  String formatPhoneNumber(String phoneNumber, {String defaultCountryCode = '+1'}) {
    // If already formatted with +, return as-is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }

    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // If it starts with country code, add +
    if (digits.length > 10) {
      return '+$digits';
    }

    // If it's a 10-digit US number, add +1
    if (digits.length == 10) {
      return '$defaultCountryCode$digits';
    }

    // Return as-is if we can't determine format
    return phoneNumber;
  }
}
