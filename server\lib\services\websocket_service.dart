import 'dart:convert';

import 'package:shelf/shelf.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// WebSocket service for real-time gamification features
/// Handles live leaderboard updates, achievement notifications, and activity feeds
class WebSocketService {
  static const String version = '1.0.0';
  
  // Connected clients organized by subscription type
  static final Map<String, Set<WebSocketChannel>> _subscribers = {};
  static final Map<WebSocketChannel, Map<String, dynamic>> _clientInfo = {};
  
  // Performance tracking
  static int _totalConnections = 0;
  static int _activeConnections = 0;
  static final List<String> _connectionHistory = [];
  
  /// Initialize WebSocket service
  static void initialize() {
    print('🔗 Initializing WebSocket Service v$version');
    _initializeSubscriptionTypes();
  }
  
  /// Initialize subscription types
  static void _initializeSubscriptionTypes() {
    final subscriptionTypes = [
      'leaderboard',
      'achievements', 
      'user_activity',
      'global_activity',
      'notifications',
      'system_updates'
    ];
    
    for (final type in subscriptionTypes) {
      _subscribers[type] = <WebSocketChannel>{};
    }
    
    print('📊 Initialized ${subscriptionTypes.length} subscription types');
  }
  
  /// Create WebSocket handler
  static Handler getWebSocketHandler() {
    return webSocketHandler((WebSocketChannel webSocket) {
      _totalConnections++;
      _activeConnections++;
      
      final connectionId = 'ws_${_totalConnections}_${DateTime.now().millisecondsSinceEpoch}';
      _connectionHistory.add('[$connectionId] Connected at ${DateTime.now().toIso8601String()}');
      
      print('🔗 New WebSocket connection: $connectionId (Active: $_activeConnections)');
      
      // Initialize client info
      _clientInfo[webSocket] = {
        'id': connectionId,
        'connected_at': DateTime.now().toIso8601String(),
        'subscriptions': <String>[],
        'user_id': null,
        'last_activity': DateTime.now().toIso8601String(),
      };
      
      // Send welcome message
      _sendMessage(webSocket, {
        'type': 'welcome',
        'connection_id': connectionId,
        'server_version': version,
        'timestamp': DateTime.now().toIso8601String(),
        'available_subscriptions': _subscribers.keys.toList(),
      });
      
      // Handle incoming messages
      webSocket.stream.listen(
        (message) => _handleMessage(webSocket, message),
        onError: (error) => _handleError(webSocket, error),
        onDone: () => _handleDisconnection(webSocket),
      );
    });
  }
  
  /// Handle incoming WebSocket messages
  static void _handleMessage(WebSocketChannel webSocket, dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final messageType = data['type'] as String?;
      
      // Update client activity
      if (_clientInfo.containsKey(webSocket)) {
        _clientInfo[webSocket]!['last_activity'] = DateTime.now().toIso8601String();
      }
      
      switch (messageType) {
        case 'subscribe':
          _handleSubscription(webSocket, data);
          break;
        case 'unsubscribe':
          _handleUnsubscription(webSocket, data);
          break;
        case 'ping':
          _handlePing(webSocket, data);
          break;
        case 'user_auth':
          _handleUserAuth(webSocket, data);
          break;
        case 'get_status':
          _sendStatus(webSocket);
          break;
        default:
          _sendError(webSocket, 'Unknown message type: $messageType');
      }
    } catch (e) {
      _sendError(webSocket, 'Invalid message format: $e');
    }
  }
  
  /// Handle subscription requests
  static void _handleSubscription(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final subscriptionType = data['subscription'] as String?;
    final userId = data['user_id'] as String?;
    
    if (subscriptionType == null || !_subscribers.containsKey(subscriptionType)) {
      _sendError(webSocket, 'Invalid subscription type: $subscriptionType');
      return;
    }
    
    // Add client to subscription
    _subscribers[subscriptionType]!.add(webSocket);
    
    // Update client info
    if (_clientInfo.containsKey(webSocket)) {
      final subscriptions = _clientInfo[webSocket]!['subscriptions'] as List<String>;
      if (!subscriptions.contains(subscriptionType)) {
        subscriptions.add(subscriptionType);
      }
      
      if (userId != null) {
        _clientInfo[webSocket]!['user_id'] = userId;
      }
    }
    
    _sendMessage(webSocket, {
      'type': 'subscription_confirmed',
      'subscription': subscriptionType,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('📡 Client subscribed to: $subscriptionType${userId != null ? ' (User: $userId)' : ''}');
  }
  
  /// Handle unsubscription requests
  static void _handleUnsubscription(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final subscriptionType = data['subscription'] as String?;
    
    if (subscriptionType == null || !_subscribers.containsKey(subscriptionType)) {
      _sendError(webSocket, 'Invalid subscription type: $subscriptionType');
      return;
    }
    
    // Remove client from subscription
    _subscribers[subscriptionType]!.remove(webSocket);
    
    // Update client info
    if (_clientInfo.containsKey(webSocket)) {
      final subscriptions = _clientInfo[webSocket]!['subscriptions'] as List<String>;
      subscriptions.remove(subscriptionType);
    }
    
    _sendMessage(webSocket, {
      'type': 'unsubscription_confirmed',
      'subscription': subscriptionType,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('📡 Client unsubscribed from: $subscriptionType');
  }
  
  /// Handle ping messages (heartbeat)
  static void _handlePing(WebSocketChannel webSocket, Map<String, dynamic> data) {
    _sendMessage(webSocket, {
      'type': 'pong',
      'timestamp': DateTime.now().toIso8601String(),
      'server_time': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  /// Handle user authentication
  static void _handleUserAuth(WebSocketChannel webSocket, Map<String, dynamic> data) {
    final userId = data['user_id'] as String?;
    
    if (userId == null) {
      _sendError(webSocket, 'User ID is required for authentication');
      return;
    }
    
    // Simple auth validation (in real app, validate JWT token)
    if (_clientInfo.containsKey(webSocket)) {
      _clientInfo[webSocket]!['user_id'] = userId;
      _clientInfo[webSocket]!['authenticated'] = true;
    }
    
    _sendMessage(webSocket, {
      'type': 'auth_success',
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('🔐 User authenticated: $userId');
  }
  
  /// Send status information
  static void _sendStatus(WebSocketChannel webSocket) {
    final clientInfo = _clientInfo[webSocket];
    
    _sendMessage(webSocket, {
      'type': 'status',
      'client_info': clientInfo,
      'server_stats': {
        'total_connections': _totalConnections,
        'active_connections': _activeConnections,
        'subscription_counts': _subscribers.map((k, v) => MapEntry(k, v.length)),
      },
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Handle connection errors
  static void _handleError(WebSocketChannel webSocket, dynamic error) {
    print('❌ WebSocket error: $error');
    _cleanupConnection(webSocket);
  }
  
  /// Handle client disconnection
  static void _handleDisconnection(WebSocketChannel webSocket) {
    final clientId = _clientInfo[webSocket]?['id'] ?? 'unknown';
    print('🔌 WebSocket disconnected: $clientId');
    
    _cleanupConnection(webSocket);
  }
  
  /// Clean up connection resources
  static void _cleanupConnection(WebSocketChannel webSocket) {
    // Remove from all subscriptions
    for (final subscribers in _subscribers.values) {
      subscribers.remove(webSocket);
    }
    
    // Remove client info
    _clientInfo.remove(webSocket);
    
    // Update active connections count
    _activeConnections--;
    
    print('🧹 Cleaned up connection. Active connections: $_activeConnections');
  }
  
  /// Broadcast message to all subscribers of a specific type
  static void broadcast(String subscriptionType, Map<String, dynamic> message) {
    if (!_subscribers.containsKey(subscriptionType)) {
      print('⚠️  Unknown subscription type: $subscriptionType');
      return;
    }
    
    final subscribers = _subscribers[subscriptionType]!;
    final activeSubscribers = <WebSocketChannel>[];
    
    message['timestamp'] = DateTime.now().toIso8601String();
    message['subscription_type'] = subscriptionType;
    
    for (final client in subscribers) {
      try {
        _sendMessage(client, message);
        activeSubscribers.add(client);
      } catch (e) {
        print('❌ Failed to send to client: $e');
        // Client will be cleaned up automatically on next message attempt
      }
    }
    
    // Update active subscribers (remove failed ones)
    _subscribers[subscriptionType] = activeSubscribers.toSet();
    
    print('📡 Broadcasted $subscriptionType to ${activeSubscribers.length} clients');
  }
  
  /// Send message to specific client
  static void _sendMessage(WebSocketChannel webSocket, Map<String, dynamic> message) {
    try {
      webSocket.sink.add(jsonEncode(message));
    } catch (e) {
      print('❌ Failed to send message: $e');
      _cleanupConnection(webSocket);
    }
  }
  
  /// Send error message to client
  static void _sendError(WebSocketChannel webSocket, String error) {
    _sendMessage(webSocket, {
      'type': 'error',
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Get service statistics
  static Map<String, dynamic> getStats() {
    return {
      'version': version,
      'total_connections': _totalConnections,
      'active_connections': _activeConnections,
      'subscription_types': _subscribers.keys.toList(),
      'subscription_counts': _subscribers.map((k, v) => MapEntry(k, v.length)),
      'recent_connections': _connectionHistory.take(10).toList(),
    };
  }
  
  /// Broadcast leaderboard update
  static void broadcastLeaderboardUpdate(Map<String, dynamic> leaderboardData) {
    broadcast('leaderboard', {
      'type': 'leaderboard_update',
      'data': leaderboardData,
    });
  }
  
  /// Broadcast achievement unlock
  static void broadcastAchievementUnlock(String userId, Map<String, dynamic> achievement) {
    broadcast('achievements', {
      'type': 'achievement_unlock',
      'user_id': userId,
      'achievement': achievement,
    });
  }
  
  /// Broadcast user activity
  static void broadcastUserActivity(Map<String, dynamic> activity) {
    broadcast('user_activity', {
      'type': 'user_activity',
      'activity': activity,
    });
  }
  
  /// Broadcast global activity feed update
  static void broadcastGlobalActivity(Map<String, dynamic> activity) {
    broadcast('global_activity', {
      'type': 'global_activity',
      'activity': activity,
    });
  }
  
  /// Broadcast system notification
  static void broadcastNotification(Map<String, dynamic> notification) {
    broadcast('notifications', {
      'type': 'notification',
      'notification': notification,
    });
  }
  
  /// Send message to specific user (if connected)
  static void sendToUser(String userId, Map<String, dynamic> message) {
    // For now, broadcast to all clients with user filter
    // A full implementation would track user-specific connections
    broadcast('notifications', {
      'type': 'user_specific',
      'target_user_id': userId,
      'message': message,
    });
  }
  
  /// Broadcast team activity updates
  static void broadcastTeamActivity(Map<String, dynamic> activity) {
    broadcast('global_activity', {
      'type': 'team_activity',
      'activity': activity,
    });
  }
  
  /// Send real-time notification to specific user
  static void sendNotificationToUser(String userId, Map<String, dynamic> notification) {
    sendToUser(userId, {
      'type': 'notification',
      'notification': notification,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    print('🔔 Sent notification to user $userId: ${notification['title']}');
  }
}