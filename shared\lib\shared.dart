/// Comprehensive shared package for Quester platform
/// 
/// Provides shared models, utilities, constants, and configuration
/// for the quest and task management platform with gamification features.
library;

// Core models
export 'src/models/core/user.dart';
export 'src/models/core/quest.dart';
export 'src/models/core/quest_progress.dart';
export 'src/models/core/task.dart';

// Gamification models
export 'src/models/gamification/gamification_models.dart' hide PointTransactionType, PointTransaction, LeaderboardEntry;
export 'src/models/gamification/user_points.dart';
export 'src/models/gamification/leaderboard.dart';
export 'src/models/gamification/reward.dart';
export 'src/models/gamification/streak.dart';

// Enterprise models
export 'src/models/enterprise/organization.dart';
export 'src/models/enterprise/organization_role.dart';
export 'src/models/enterprise/organization_member.dart';
export 'src/models/enterprise/analytics.dart';
export 'src/models/enterprise/sso_configuration.dart';
export 'src/models/enterprise/compliance.dart';
export 'src/models/enterprise/gdpr.dart';
export 'src/models/enterprise/api_management.dart';

// Advanced Analytics models
export 'src/models/analytics/analytics_event.dart';
export 'src/models/analytics/analytics_metrics.dart';
export 'src/models/analytics/user_behavior_analytics.dart';
export 'src/models/analytics/custom_report.dart';
export 'src/models/analytics/analytics_insights.dart';
export 'src/models/analytics/prediction_models.dart';
export 'src/models/analytics/ml_insights.dart';

// Authentication models
export 'src/models/auth/auth_session.dart';
export 'src/models/auth/admin_user.dart';

// Security models  
export 'src/models/security/sso_provider.dart' show SSOProvider, SSOProviderType;
export 'src/models/security/user_sso_identity.dart';
export 'src/models/security/user_mfa_settings.dart';
export 'src/models/security/organization_security_policy.dart';
export 'src/models/security/security_audit_log.dart';
export 'src/models/security/user_session_enhanced.dart';
export 'src/models/security/ip_access_control.dart';
export 'src/models/security/backup_code_models.dart';
export 'src/models/security/mfa_models.dart' hide DeviceType, DeviceRegistrationRequest, DeviceVerificationResult;
export 'src/models/security/trusted_device_models.dart';
// export 'src/models/security/threat_models.dart'; // Hiding to avoid conflicts with threat_detection.dart
export 'src/models/security/threat_detection.dart';

// Real-time models
export 'src/models/realtime/websocket_event.dart';
export 'src/models/realtime/user_presence.dart';
export 'src/models/realtime/collaboration_session.dart';

// Mobile configuration models
export 'src/models/mobile/mobile_config.dart';

// DTOs for API communication
export 'src/dtos/api_response.dart';
export 'src/dtos/auth_dto.dart';
export 'src/dtos/enhanced_auth_dto.dart';
export 'src/dtos/gamification_dto.dart';
export 'src/dtos/quest_dto.dart';
export 'src/dtos/task_dto.dart';

// Constants
export 'src/constants/api_endpoints.dart';
export 'src/constants/app_constants.dart';
export 'src/constants/gamification_constants.dart';
export 'src/constants/validation_constants.dart';

// Utilities
export 'src/utils/date_utils.dart';
export 'src/utils/security_policy_validator.dart' hide SecurityValidationResult;
export 'src/utils/string_extensions.dart';
export 'src/utils/validation_utils.dart' hide ValidationResult;

// Configuration
export 'src/config/environment_config.dart';
