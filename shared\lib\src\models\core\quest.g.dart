// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Quest _$QuestFromJson(Map<String, dynamic> json) => Quest(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  createdById: json['createdById'] as String,
  assignedToId: json['assignedToId'] as String?,
  status: $enumDecode(_$QuestStatusEnumMap, json['status']),
  priority: $enumDecode(_$QuestPriorityEnumMap, json['priority']),
  difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
  category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
  basePoints: (json['basePoints'] as num).toInt(),
  bonusPoints: (json['bonusPoints'] as num).toInt(),
  totalPoints: (json['totalPoints'] as num).toInt(),
  earnedPoints: (json['earnedPoints'] as num).toInt(),
  deadline: json['deadline'] == null
      ? null
      : DateTime.parse(json['deadline'] as String),
  estimatedHours: (json['estimatedHours'] as num?)?.toInt(),
  actualHours: (json['actualHours'] as num?)?.toInt(),
  progressPercentage: (json['progressPercentage'] as num).toDouble(),
  taskIds: (json['taskIds'] as List<dynamic>).map((e) => e as String).toList(),
  participantIds: (json['participantIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  startedAt: json['startedAt'] == null
      ? null
      : DateTime.parse(json['startedAt'] as String),
);

Map<String, dynamic> _$QuestToJson(Quest instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'createdById': instance.createdById,
  'assignedToId': instance.assignedToId,
  'status': _$QuestStatusEnumMap[instance.status]!,
  'priority': _$QuestPriorityEnumMap[instance.priority]!,
  'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
  'category': _$QuestCategoryEnumMap[instance.category]!,
  'basePoints': instance.basePoints,
  'bonusPoints': instance.bonusPoints,
  'totalPoints': instance.totalPoints,
  'earnedPoints': instance.earnedPoints,
  'deadline': instance.deadline?.toIso8601String(),
  'estimatedHours': instance.estimatedHours,
  'actualHours': instance.actualHours,
  'progressPercentage': instance.progressPercentage,
  'taskIds': instance.taskIds,
  'participantIds': instance.participantIds,
  'tags': instance.tags,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
  'startedAt': instance.startedAt?.toIso8601String(),
};

const _$QuestStatusEnumMap = {
  QuestStatus.draft: 'draft',
  QuestStatus.active: 'active',
  QuestStatus.inProgress: 'in_progress',
  QuestStatus.completed: 'completed',
  QuestStatus.archived: 'archived',
  QuestStatus.cancelled: 'cancelled',
};

const _$QuestPriorityEnumMap = {
  QuestPriority.low: 'low',
  QuestPriority.medium: 'medium',
  QuestPriority.high: 'high',
  QuestPriority.urgent: 'urgent',
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.beginner: 'beginner',
  QuestDifficulty.intermediate: 'intermediate',
  QuestDifficulty.advanced: 'advanced',
  QuestDifficulty.expert: 'expert',
  QuestDifficulty.master: 'master',
};

const _$QuestCategoryEnumMap = {
  QuestCategory.personal: 'personal',
  QuestCategory.work: 'work',
  QuestCategory.learning: 'learning',
  QuestCategory.health: 'health',
  QuestCategory.social: 'social',
  QuestCategory.creative: 'creative',
  QuestCategory.productivity: 'productivity',
  QuestCategory.other: 'other',
};
